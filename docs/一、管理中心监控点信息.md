一、管理中心监控点信息

1、精准预警界面的预警分布模块

请求地址：

```
/system/CameraMonitorPoint/list
```

请求参数：

```
无
```
响应实体类：

```
// 监控点编号(UUID)
    private String cameraindexcode;
    // 创建时间(ISO8601格式)
    private String createtime;
    // 图标编号(1-255位)
    private String gbindexcode;
    // 纬度(WGS84)
    private String latitude;
    // 经度(WGS84)
    private String longitude;
    // 管理中心名称
    private String mgtcentername;
    // 监控点名称(1-255位)
    private String name;
    // 桩号
    private String stakenum;
    // 在线状态(0-离线 1-在线)
    private String status;
    // 更新时间(ISO8601格式)
    private String updatetime;
```

响应参数：

```
{
    "code": 200,
    "msg": "操作成功",
    "data": [
        {
            "cameraindexcode": "20021808001310241843",
            "createtime": "2024-11-28T10:26:26.000+08:00",
            "gbindexcode": "20021808001310241843",
            "latitude": "",
            "longitude": "",
            "mgtcentername": "舟山管理中心",
            "name": "舟山进口球机",
            "stakenum": "",
            "status": "1",
            "updatetime": "2025-07-18T05:04:33.052+08:00"
        },
        {
            "cameraindexcode": "20021808001310291411",
            "createtime": "2022-11-04T13:45:59.000+08:00",
            "gbindexcode": "20021808001310291411",
            "latitude": "",
            "longitude": "",
            "mgtcentername": "舟山管理中心",
            "name": "舟山站 入01内",
            "stakenum": "",
            "status": "1",
            "updatetime": "2025-07-18T05:04:32.960+08:00"
        }
    ]
}
```

二、收费断面流量分时数据

1、获取通行流量统计数据（通行流量、车流趋势、流量统计）

请求地址：

```
/system/TrafficFlow/stats
```

请求参数：

```
system/TrafficFlow/stats?type=3
统计类型：1-当天（3小时一段），2-当月（按天），3-当年（按月）
```

响应示例：

```
{
    "code": 200,
    "msg": "操作成功",
    "data": {
        "01月": 0,
        "02月": 0,
        "03月": 0,
        "04月": 0,
        "05月": 0,
        "06月": 72604,
        "07月": 0,
        "08月": 0,
        "09月": 0,
        "10月": 0,
        "11月": 0,
        "12月": 0
    }
}
```

2、拥堵对比、拥堵指数（获取各个方向的流量统计）

请求地址：

```
/system/TrafficFlow/directionStats
```

请求参数：

```
无
```

响应示例：

```
{
    "code": 200,
    "msg": "操作成功",
    "data": {
        "舟向": 0,
        "宁向": 72604,
        "totalCount": 72604,
        "舟向占比": "0.00%",
        "宁向占比": "100.00%"
    }
}
```

3、运行分析

请求地址：

```
/system/TrafficFlow/latestByHub
```

请求参数：

```
无
```

响应实体类：

```
	private Long id;          // 自增主键
    private String direction; // 方向中文名
    private Integer directionCode; // 方向代码
    private String ds;        // 数据分区字段
    private String gantryId;  // 门架编号
    private Long hourBatchNo; // 小时
    private String hubCode01; // 断面起点
    private String hubCode02; // 断面终点
    private String regionName; // 管理中心名称
    private Integer sectionNo; // 断面序号
    private String stationCode; // 站码
    private Integer tradeDate; // 数据日期
    private Long vehicleCls;  // 断面流量
    private Integer yzCode;   // 业主编码
```

响应示例：

```
{
    "code": 200,
    "msg": "操作成功",
    "data": [
        {
            "id": "1946101380402507778",
            "direction": "宁向",
            "directionCode": 1,
            "ds": "2025061221",
            "gantryId": "G92113300100072",
            "hourBatchNo": 2025061221,
            "hubCode01": "金塘",
            "hubCode02": "沥港",
            "regionName": "舟山管理中心",
            "sectionNo": 3,
            "stationCode": "2921",
            "tradeDate": 20250612,
            "vehicleCls": 37796,
            "yzCode": 2901
        },
        {
            "id": "1946101380633194498",
            "direction": "宁向",
            "directionCode": 1,
            "ds": "2025061221",
            "gantryId": "G92113300100082",
            "hourBatchNo": 2025061221,
            "hubCode01": "沥港",
            "hubCode02": "滨海枢纽（杭甬复线甬舟）",
            "regionName": "舟山管理中心",
            "sectionNo": 4,
            "stationCode": "2925",
            "tradeDate": 20250612,
            "vehicleCls": 34808,
            "yzCode": 2901
        }
    ]
}
```

4、历史流量

请求地址：

```
/system/TrafficFlow/hubStat
```

请求参数：

```
无
```

响应示例：

```
{
    "code": 200,
    "msg": "操作成功",
    "data": [
        {
            "hubCode01": "金塘",
            "totalVehicleCls": 37796
        },
        {
            "hubCode01": "沥港",
            "totalVehicleCls": 34808
        }
    ]
}已将排序了
```

