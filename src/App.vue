<template>
  <el-config-provider :locale="appStore.locale" :size="appStore.size">
    <div id="app" ref="appRef" :class="{ 'fullscreen-mode': screenAdapter.isFullscreen?.value }">
      <template v-if="!isLoginPage">
        <div class="screen-container" :style="scaleStyle">
          <div class="title">
            <div class="tit-left">
              <div class="img"></div>
              <div class="bt">
                <span>舟山跨海大桥综合监管指挥平台</span>
                <span>Zhoushan Cross-Sea Bridge Integrated Supervision and Command Platform</span>
              </div>
            </div>
            <div class="tit-right">
              <div class="img"></div>
              <div class="list">
                <span>{{ currentDate }}</span>
                <span>33℃<i>西北风2级</i></span>
              </div>
            </div>
          </div>
          <div class="content">
            <Navigation />
            <MapRight />
            <Bridge />
            <FlowRate />
            <router-view />
            <MapComponent />
          </div>
          <!-- 悬浮窗管理器 -->
          <FloatingWindowManager />
          <!-- 网络状态指示器 -->
          <NetworkStatus />
        </div>
      </template>
      <template v-else>
        <router-view />
      </template>
    </div>
  </el-config-provider>
</template>

<script setup lang="ts">
import { onMounted, nextTick, computed, ref, onBeforeUnmount } from 'vue';
import { useRoute } from 'vue-router';
import useSettingsStore from '@/store/modules/settings';
import { handleThemeStyle } from '@/utils/theme';
import useAppStore from '@/store/modules/app';
import MapComponent from '@/components/MapComponent.vue';
import Navigation from '@/components/Navigation/index.vue';
import MapRight from '@/components/MapRight/index.vue';
import Bridge from '@/components/Bridge/index.vue';
import FlowRate from '@/components/FlowRate/index.vue';
import FloatingWindowManager from '@/components/FloatingWindowManager/index.vue';
import NetworkStatus from '@/components/NetworkStatus/index.vue';
import { getMapInstance } from '@/utils/mapMethods';
import { useScreenAdapter, ScreenType } from '@/utils/screenAdapter';

const appStore = useAppStore();
const route = useRoute();
const appRef = ref<HTMLElement | null>(null);

// 实时日期相关
const currentDate = ref('');
let updateDateInterval: ReturnType<typeof setInterval>;

// 格式化日期函数
const formatDate = (date: Date): string => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

// 初始化屏幕适配工具
const screenAdapter = useScreenAdapter({
  debug: false // 可以根据需要开启调试模式
});

// 设计稿尺寸
const designWidth = 4800;
const designHeight = 1620;

// 窗口尺寸
const windowSize = ref({
  width: document.documentElement.clientWidth,
  height: document.documentElement.clientHeight
});

// 缩放比例
const scale = ref(1);

// 计算缩放样式
const scaleStyle = computed(() => {
  const { width, height } = windowSize.value;

  // 检查全屏状态
  const isFullscreen = screenAdapter.isFullscreen?.value || false;

  // 全屏模式：完全移除固定尺寸约束，使用响应式布局
  if (isFullscreen) {
    console.log('[ScreenAdapter] 全屏模式已激活 - 移除固定尺寸约束，启用响应式布局');
    return {
      transform: 'none',
      transformOrigin: 'left top',
      width: '100vw',
      height: '100vh',
      position: 'fixed',
      top: '0',
      left: '0',
      zIndex: '1',
      overflow: 'hidden',
      // 关键：不设置固定像素尺寸，让内容自然流动
      maxWidth: 'none',
      maxHeight: 'none'
    };
  }

  // 正常模式：保持原有缩放逻辑（显示黑色边框）
  // 计算缩放比例
  const widthScale = width / designWidth;
  const heightScale = height / designHeight;
  scale.value = Math.min(widthScale, heightScale);

  // 计算居中偏移
  const translateX = (width - designWidth * scale.value) / 2;
  const translateY = (height - designHeight * scale.value) / 2;

  return {
    transform: `translate(${translateX}px, ${translateY}px) scale(${scale.value})`,
    transformOrigin: 'left top',
    width: `${designWidth}px`,
    height: `${designHeight}px`,
    position: 'relative'
  };
});

// 监听窗口大小变化
const handleResize = () => {
  windowSize.value = {
    width: document.documentElement.clientWidth,
    height: document.documentElement.clientHeight
  };
  const map = getMapInstance();
  if (map) {
    map.updateSize();
  }
};

onMounted(async () => {
  try {
    // 等待DOM完全准备就绪
    await nextTick();

    console.log('[App] 开始初始化屏幕适配系统');

    // 初始化屏幕适配（现在是异步的）
    await screenAdapter.initScreenAdapter();

    // 监听屏幕类型变化并自动应用CSS类
    screenAdapter.watchScreenTypeChange();

    // 监听全屏状态变化，确保样式及时更新并添加平滑过渡
    if (screenAdapter.isFullscreen) {
      // 使用watch监听全屏状态变化
      const { watch } = await import('vue');
      watch(
        screenAdapter.isFullscreen,
        (newValue, oldValue) => {
          if (newValue !== oldValue) {
            console.log(`[App] 全屏状态变化: ${oldValue} → ${newValue}`);

            // 添加过渡类名，提供平滑的切换效果
            document.body.classList.add('fullscreen-transitioning');

            // 触发窗口大小重新计算，确保样式更新
            nextTick(() => {
              handleResize();

              // 延迟移除过渡类名，确保过渡动画完成
              setTimeout(() => {
                document.body.classList.remove('fullscreen-transitioning');
                console.log('[App] 全屏切换过渡动画完成');
              }, 300); // 300ms过渡时间
            });
          }
        },
        { immediate: false }
      );
    }

    // 打印初始状态
    if (screenAdapter.config.debug) {
      console.log('[App] 屏幕适配初始状态:');
      screenAdapter.printAdapterStatus();
    }

    // 初始化主题样式
    handleThemeStyle(useSettingsStore().theme);

    // 添加窗口大小变化监听
    window.addEventListener('resize', handleResize);

    // 在开发环境下，将屏幕适配器暴露到全局，方便调试
    if (import.meta.env.DEV) {
      (window as any).screenAdapter = screenAdapter;
      console.log('[App] 屏幕适配器已暴露到全局 window.screenAdapter，可在控制台调用以下方法:');
      console.log('- window.screenAdapter.printAdapterStatus() // 打印当前状态');
      console.log('- window.screenAdapter.getAdapterStatus() // 获取状态对象');
    }

    console.log('[App] 屏幕适配系统初始化完成');
  } catch (error) {
    console.error('[App] 屏幕适配系统初始化失败:', error);
  }
  // 立即更新一次
  currentDate.value = formatDate(new Date());
  // 设置定时器，每秒更新一次
  updateDateInterval = setInterval(() => {
    currentDate.value = formatDate(new Date());
  }, 1000);
});

onBeforeUnmount(() => {
  // 清理屏幕适配
  screenAdapter.cleanupScreenAdapter();

  // 清理窗口大小变化监听
  window.removeEventListener('resize', handleResize);

  //清楚时间定时器
  clearInterval(updateDateInterval);
});

// 判断是否是登录页
const isLoginPage = computed(() => {
  return route.path === '/login';
});
</script>

<style scoped lang="scss">
#app {
  width: 100vw;
  height: 100vh;
  background: #000000;
  overflow: hidden;
  position: relative;

  .screen-container {
    position: absolute;
    left: 0;
    top: 0;
    transform-origin: left top;
    // 添加平滑过渡效果，提升全屏切换体验
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .title {
    height: 158px;
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .tit-left {
      display: flex;
      margin-left: 66px;
      .img {
        width: 103px;
        height: 103px;
        background: url('@/assets/images/logo.png') no-repeat;
        background-size: 100%;
        margin-right: 20px;
      }
      .bt {
        color: #fff;
        display: flex;
        flex-direction: column;
        span:nth-child(1) {
          font-size: 57px !important;
          font-family: 'Title3' !important;
          color: #fff !important;
        }
        span:nth-child(2) {
          font-size: 18px !important;
          color: #7c7c86 !important;
          font-family: 'Microsoft YaHei Light' !important;
        }
      }
    }
    .tit-right {
      display: flex;
      margin-right: 71px;
      .img {
        width: 70px;
        height: 70px;
        background: url('@/assets/images/tq.png') no-repeat;
        background-size: 100%;
        margin-right: 15px;
      }
      .list {
        color: #fff;
        font-family: 'Microsoft YaHei Light';
        display: flex;
        flex-direction: column;
        span:nth-child(1) {
          font-size: 24px;
          display: flex;
          flex-wrap: nowrap;
        }
        span:nth-child(2) {
          font-size: 41px;
          i {
            font-style: normal;
            margin-left: 12px;
            font-size: 24px;
          }
        }
      }
    }
  }
  .content {
    width: 100%;
    height: calc(100% - 158px);
    position: relative;
  }

  // 全屏模式特殊样式
  &.fullscreen-mode {
    .screen-container {
      // 使用flex布局替代固定尺寸，实现真正的响应式布局
      display: flex !important;
      flex-direction: column !important;
      width: 100vw !important;
      height: 100vh !important;

      .title {
        // 使用相对高度替代固定158px，确保响应式适配
        height: 10vh; // 在1620px高度下约等于162px，接近原始158px
        min-height: 120px; // 确保最小高度，防止过小屏幕下标题被压缩
        max-height: 200px; // 确保最大高度，防止过大屏幕下标题过高
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-shrink: 0; // 防止标题被压缩
      }

      .content {
        // 使用flex: 1占满剩余空间，实现真正的响应式布局
        flex: 1;
        width: 100%;
        overflow: hidden; // 防止内容溢出
        position: relative;
      }
    }
  }
}
</style>
