import request from '@/utils/request';

/**
 * 获取桥梁列表
 * @param data - 请求参数，包含筛选条件等信息
 * @returns 返回一个 Promise，该 Promise 解析为包含桥梁列表的响应数据
 */
export function getBridgeList(data: any) {
  // 发起请求，请求的 URL 为 /system/bridge/list，请求方法为 GET，请求参数为 data
  return request({
    url: '/system/bridgeArchive/list',
    method: 'GET',
    params: data
  });
}

/**
 * 获取桥梁详情
 * @param id - 桥梁 ID
 * @returns 返回一个 Promise，该 Promise 解析为包含桥梁详情的响应数据
 */
export function getBridgeById(id: any) {
  return request({
    url: '/system/bridgeArchive/' + id,
    method: 'GET'
  });
}

/**
 * 添加桥梁
 * @param data - 请求参数，包含桥梁信息
 * @returns 返回一个 Promise，该 Promise 解析为包含添加桥梁响应数据的响应
 */
export function addBridge(data: any) {
  return request({
    url: '/system/bridgeArchive',
    method: 'POST',
    data: data
  });
}

/**
 * 更新桥梁
 * @param data - 请求参数，包含桥梁信息
 * @returns 返回一个 Promise，该 Promise 解析为包含更新桥梁响应数据的响应
 */
export function updateBridge(data: any) {
  return request({
    url: '/system/bridgeArchive',
    method: 'PUT',
    data: data
  });
}

/**
 * 删除桥梁
 * @param id - 桥梁 ID
 * @returns 返回一个 Promise，该 Promise 解析为包含删除桥梁响应数据的响应
 */
export function deleteBridge(id: any) {
  return request({
    url: '/system/bridgeArchive/' + id,
    method: 'DELETE'
  });
}

/**
 * 获取桥梁分析报告列表
 * @returns 返回一个 Promise，该 Promise 解析为包含桥梁分析报告列表的响应数据
 */
export function getBridgeHealthReportList(data: any) {
  return request({
    url: '/system/bridgeHealthReport/list',
    method: 'GET',
    params: data
  });
}

/**
 * 获取桥梁健康报告详情
 * @param id - 桥梁健康报告 ID
 * @returns 返回一个 Promise，该 Promise 解析为包含桥梁健康报告详情的响应数据
 */
export function getBridgeHealthReportById(id: any) {
  return request({
    url: '/system/bridgeHealthReport/' + id,
    method: 'GET'
  });
}

/**
 * 添加桥梁健康报告
 * @param data - 请求参数，包含桥梁健康报告信息
 * @returns 返回一个 Promise，该 Promise 解析为包含添加桥梁健康报告响应数据的响应
 */
export function addBridgeHealthReport(data: any) {
  return request({
    url: '/system/bridgeHealthReport',
    method: 'POST',
    data: data
  });
}

/**
 * 更新桥梁健康报告
 * @param data - 请求参数，包含桥梁健康报告信息
 * @returns 返回一个 Promise，该 Promise 解析为包含更新桥梁健康报告响应数据的响应
 */
export function updateBridgeHealthReport(data: any) {
  return request({
    url: '/system/bridgeHealthReport',
    method: 'PUT',
    data: data
  });
}

/**
 * 删除桥梁健康报告
 * @param id - 桥梁健康报告 ID
 * @returns 返回一个 Promise，该 Promise 解析为包含删除桥梁健康报告响应数据的响应
 */
export function deleteBridgeHealthReport(id: any) {
  return request({
    url: '/system/bridgeHealthReport/' + id,
    method: 'DELETE'
  });
}

/**
 * 获取桥梁保养周期 
 * @returns 返回一个 Promise，该 Promise 解析为包含桥梁保养周期响应数据的响应
 */
export function getBridgeMaintenanceSchedule(data: any) {
  return request({
    url: '/system/bridgeMaintenanceSchedule/list',
    method: 'GET',
    params: data
  });
}

/**
 * 获取桥梁保养周期详情
 * @param id - 桥梁保养周期 ID
 * @returns 返回一个 Promise，该 Promise 解析为包含桥梁保养周期详情的响应数据
 */
export function getBridgeMaintenanceScheduleById(id: any) {
  return request({
    url: '/system/bridgeMaintenanceSchedule/' + id,
    method: 'GET'
  });
}

/**
 * 添加桥梁保养周期
 * @param data - 请求参数，包含桥梁保养周期信息
 * @returns 返回一个 Promise，该 Promise 解析为包含添加桥梁保养周期响应数据的响应
 */
export function addBridgeMaintenanceSchedule(data: any) {   
  return request({
    url: '/system/bridgeMaintenanceSchedule',
    method: 'POST',
    data: data
  });
}

/**
 * 更新桥梁保养周期
 * @param data - 请求参数，包含桥梁保养周期信息
 * @returns 返回一个 Promise，该 Promise 解析为包含更新桥梁保养周期响应数据的响应
 */
export function updateBridgeMaintenanceSchedule(data: any) {
  return request({
    url: '/system/bridgeMaintenanceSchedule',
    method: 'PUT',
    data: data
  });
}

/**
 * 删除桥梁保养周期
 * @param id - 桥梁保养周期 ID
 * @returns 返回一个 Promise，该 Promise 解析为包含删除桥梁保养周期响应数据的响应
 */
export function deleteBridgeMaintenanceSchedule(id: any) {  
  return request({
    url: '/system/bridgeMaintenanceSchedule/' + id,
    method: 'DELETE'
  });
}



/**
 * 获取 保养记录
 * @returns 返回一个 Promise，该 Promise 解析为包含桥梁保养记录响应数据的响应
 */
export function getBridgeMaintenanceRecord(data: any) {
  return request({
    url: '/system/bridgeMaintenanceRecord/list',
    method: 'GET',
    params: data
  });
}

/**
 * 获取 保养记录详情
 * @param id - 保养记录 ID
 * @returns 返回一个 Promise，该 Promise 解析为包含保养记录详情响应数据的响应
 */
export function getBridgeMaintenanceRecordById(id: any) {
  return request({
    url: '/system/bridgeMaintenanceRecord/' + id,
    method: 'GET'
  });
}

/**
 * 添加 保养记录
 * @param data - 请求参数，包含保养记录信息
 * @returns 返回一个 Promise，该 Promise 解析为包含添加保养记录响应数据的响应
 */
export function addBridgeMaintenanceRecord(data: any) {
  return request({
    url: '/system/bridgeMaintenanceRecord',
    method: 'POST',
    data: data
  });
}

/**
 * 更新 保养记录
 * @param data - 请求参数，包含保养记录信息
 * @returns 返回一个 Promise，该 Promise 解析为包含更新保养记录响应数据的响应
 */
export function updateBridgeMaintenanceRecord(data: any) {
  return request({
    url: '/system/bridgeMaintenanceRecord',
    method: 'PUT',
    data: data
  });
}

/**
 * 删除 保养记录
 * @param id - 保养记录 ID
 * @returns 返回一个 Promise，该 Promise 解析为包含删除保养记录响应数据的响应
 */
export function deleteBridgeMaintenanceRecord(id: any) {
  return request({
    url: '/system/bridgeMaintenanceRecord/' + id,
    method: 'DELETE'
  });
}






