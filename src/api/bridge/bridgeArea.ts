import request from '@/utils/request';

/**
 * 获取处置记录数据
 * @param data - 请求参数，包含筛选条件等信息
 * @returns 返回一个 Promise
 */
export function getFlowList(data: any) {
  return request({
    url: '/system/screen/wading/process/flow/list',
    method: 'GET',
    params: data
  });
}

/**
 * 获取船舶同行流量
 * @param data - 请求参数，包含筛选条件等信息
 * @returns 返回一个 Promise
 */
export function getFlowShip(data: any) {
  return request({
    url: '/system/screen/prefabricated/ship/flow/stats',
    method: 'GET',
    params: data
  });
}

/**
 * 获取电子围栏预警数据
 * @param data - 请求参数，包含筛选条件等信息
 * @returns 返回一个 Promise
 */
export function getWarningList(data: any) {
  return request({
    url: '/system/screen/wading/warning/list',
    method: 'GET',
    params: data
  });
}

/**
 * 获取在桥船舶分析数据
 * @param data - 请求参数，包含筛选条件等信息
 * @returns 返回一个 Promise
 */
export function getBridgeShip() {
  return request({
    url: '/system/screen/wading/bridgeShip/stats',
    method: 'GET',
  });
}
