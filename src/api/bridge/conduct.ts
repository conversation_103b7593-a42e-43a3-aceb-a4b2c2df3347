import request from '@/utils/request';

/**
 * 获取事件列表数据
 * @param data - 请求参数，包含筛选条件等信息
 * @returns 返回一个 Promise
 */
export function getEventsList(data: any) {
  return request({
    url: '/system/screen/synergy/events',
    method: 'GET',
    params: data
  });
}

/**
 * 获取预案匹配
 * @param data - 请求参数，包含筛选条件等信息
 * @returns 返回一个 Promise
 */
export function getEventsByPlan(data: any) {
  return request({
    url: '/system/screen/synergy/eventsByPlan',
    method: 'GET',
    params: data
  });
}

/**
 * 获取险情同步数据
 * @param eventId - 事件id请求参数，包含筛选条件等信息
 * @returns 返回一个 Promise
 */
export function getNotifyList(eventId: any) {
  return request({
    url: `/system/screen/synergy/warn/notify/list?eventId=${eventId}`,
    method: 'GET',
  });
}

/**
 * 获取险情同步表格数据
 * @param data - 请求参数，包含筛选条件等信息
 * @returns 返回一个 Promise
 */
export function getNotifyPage(data: any) {
  return request({
    url: '/system/screen/synergy/warn/notify/page',
    method: 'GET',
    params: data
  });
}

/**
 * 根据事件id获取现场处置数据
 * @param  eventId- 事件id
 * @returns 返回一个 Promise
 */
export function getLogsId(data: any) {
  return request({
    url: `/system/screen/synergy/process/flow/logs`,
    method: 'GET',
    params:data
  });
}

/**
 * 物资点研-应急物资数据
 * @param  - 
 * @returns 返回一个 Promise
 */
export function getSupplies() {
  return request({
    url: `/system/screen/synergy/supplies`,
    method: 'GET',
  });
}

/**
 * 物资点研-应急知识
 * @param  - 
 * @returns 返回一个 Promise
 */
export function getKnowledge() {
  return request({
    url: `/system/screen/synergy/knowledge/list`,
    method: 'GET',
  });
}

/**
 * 物资点研-救援队伍
 * @param  - 
 * @returns 返回一个 Promise
 */
export function getTeams() {
  return request({
    url: `/system/screen/synergy/rescue/teams`,
    method: 'GET',
  });
}

/**
 * 处置评估
 * @param  eventId- 事件id
 * @returns 返回一个 Promise
 */
export function getEvaluation(data:any) {
  return request({
    url: `/system/screen/synergy/evaluation/detail`,
    method: 'GET',
    params:data
  });
}

/**
 * 人员列表
 * @param  
 * @returns 返回一个 Promise
 */
export function getNoticeTree(data:any) {
  return request({
    url: `/system/screen/synergy/plan/notice/tree`,
    method: 'GET',
    params: data
  });
}

/**
 * 修改预案匹配
 * @param  
 * @returns 返回一个 Promise
 */
export function getEventPlan(data:any) {
  return request({
    url: `/system/screen/synergy/event/plan`,
    method: 'PUT',
    params: data
  });
}

