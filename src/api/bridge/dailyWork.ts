import request from '@/utils/request';

/**
 * 获取警戒力量数据
 * @param data - 请求参数，包含筛选条件等信息
 * @returns 返回一个 Promise
 */
export function getWhitelist(data: any) {
  return request({
    url: '/system/screen/routine/whitelist',
    method: 'GET',
    params: data
  });
}

/**
 * 获取任务情况数据
 * @param data - 请求参数，包含筛选条件等信息
 * @returns 返回一个 Promise
 */
export function getStatistics(data: any) {
  return request({
    url: '/system/screen/routine/statistics',
    method: 'GET',
    params: data
  });
}
