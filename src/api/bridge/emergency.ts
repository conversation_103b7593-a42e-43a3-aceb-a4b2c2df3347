import request from '@/utils/request';

/**
 * 叮消息叮电话
 * @param data - 请求参数，包含筛选条件等信息
 * @returns 返回一个 Promise
 */
export function postNotify(data: any) {
  return request({
    url: '/system/screen/synergy/notify',
    method: 'post',
    params: data
  });
}

/**
 * 获取处置记录数据
 * @param data - 请求参数，包含筛选条件等信息
 * @returns 返回一个 Promise
 */
export function getFlowList(data: any) {
  return request({
    url: '/system/screen/wading/process/flow/list',
    method: 'GET',
    params: data
  });
}

/**
 * 根据预案id查询人员数
 * @param data - 请求参数，包含筛选条件等信息
 * @returns 返回一个 Promise
 */
export function getPersonnelNames(data: any) {
  return request({
    url: '/system/screen/synergy/plan/personnelNames',
    method: 'GET',
    params: data
  });
}
