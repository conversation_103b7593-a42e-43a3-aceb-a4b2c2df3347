import request from '@/utils/request';

/**
 * 获取预警类型
 * @param data - 请求参数，包含筛选条件等信息
 * @returns 返回一个 Promise
 */
export function getCountByTitle(data: any) {
  return request({
    url: '/system/screen/forewarn/countByTitle',
    method: 'GET',
    params: data
  });
}

/**
 * 获取预警统计
 * @param data - 请求参数，包含筛选条件等信息
 * @returns 返回一个 Promise
 */
export function getLast24Hours(data: any) {
  return request({
    url: '/system/screen/forewarn/last24Hours',
    method: 'GET',
    params: data
  });
}


/**
 * 获取预警统计列表
 * @param data - 请求参数，包含筛选条件等信息
 * @returns 返回一个 Promise
 */
export function getForewarnCount(data: any) {
  return request({
    url: '/system/screen/forewarn/location/count',
    method: 'GET',
    params: data
  });
}

/**
 * 获取事件列表
 * @param data - 请求参数，包含筛选条件等信息
 * @returns 返回一个 Promise
 */
export function getSpecialEvents(data: any) {
  return request({
    url: '/system/TrafficEvent/page',
    method: 'GET',
    params: data
  });
}

/**
 * 获取预警分布
 * @param data - 请求参数，包含筛选条件等信息
 * @returns 返回一个 Promise
 */
export function getBridgeMonitor(data: any) {
  return request({
    url: `/system/screen/prefabricated/bridgeMonitor?bridgeName=${data.bridgeName}&time=${data.time}`,
    method: 'GET',
  });
}

/**
 * 获取预警类型列表
 * @param data - 请求参数，包含筛选条件等信息
 * @returns 返回一个 Promise
 */
export function getBridgeWarning(data: any) {
  return request({
    url: `/system/screen/forewarn/warning/bridge`,
    method: 'GET',
    params: data
  });
}
