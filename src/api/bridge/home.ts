import request from '@/utils/request';

/**
 * 获取桥梁个数
 * @param data - 请求参数，包含筛选条件等信息
 * @returns 返回一个 Promise
 */
export function getBridgeSum(data: any) {
  return request({
    url: '/system/screen/bridge/statistics',
    method: 'GET',
    params: data
  });
}

/**
 * 获取桥梁历程
 * @param data - 请求参数，包含筛选条件等信息
 * @returns 返回一个 Promise
 */
export function getBridgeScale(data: any) {
  return request({
    url: '/system/screen/bridge/scale',
    method: 'GET',
    params: data
  });
}

/**
 * 获取桥梁数据
 * @param data - 请求参数，包含筛选条件等信息
 * @returns 返回一个 Promise
 */
export function getBridgeLengths(data: any) {
  return request({
    url: '/system/screen/bridge/lengths',
    method: 'GET',
    params: data
  });
}

/**
 * 按照桥梁状态桥梁基本数据
 * @param data - 分页参数 包含筛选条件等信息 状态 0已开通 1建设中 2规划中
 * @returns 返回一个 Promise
 */
export function getBridgeState(data: any) {
  return request({
    url: '/system/screen/bridge/state/page',
    method: 'GET',
    params:data
  });
}

/**
 * 根据桥的id以及name查询健康监测以及历史事件数据
 * @param data - 请求参数，包含id和bridgeName
 * @returns 返回一个 Promise
 */
export function getBridgeIdHealth(data: any) {
  return request({
    url: `/system/screen/bridge/healthAlarm`,
    method: 'GET',
    params: data
  });
}


/**
 * 同行流量
 * @param data - 请求参数，包含筛选条件等信息 type: 1-船 2-车 time: 1-日  3-月 4-年
 * @returns 返回一个 Promise
 */
export function getTrafficFlow(data: any) {
  return request({
    url: `/system/screen/prefabricated/trafficFlow?type=${data.type}&time=${data.time}`,
    method: 'GET',
  });
}

/**
 * 流量字典
 * @param
 * @returns 返回一个 Promise
 */
export function getFlowDict() {
  return request({
    url: `/system/dict/data/list?pageNum=1&pageSize=1000&dictType=car_hot`,
    method: 'GET',
  });
}

/**
 * 根据桥梁id查询安全设备数据
 * @param
 * @returns 返回一个 Promise
 */
export function getBridgeSafetyEquip(id) {
  return request({
    url: `/system/bridgeSafetyEquip/bridge/${id}`,
    method: 'GET',
  });
}

/**
 * 根据桥梁id查询桥梁运维
 * @param
 * @returns 返回一个 Promise
 */
export function getListByBridgeId(id) {
  return request({
    url: `/system/bridgeMaintain/listByBridgeId/${id}`,
    method: 'GET',
  });
}

/**
 * 根据桥梁id查询工程车辆
 * @param
 * @returns 返回一个 Promise
 */
export function getTrafficEngineeringVehicle(data) {
  return request({
    url: `/system/trafficEngineeringVehicle/bridge`,
    method: 'GET',
    params:data
  });
}

/**
 * 根据桥梁id查询执法机构
 * @param
 * @returns 返回一个 Promise
 */
export function getWithDeptId(data) {
  return request({
    url: `/system/screen/bridge/withDeptId`,
    method: 'GET',
    params:data
  });
}

/**
 * 查询设备管理
 * @param type 1AIS基站 2视频监控 3隧道
 * @returns 返回一个 Promise
 */
export function getByType(data) {
  return request({
    url: `/system/deviceInfo/getByType`,
    method: 'GET',
    params:data
  });
}

/**
 * 查询卡口设备
 * @param
 * @returns 返回一个 Promise
 */
export function getCheckpointDevice(data) {
  return request({
    url: `/system/checkpointDevice/list`,
    method: 'GET',
    params:data
  });
}

/**
 * 查询感知设备
 * @param
 * @returns 返回一个 Promise
 */
export function getSensingData(data) {
  return request({
    url: `/system/sensingData/list`,
    method: 'GET',
    params:data
  });
}


/**
 * 水域监测数据
 * @param
 * @returns 返回一个 Promise
 */
export function getWaterMonitoring(data) {
  return request({
    url: `/system/levelData/list`,
    method: 'GET',
    params:data
  });
}

/**
 * 大桥周边气象监测数据
 * @param
 * @returns 返回一个 Promise
 */
export function getMeteorologicalMonitoring(data) {
  return request({
    url: `/system/meteorology/all`,
    method: 'GET',
    params:data
  });
}

/**
 * 查询大桥卡口设备
 * @param
 * @returns 返回一个 Promise
 */
export function getPointDevice(data) {
  return request({
    url: `/system/checkpointDevice/list`,
    method: 'GET',
    params:data
  });
}
