import request from '@/utils/request';

/**
 * 获取事故情况数据
 * @param data - 请求参数，包含筛选条件等信息
 * @returns 返回一个 Promise
 */
export function getEventStatistics(data: any) {
  return request({
    url: '/system/screen/situation/bridgeEventStatistics',
    method: 'GET',
    params: data
  });
}

/**
 * 获取事故情况基本数据
 * @param data - 请求参数，包含筛选条件等信息
 * @returns 返回一个 Promise
 */
export function getEventList(data: any) {
  return request({
    url: '/system/screen/situation/specialEvents/list',
    method: 'GET',
    params: data
  });
}

/**
 * 根据事件id获取事件详情
 * @param id - 请求参数，事件id
 * @returns 返回一个 Promise
 */
export function getWarningId(id: any) {
  return request({
    url: `/system/screen/situation/event-detail/${id}`,
    method: 'GET',
  });
}

/**
 * 获取桥梁结构健康
 * @param data - 请求参数，
 * @returns 返回一个 Promise
 */
export function getHealthstatistics(data: any) {
  return request({
    url: `/system/screen/situation/bridge-health-statistics`,
    method: 'GET',
    params: data
  });
}

/**
 * 获取桥梁结构健康分页
 * @param data - 请求参数，
 * @returns 返回一个 Promise
 */
export function getHealthStatisticsPage(data: any) {
  return request({
    url: `/system/screen/situation/bridge-health-page`,
    method: 'GET',
    params: data
  });
}

/**
 * 获取社会舆情
 * @param data - 请求参数，
 * @returns 返回一个 Promise
 */
export function getSentimentStats(data: any) {
  return request({
    url: `/system/screen/opinionInfo/sentimentStats`,
    method: 'GET',
    params: data
  });
}


/**
 * 获取社会舆情列表数据
 * @param data - 请求参数，
 * @returns 返回一个 Promise
 */
export function getSearchPage(data: any) {
  return request({
    url: `/system/screen/opinionInfo/searchPage`,
    method: 'GET',
    params: data
  });
}

/**
 * 获取事件列表
 * @param data - 请求参数，
 * @returns 返回一个 Promise
 */
export function getWarningStatistics(data: any) {
  return request({
    url: `/system/screen/opinionInfo/warningStatistics`,
    method: 'GET',
    params: data
  });
}
/**
 * 获取事件列表分页
 * @param data - 请求参数，
 * @returns 返回一个 Promise
 */
export function getWarningPage(data: any) {
  return request({
    url: `/system/screen/opinionInfo/emergencyPage`,
    method: 'GET',
    params: data
  });
}


/**
 * 获取车流趋势数据
 * @param data - 请求参数，
 * @returns 返回一个 Promise
 */
export function getFlowTrend(data: any) {
  return request({
    url: `/system/screen/prefabricated/flowTrend?time=${data}`,
    method: 'GET',
    params: data
  });
}

