import request from '@/utils/request';


/**
 * 获取船舶实时数据列表
 * @param data - 请求参数，包含筛选条件等信息
 * @returns 返回一个 Promise
 */
export function getShipRealtime(data: any) {
  return request({
    url: '/system/screen/iot/ship/realtime',
    method: 'GET',
    params: data
  });
}

/**
 * 获取船舶AIS设备列表
 * @param data - 请求参数，包含筛选条件等信息
 * @returns 返回一个 Promise
 */
export function getShipAis(data: any) {
  // 发起请求，请求的 URL 为 /system/bridge/list，请求方法为 GET，请求参数为 data
  return request({
    url: '/system/shipData/list',
    method: 'GET',
    params: data
  });
}
/**
 * 获取电子围栏数据
 * @returns 返回一个 Promise
 */
export function getNavigationEnv() {
  // 发起请求，请求的 URL 为 /system/bridge/list，请求方法为 GET，请求参数为 data
  return request({
    url: '/system/screen/bridge/navigationEnv',
    method: 'GET',
  });
}

/**
 * 获取执法船监控
 * @returns 返回一个 Promise
 */
export function getUrlVideo() {
  // 发起请求，请求的 URL 为 /system/bridge/list，请求方法为 GET，请求参数为 data
  return request({
    url: '/system/dahua/realplay/getUrlNew',
    method: 'post',
  });
}

/**
 * 获取监控控制上下左右
 * @returns 返回一个 Promise
 */
export function getControlling(data: any) {
  // 发起请求，请求的 URL 为 /system/bridge/list，请求方法为 GET，请求参数为 data
  return request({
    url: 'system/dahua/realplay/controlling',
    method: 'post',
    params: data
  });
}

/**
 * 获取监控回放
 * @returns 返回一个 Promise
 */
export function getBackUrl(data: any) {
  return request({
    url: 'system/dahua/realplay/getBackUrl',
    method: 'post',
    params: data
  });
}


/**
 * 获取五座桥的一个在舶范围
 * @returns 返回一个 Promise
 */
export function getBridgeRange() {
  return request({
    url: '/system/screen/wading/bridgeRange/list',
    method: 'get',
  });
}

/**
 * 获取车辆GPS定位信息
 * @param data - 请求参数，包含筛选条件等信息
 * @returns 返回一个 Promise
 */
export function getVehicleGpsTracking(data: any) {
  return request({
    url: '/system/vehicleGpsTracking/listLatestWithVehicle',
    method: 'GET',
    params: data
  });
}

/**
 * 获取雷达感知数据列表
 * @returns 返回一个 Promise
 */
export function getRadarSensingData() {
  return request({
    url: '/system/radarSensingData/list',
    method: 'get',
  });
}

/**
 * 根据mmsi号查询历史轨迹
 * @returns 返回一个 Promise
 */
export function getHistoryIot(data) {
  return request({
    url: '/system/screen/iot/history',
    method: 'get',
    params: data
  });
}