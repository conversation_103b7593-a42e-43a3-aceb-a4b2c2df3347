import request from '@/utils/request';

/**
 * 获取车辆基本信息数据
 * @param data - 请求参数，包含筛选条件等信息
 * @returns 返回一个 Promise
 */
export function getVehicleList(data: any) {
  return request({
    url: '/system/screen/vehicle/list',
    method: 'GET',
    params: data
  });
}

/**
 * 获取车辆基本信息数据
 * @param data - 请求参数，包含筛选条件等信息
 * @returns 返回一个 Promise
 */
export function getTransport(data: any) {
  return request({
    url: '/system/screen/vehicle/transport',
    method: 'GET',
    params: data
  });
}

/**
 * 获取车辆的历史轨迹
 * @param data - 请求参数，包含筛选条件等信息
 * @returns 返回一个 Promise
 */
export function getGpsByVehicleId(data: any) {
  return request({
    url: '/system/screen/vehicle/gpsByVehicleId',
    method: 'GET',
    params: data
  });
}


/**
 * 获取卡口监测数据
 * @param data - 请求参数，包含筛选条件等信息
 * @returns 返回一个 Promise
 */
export function getmonitorList(data: any) {
  return request({
    url: '/system/monitorData/list',
    method: 'GET',
    params: data
  });
}

/**
 * 获取车辆规划路线
 * @param data - 请求参数，包含筛选条件等信息
 * @returns 返回一个 Promise
 */
export function getlistByTransportId(data: any) {
  return request({
    url: '/system/transportRoute/listByTransportId',
    method: 'GET',
    params: data
  });
}

/**
 * 获取卡口流量数据
 * @param data - 请求参数，包含筛选条件等信息
 * @returns 返回一个 Promise
 */
export function getFlowList(data: any) {
  return request({
    url: '/system/TrafficFlow/list',
    method: 'GET',
    params: data
  });
}
 
/**
 * 获取事件信息分页数据 
 * @param data - 请求参数，包含筛选条件等信息
 * @returns 返回一个 Promise
 */
export function getEventInfoPage(data: any) {
  return request({
    url: '/system/EventInfo/page',
    method: 'GET',
    params: data
  });
}
