import request from '@/utils/request';

export function addEnvironmentalPollutionWarning(data : any) {
  return request({
    url: '/system/environmentalPollutionWarning',
    method: 'POST',
    data: data
  });
}

export function getEnvironmentalPollutionWarningList(data : any) {
  return request({
    url: '/system/environmentalPollutionWarning/list',
    method: 'GET',
    params: data
  });
}

export function getEnvironmentalPollutionWarningById(id : any) {
  return request({
    url: '/system/environmentalPollutionWarning/' + id,
    method: 'GET'
  });
}

export function updateEnvironmentalPollutionWarning(data : any) {
  return request({
    url: '/system/environmentalPollutionWarning',
    method: 'PUT',
    data: data
  });
}

export function deleteEnvironmentalPollutionWarning(id : any) {
  return request({
    url: '/system/environmentalPollutionWarning/' + id,
    method: 'DELETE'
  });
}
