import request from '@/utils/request';
// 获取路由
export function addGeologicalWarning(data : any) {
  return request({
    url: '/system/geologicalWarning',
    method: 'POST',
    data: data
  });
}

export function getGeologyWarningList(data : any) {
  return request({
    url: '/system/geologicalWarning/list',
    method: 'GET',
    params: data
  });
}

export function getGeologyWarningById(id : any) {
  return request({
    url: '/system/geologicalWarning/' + id,
    method: 'GET'
  });   
}

export function updateGeologyWarning(data : any) {
  return request({
    url: '/system/geologicalWarning',
    method: 'PUT',
    data: data
  });
}

export function deleteGeologyWarning(id : any) {
  return request({
    url: '/system/geologicalWarning/' + id,
    method: 'DELETE'
  });
}