import request from '@/utils/request';
// 获取路由
export function addMeteorologicalWarning(data : any) {
  return request({
    url: '/system/meteorologicalWarning',
    method: 'POST',
    data: data
  });
}

export function getMeteorologicalWarningList(data : any) {
  return request({
    url: '/system/meteorologicalWarning/list',
    method: 'GET',
    params: data
  });
}

export function getMeteorologicalWarningById(id : any) {
  return request({
    url: '/system/meteorologicalWarning/' + id,
    method: 'GET'
  });
}

export function updateMeteorologicalWarning(data : any) {
  return request({
    url: '/system/meteorologicalWarning',
    method: 'PUT',
    data: data
  });
}

export function deleteMeteorologicalWarning(id : any) {
  return request({
    url: '/system/meteorologicalWarning/' + id,
    method: 'DELETE'
  });
}

