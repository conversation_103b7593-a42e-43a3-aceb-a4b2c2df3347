import request from '@/utils/request';
import type {
  TrafficFlowStats,
  DirectionFlowStats,
  TrafficFlowAnalysis,
  HubFlowStat,
  ApiResponse
} from '@/api/types';

/**
 * 获取通行流量统计数据
 * 用于通行流量、车流趋势、流量统计
 * @param type - 统计类型：1-当天（3小时一段），2-当月（按天），3-当年（按月）
 * @returns 返回流量统计数据
 */
export function getTrafficFlowStats(type: any): Promise<ApiResponse<TrafficFlowStats>> {
  return request({
    url: `/system/TrafficFlow/stats?type=${type}`,
    method: 'GET'
  });
}

/**
 * 获取各个方向的流量统计
 * 用于拥堵对比、拥堵指数
 * @returns 返回方向流量统计数据
 */
export function getDirectionFlowStats(): Promise<ApiResponse<DirectionFlowStats>> {
  return request({
    url: '/system/TrafficFlow/directionStats',
    method: 'GET'
  });
}

/**
 * 获取运行分析数据
 * 获取最新的断面流量数据
 * @returns 返回运行分析数据列表
 */
export function getTrafficFlowAnalysis(): Promise<ApiResponse<TrafficFlowAnalysis[]>> {
  return request({
    url: '/system/TrafficFlow/latestByHub',
    method: 'GET'
  });
}

/**
 * 获取历史流量数据
 * 按断面统计的历史流量
 * @returns 返回历史流量统计数据
 */
export function getHubFlowStats(): Promise<ApiResponse<HubFlowStat[]>> {
  return request({
    url: '/system/TrafficFlow/hubStats',
    method: 'GET'
  });
}
