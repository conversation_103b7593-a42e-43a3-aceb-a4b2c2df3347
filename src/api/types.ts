/**
 * 注册
 */
export type RegisterForm = {
  tenantId: string;
  username: string;
  password: string;
  confirmPassword?: string;
  code?: string;
  uuid?: string;
  userType?: string;
};

/**
 * 登录请求
 */
export interface LoginData {
  tenantId?: string;
  username?: string;
  password?: string;
  rememberMe?: boolean;
  socialCode?: string;
  socialState?: string;
  source?: string;
  code?: string;
  uuid?: string;
  clientId: string;
  grantType: string;
}

/**
 * 登录响应
 */
export interface LoginResult {
  access_token: string;
}

/**
 * 钉钉用户信息
 */
export interface DingTalkUserInfo {
  lastName?: string;
  accountId: string;
  realmId?: string;
  clientId?: string;
  tenantName?: string;
  realmName?: string;
  namespace?: string;
  tenantId: string;
  nickNameCn?: string;
  tenantUserId?: string;
  account?: string;
  employeeCode?: string;
}

/**
 * 验证码返回
 */
export interface VerifyCodeResult {
  captchaEnabled: boolean;
  uuid?: string;
  img?: string;
}

/**
 * 租户
 */
export interface TenantVO {
  companyName: string;
  domain: any;
  tenantId: string;
}

export interface TenantInfo {
  tenantEnabled: boolean;
  voList: TenantVO[];
}

// export interface AccidentEffect {
//   warningName: string;
//   occurrenceLocation: string;
//   warningLevel: string;
//   accidentType: string;
//   occurTime: string;
// }

/**
 * 监控点信息
 */
export interface CameraMonitorPoint {
  /** 监控点编号(UUID) */
  cameraindexcode: string;
  /** 创建时间(ISO8601格式) */
  createtime: string;
  /** 图标编号(1-255位) */
  gbindexcode: string;
  /** 纬度(WGS84) */
  latitude: string;
  /** 经度(WGS84) */
  longitude: string;
  /** 管理中心名称 */
  mgtcentername: string;
  /** 监控点名称(1-255位) */
  name: string;
  /** 桩号 */
  stakenum: string;
  /** 在线状态(0-离线 1-在线) */
  status: string;
  /** 更新时间(ISO8601格式) */
  updatetime: string;
}

/**
 * 流量统计数据
 */
export interface TrafficFlowStats {
  [key: string]: number; // 如 "01月": 0, "02月": 0 等
}

/**
 * 方向流量统计
 */
export interface DirectionFlowStats {
  /** 舟向流量 */
  舟向: number;
  /** 宁向流量 */
  宁向: number;
  /** 总流量 */
  totalCount: number;
  /** 舟向占比 */
  舟向占比: string;
  /** 宁向占比 */
  宁向占比: string;
}

/**
 * 运行分析数据
 */
export interface TrafficFlowAnalysis {
  /** 自增主键 */
  id: string;
  /** 方向中文名 */
  direction: string;
  /** 方向代码 */
  directionCode: number;
  /** 数据分区字段 */
  ds: string;
  /** 门架编号 */
  gantryId: string;
  /** 小时 */
  hourBatchNo: number;
  /** 断面起点 */
  hubCode01: string;
  /** 断面终点 */
  hubCode02: string;
  /** 管理中心名称 */
  regionName: string;
  /** 断面序号 */
  sectionNo: number;
  /** 站码 */
  stationCode: string;
  /** 数据日期 */
  tradeDate: number;
  /** 断面流量 */
  vehicleCls: number;
  /** 业主编码 */
  yzCode: number;
}

/**
 * 历史流量统计
 */
export interface HubFlowStat {
  /** 断面起点 */
  hubCode01: string;
  /** 总流量 */
  totalVehicleCls: number;
}

/**
 * 通用API响应格式
 */
export interface ApiResponse<T = any> {
  /** 响应码 */
  code: number;
  /** 响应消息 */
  msg: string;
  /** 响应数据 */
  data: T;
}
