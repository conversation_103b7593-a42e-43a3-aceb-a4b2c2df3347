// 动效配置和管理系统
@import './variables.module.scss';

// ==================== 动效配置管理 ====================

// 动效偏好设置
:root {
  // 动效开关
  --animations-enabled: 1;
  --reduced-motion: 0;
  
  // 性能模式
  --performance-mode: normal; // normal | high-performance | accessibility
  
  // 动效强度
  --animation-intensity: 1; // 0.5 | 1 | 1.5
}

// 检测用户偏好设置
@media (prefers-reduced-motion: reduce) {
  :root {
    --reduced-motion: 1;
    --animation-intensity: 0.5;
  }
}

// 动效禁用类
.no-animations,
.no-animations * {
  animation-duration: 0.01ms !important;
  animation-iteration-count: 1 !important;
  transition-duration: 0.01ms !important;
  transition-delay: 0ms !important;
  scroll-behavior: auto !important;
}

// 减少动效类
.reduced-animations,
.reduced-animations * {
  animation-duration: calc(var(--animation-duration-normal) * 0.5) !important;
  transition-duration: calc(var(--animation-duration-normal) * 0.5) !important;
}

// ==================== 性能优化动效 ====================

// GPU加速优化
@mixin gpu-accelerated {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

// 高性能动效 mixin
@mixin high-performance-animation($property: transform) {
  @include gpu-accelerated;
  will-change: $property;
  
  &:hover {
    will-change: auto;
  }
}

// 条件动效 mixin - 根据用户偏好调整
@mixin conditional-animation($animation-name, $duration: var(--animation-duration-normal)) {
  @if var(--reduced-motion) == 0 {
    animation: $animation-name calc(#{$duration} * var(--animation-intensity)) var(--animation-easing);
  }
}

// ==================== 专用动效类 ====================

// 加载动效
.loading-spinner {
  @include high-performance-animation(transform);
  animation: spin 1s linear infinite;
  
  @media (prefers-reduced-motion: reduce) {
    animation: pulse 2s ease-in-out infinite;
  }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

// 呼吸动效
.breathing {
  animation: breathing 3s ease-in-out infinite;
  
  @media (prefers-reduced-motion: reduce) {
    animation: none;
    opacity: 0.8;
  }
}

@keyframes breathing {
  0%, 100% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.05); opacity: 0.8; }
}

// 闪烁动效
.blink {
  animation: blink 1.5s ease-in-out infinite;
  
  @media (prefers-reduced-motion: reduce) {
    animation: none;
    opacity: 0.7;
  }
}

@keyframes blink {
  0%, 50%, 100% { opacity: 1; }
  25%, 75% { opacity: 0.3; }
}

// 摇摆动效
.shake {
  animation: shake 0.5s ease-in-out;
  
  @media (prefers-reduced-motion: reduce) {
    animation: none;
    border: 2px solid var(--el-color-danger);
  }
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
  20%, 40%, 60%, 80% { transform: translateX(5px); }
}

// 弹跳动效
.bounce {
  animation: bounce 1s ease-in-out;
  
  @media (prefers-reduced-motion: reduce) {
    animation: none;
    transform: scale(1.1);
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% { transform: translateY(0); }
  40%, 43% { transform: translateY(-15px); }
  70% { transform: translateY(-7px); }
  90% { transform: translateY(-3px); }
}

// 打字机效果
.typewriter {
  overflow: hidden;
  border-right: 2px solid var(--el-color-primary);
  white-space: nowrap;
  animation: typing 3s steps(40, end), blink-caret 0.75s step-end infinite;
  
  @media (prefers-reduced-motion: reduce) {
    animation: none;
    border-right: none;
  }
}

@keyframes typing {
  from { width: 0; }
  to { width: 100%; }
}

@keyframes blink-caret {
  from, to { border-color: transparent; }
  50% { border-color: var(--el-color-primary); }
}

// ==================== 交互反馈动效 ====================

// 点击波纹效果
.ripple {
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
  }
  
  &:active::before {
    width: 300px;
    height: 300px;
  }
  
  @media (prefers-reduced-motion: reduce) {
    &::before {
      display: none;
    }
  }
}

// 悬停发光效果
.glow {
  transition: box-shadow var(--animation-duration-normal) var(--animation-easing);
  
  &:hover {
    box-shadow: 0 0 20px rgba(64, 158, 255, 0.5);
  }
  
  @media (prefers-reduced-motion: reduce) {
    &:hover {
      box-shadow: 0 0 0 2px var(--el-color-primary);
    }
  }
}

// 成功反馈动效
.success-feedback {
  animation: successPulse 0.6s ease-out;
  
  @media (prefers-reduced-motion: reduce) {
    animation: none;
    background-color: var(--el-color-success-light-8);
  }
}

@keyframes successPulse {
  0% { background-color: var(--el-color-success); transform: scale(1); }
  50% { background-color: var(--el-color-success-light-3); transform: scale(1.05); }
  100% { background-color: transparent; transform: scale(1); }
}

// 错误反馈动效
.error-feedback {
  animation: errorShake 0.5s ease-out;
  
  @media (prefers-reduced-motion: reduce) {
    animation: none;
    border: 2px solid var(--el-color-danger);
  }
}

@keyframes errorShake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-3px); }
  20%, 40%, 60%, 80% { transform: translateX(3px); }
}

// ==================== 工具类 ====================

// 动效延迟类
.delay-1 { animation-delay: 0.1s; }
.delay-2 { animation-delay: 0.2s; }
.delay-3 { animation-delay: 0.3s; }
.delay-4 { animation-delay: 0.4s; }
.delay-5 { animation-delay: 0.5s; }

// 动效持续时间类
.duration-fast { animation-duration: var(--animation-duration-fast) !important; }
.duration-normal { animation-duration: var(--animation-duration-normal) !important; }
.duration-slow { animation-duration: var(--animation-duration-slow) !important; }

// 动效缓动类
.ease-linear { animation-timing-function: linear !important; }
.ease-in { animation-timing-function: ease-in !important; }
.ease-out { animation-timing-function: ease-out !important; }
.ease-in-out { animation-timing-function: ease-in-out !important; }
.ease-bounce { animation-timing-function: var(--animation-easing-bounce) !important; }
