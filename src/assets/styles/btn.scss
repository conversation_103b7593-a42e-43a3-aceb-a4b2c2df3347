@import './variables.module.scss';

@mixin colorBtn($color) {
  background: $color;

  &:hover {
    color: $color;

    &:before,
    &:after {
      background: $color;
    }
  }
}

.blue-btn {
  @include colorBtn($blue);
}

.light-blue-btn {
  @include colorBtn($light-blue);
}

.red-btn {
  @include colorBtn($red);
}

.pink-btn {
  @include colorBtn($pink);
}

.green-btn {
  @include colorBtn($green);
}

.tiffany-btn {
  @include colorBtn($tiffany);
}

.yellow-btn {
  @include colorBtn($yellow);
}

.pan-btn {
  font-size: 14px;
  color: #fff;
  padding: 14px 36px;
  border-radius: 8px;
  border: none;
  outline: none;
  transition: 600ms ease all;
  position: relative;
  display: inline-block;

  &:hover {
    background: #fff;

    &:before,
    &:after {
      width: 100%;
      transition: 600ms ease all;
    }
  }

  &:before,
  &:after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    height: 2px;
    width: 0;
    transition: 400ms ease all;
  }

  &::after {
    right: inherit;
    top: inherit;
    left: 0;
    bottom: 0;
  }
}

.custom-button {
  display: inline-block;
  line-height: 1;
  white-space: nowrap;
  cursor: pointer;
  background: #fff;
  color: #fff;
  -webkit-appearance: none;
  text-align: center;
  box-sizing: border-box;
  outline: 0;
  margin: 0;
  padding: 10px 15px;
  font-size: 14px;
  border-radius: 4px;
}

// ==================== 统一交互动效系统 ====================

// 动效配置变量
:root {
  --animation-duration-fast: 0.15s;
  --animation-duration-normal: 0.3s;
  --animation-duration-slow: 0.5s;
  --animation-easing: cubic-bezier(0.4, 0, 0.2, 1);
  --animation-easing-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
  --scale-hover: 1.05;
  --scale-active: 0.95;
  --scale-focus: 1.02;
}

// 基础动效 mixin
@mixin base-interaction {
  transition: all var(--animation-duration-normal) var(--animation-easing);
  cursor: pointer;
  user-select: none;

  &:focus {
    outline: none;
    transform: scale(var(--scale-focus));
  }

  &:active {
    transform: scale(var(--scale-active));
    transition-duration: var(--animation-duration-fast);
  }
}

// 按钮动效 mixin
@mixin button-interaction($hover-scale: var(--scale-hover)) {
  @include base-interaction;

  &:hover:not(:disabled):not(.disabled) {
    transform: scale($hover-scale);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  &:disabled,
  &.disabled {
    cursor: not-allowed;
    opacity: 0.6;
    transform: none !important;
    box-shadow: none !important;
  }
}

// 图标动效 mixin
@mixin icon-interaction {
  @include base-interaction;

  &:hover {
    transform: scale(var(--scale-hover)) rotate(5deg);
  }

  &:active {
    transform: scale(var(--scale-active)) rotate(-5deg);
  }
}

// 卡片动效 mixin
@mixin card-interaction {
  transition: all var(--animation-duration-normal) var(--animation-easing);

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }

  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

// 脉冲动效 mixin
@mixin pulse-animation($color: var(--el-color-primary)) {
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100%;
    height: 100%;
    border-radius: inherit;
    background: $color;
    opacity: 0;
    transform: translate(-50%, -50%) scale(1);
    transition: all var(--animation-duration-normal) var(--animation-easing);
  }

  &:hover::before {
    opacity: 0.1;
    transform: translate(-50%, -50%) scale(1.1);
  }
}

// 通用交互动效类
.interactive {
  @include base-interaction;
}

.interactive-button {
  @include button-interaction;
}

.interactive-icon {
  @include icon-interaction;
}

.interactive-card {
  @include card-interaction;
}

.interactive-pulse {
  @include pulse-animation;
}

// 特殊动效类
.bounce-in {
  animation: bounceIn var(--animation-duration-slow) var(--animation-easing-bounce);
}

.fade-in {
  animation: fadeIn var(--animation-duration-normal) var(--animation-easing);
}

.slide-up {
  animation: slideUp var(--animation-duration-normal) var(--animation-easing);
}

// 动效关键帧
@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// ==================== Element Plus 组件动效覆盖 ====================

// 按钮组件动效
.el-button {
  @include button-interaction;

  &.el-button--primary {
    &:hover:not(:disabled) {
      box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
    }
  }

  &.el-button--success {
    &:hover:not(:disabled) {
      box-shadow: 0 4px 12px rgba(103, 194, 58, 0.3);
    }
  }

  &.el-button--warning {
    &:hover:not(:disabled) {
      box-shadow: 0 4px 12px rgba(230, 162, 60, 0.3);
    }
  }

  &.el-button--danger {
    &:hover:not(:disabled) {
      box-shadow: 0 4px 12px rgba(245, 108, 108, 0.3);
    }
  }

  &.el-button--text {
    @include base-interaction;
    box-shadow: none !important;

    &:hover:not(:disabled) {
      transform: scale(1.02);
      background-color: var(--el-color-primary-light-9);
    }
  }
}

// 输入框组件动效
.el-input {
  .el-input__inner {
    transition: all var(--animation-duration-normal) var(--animation-easing);

    &:focus {
      transform: scale(1.01);
      box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
    }
  }
}

// 选择器组件动效
.el-select {
  .el-input__inner {
    transition: all var(--animation-duration-normal) var(--animation-easing);

    &:hover {
      transform: scale(1.01);
    }
  }
}

// 卡片组件动效
.el-card {
  @include card-interaction;

  &.interactive-card {
    cursor: pointer;
  }
}

// 标签组件动效
.el-tag {
  @include base-interaction;

  &:hover {
    transform: scale(1.05);
  }

  &.el-tag--closable {
    .el-tag__close {
      @include icon-interaction;

      &:hover {
        transform: scale(1.2);
        color: var(--el-color-danger);
      }
    }
  }
}

// 菜单项动效
.el-menu-item,
.el-submenu__title {
  transition: all var(--animation-duration-normal) var(--animation-easing);

  &:hover {
    transform: translateX(4px);
  }
}

// 表格行动效
.el-table {
  .el-table__row {
    transition: all var(--animation-duration-fast) var(--animation-easing);

    &:hover {
      transform: scale(1.01);
    }
  }
}

// 对话框动效
.el-dialog {
  animation: dialogFadeIn var(--animation-duration-normal) var(--animation-easing);
}

@keyframes dialogFadeIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

// 消息提示动效
.el-message {
  animation: messageSlideIn var(--animation-duration-normal) var(--animation-easing);
}

@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 工具提示动效
.el-tooltip__popper {
  animation: tooltipFadeIn var(--animation-duration-fast) var(--animation-easing);
}

@keyframes tooltipFadeIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
