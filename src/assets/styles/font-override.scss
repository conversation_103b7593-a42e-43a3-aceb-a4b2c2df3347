/**
 * 全局字体统一强制样式
 * 使用!important覆盖组件内部的硬编码字体样式
 * 确保所有文字统一使用25-30px范围内的字体大小
 */

@import './variables.module.scss';

// ==================== 基础HTML元素强制字体 ====================

//// 基础文本元素
//* {
//  font-size: var(--font-size-base) !important; // 25px
//  line-height: var(--line-height-normal) !important;
//}
//
//// 标题元素强制字体
h1, .h1 {
  font-size: var(--font-size-module-title) !important; // 30px
}

h2, .h2 {
  font-size: var(--font-size-data-large) !important; // 30px
}

h3, .h3 {
  font-size: var(--font-size-data-medium) !important; // 26px
}

h4, .h4, h5, .h5, h6, .h6 {
  font-size: var(--font-size-base) !important; // 25px
}

// 段落和文本
p, div, label, a,span{
  font-size: var(--font-size-content) !important; // 25px
}
//
// 列表元素
ul, ol, li {
  font-size: var(--font-size-list-text) !important; // 25px
}

// 表格元素
table, th, td {
  font-size: var(--font-size-content) !important; // 25px
}

// 表单元素
input, textarea, select, button {
  font-size: var(--font-size-content) !important; // 25px
}

// ==================== Element Plus 组件强制字体 ====================

// 弹窗组件
.el-dialog {
  .el-dialog__title {
    font-size: var(--font-size-popup-title) !important; // 45px
  }

  .el-dialog__body {
    font-size: var(--font-size-content) !important; // 25px
  }

  .el-dialog__footer {
    font-size: var(--font-size-content) !important; // 25px
  }
}

// 按钮组件
.el-button {
  font-size: var(--font-size-content) !important; // 25px

  &.el-button--large {
    font-size: var(--font-size-data-medium) !important; // 26px
  }

  &.el-button--small {
    font-size: var(--font-size-base) !important; // 25px
  }
}

// 表格组件
.el-table {
  font-size: var(--font-size-content) !important; // 25px

  .el-table__header {
    font-size: var(--font-size-content) !important; // 25px
  }

  .el-table__body {
    font-size: var(--font-size-content) !important; // 25px
  }
}

// 表单组件
.el-form {
  .el-form-item__label {
    font-size: var(--font-size-label) !important; // 25px
  }

  .el-input__inner {
    font-size: var(--font-size-content) !important; // 25px
  }

  .el-textarea__inner {
    font-size: var(--font-size-content) !important; // 25px
  }
}

// 菜单组件
.el-menu {
  font-size: var(--font-size-menu) !important; // 30px

  .el-menu-item {
    font-size: var(--font-size-menu) !important; // 30px
  }

  .el-submenu__title {
    font-size: var(--font-size-menu) !important; // 30px
  }
}

// 选择器组件
.el-select {
  .el-input__inner {
    font-size: var(--font-size-content) !important; // 25px
  }
}

.el-option {
  font-size: var(--font-size-content) !important; // 25px
}

// 分页组件
.el-pagination {
  font-size: var(--font-size-content) !important; // 25px

  .el-pager li {
    font-size: var(--font-size-content) !important; // 25px
  }
}

// 标签组件
.el-tag {
  font-size: var(--font-size-content) !important; // 25px
}

// 提示组件
.el-tooltip__popper {
  font-size: var(--font-size-content) !important; // 25px
}

.el-message {
  font-size: var(--font-size-content) !important; // 25px
}

.el-notification {
  font-size: var(--font-size-content) !important; // 25px
}

// ==================== 项目特定组件强制字体 ====================

// 弹窗组件 (PopUp)
.popup-container {
  .popup-title {
    font-size: var(--font-size-popup-title) !important; // 45px
  }

  .popup-content {
    font-size: var(--font-size-content) !important; // 25px
  }
}

// 导航组件 (Navigation)
.navigation {
  font-size: var(--font-size-menu) !important; // 30px

  .nav-item {
    font-size: var(--font-size-menu) !important; // 30px
  }
}

// 桥梁组件 (Bridge)
.bridge-component {
  .bridge-title {
    font-size: var(--font-size-component-title) !important; // 37px
  }

  .bridge-content {
    font-size: var(--font-size-content) !important; // 25px
  }

  .bridge-data {
    font-size: var(--font-size-data-large) !important; // 30px
  }
}

// 流量组件 (FlowRate)
.flow-rate {
  .flow-title {
    font-size: var(--font-size-component-title) !important; // 37px
  }

  .flow-data {
    font-size: var(--font-size-data-large) !important; // 30px
  }

  .flow-label {
    font-size: var(--font-size-content) !important; // 25px
  }
}

// 地图组件相关
.map-component {
  .map-popup {
    font-size: var(--font-size-content) !important; // 25px
  }

  .map-tooltip {
    font-size: var(--font-size-content) !important; // 25px
  }
}

// 数据展示组件
.data-display {
  .data-title {
    font-size: var(--font-size-component-title) !important; // 37px
  }

  .data-value {
    font-size: var(--font-size-data-large) !important; // 30px
  }

  .data-unit {
    font-size: var(--font-size-content) !important; // 25px
  }
}

// ==================== 通用类名强制字体 ====================

// 标题类
.title, .heading {
  font-size: var(--font-size-component-title) !important; // 37px
}

.subtitle {
  font-size: var(--font-size-module-title) !important; // 30px
}

// 内容类
.content, .text {
  font-size: var(--font-size-content) !important; // 25px
}

// 数据类
.data-large {
  font-size: var(--font-size-data-large) !important; // 30px
}

.data-medium {
  font-size: var(--font-size-data-medium) !important; // 26px
}

.data-small {
  font-size: var(--font-size-data-small) !important; // 25px
}

// 列表类
.list-item {
  font-size: var(--font-size-list-text) !important; // 25px
}

// 标签类
.label {
  font-size: var(--font-size-label) !important; // 25px
}

// ==================== 特殊情况排除 ====================

// 图标字体不受影响
.icon, .el-icon, [class*="el-icon-"] {
  font-size: inherit !important;
}

// Logo和特殊标识不受影响
.logo, .brand {
  font-size: inherit !important;
}

// 代码字体不受影响
code, pre, .code {
  font-size: inherit !important;
}
