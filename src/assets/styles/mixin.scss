@mixin clearfix {
  &:after {
    content: '';
    display: table;
    clear: both;
  }
}

@mixin scrollBar {
  &::-webkit-scrollbar-track-piece {
    background: #d3dce6;
  }

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background: #99a9bf;
    border-radius: 20px;
  }
}

@mixin relative {
  position: relative;
  width: 100%;
  height: 100%;
}

@mixin pct($pct) {
  width: #{$pct};
  position: relative;
  margin: 0 auto;
}

@mixin triangle($width, $height, $color, $direction) {
  $width: $width/2;
  $color-border-style: $height solid $color;
  $transparent-border-style: $width solid transparent;
  height: 0;
  width: 0;

  @if $direction==up {
    border-bottom: $color-border-style;
    border-left: $transparent-border-style;
    border-right: $transparent-border-style;
  } @else if $direction==right {
    border-left: $color-border-style;
    border-top: $transparent-border-style;
    border-bottom: $transparent-border-style;
  } @else if $direction==down {
    border-top: $color-border-style;
    border-left: $transparent-border-style;
    border-right: $transparent-border-style;
  } @else if $direction==left {
    border-right: $color-border-style;
    border-top: $transparent-border-style;
    border-bottom: $transparent-border-style;
  }
}

// 响应式字体系统 mixin
@mixin responsive-font($base-size: var(--font-size-base), $line-height: var(--line-height-normal)) {
  font-size: $base-size;
  line-height: $line-height;

  // 小屏幕设备 (手机)
  @media (max-width: 768px) {
    font-size: calc(#{$base-size} * 0.9);
  }

  // 大屏幕设备 (桌面)
  @media (min-width: 1200px) {
    font-size: calc(#{$base-size} * 1.05);
  }
}

// 字体大小快捷 mixin
@mixin font-xs { @include responsive-font(var(--font-size-xs), var(--line-height-tight)); }
@mixin font-sm { @include responsive-font(var(--font-size-sm), var(--line-height-normal)); }
@mixin font-base { @include responsive-font(var(--font-size-base), var(--line-height-normal)); }
@mixin font-lg { @include responsive-font(var(--font-size-lg), var(--line-height-normal)); }
@mixin font-xl { @include responsive-font(var(--font-size-xl), var(--line-height-relaxed)); }

// 文本样式组合 mixin
@mixin text-title {
  @include font-lg;
  font-weight: var(--font-weight-semibold);
  color: var(--el-text-color-primary);
}

@mixin text-subtitle {
  @include font-base;
  font-weight: var(--font-weight-medium);
  color: var(--el-text-color-regular);
}

@mixin text-body {
  @include font-sm;
  font-weight: var(--font-weight-normal);
  color: var(--el-text-color-regular);
}

@mixin text-caption {
  @include font-xs;
  font-weight: var(--font-weight-normal);
  color: var(--el-text-color-secondary);
}

// ==================== 多屏幕适配样式 ====================

/**
 * 主屏显示模式 (1080P)
 * 保持现有的缩放和居中显示效果
 */
.primary-display {
  // 保持现有的App.vue样式逻辑
  #app {
    // 继承现有样式，不做额外修改
    // 让现有的scaleStyle缩放逻辑继续工作
  }

  .screen-container {
    // 保持现有的缩放和定位逻辑
    // 这样可以维持上下黑边的效果
  }
}

/**
 * 扩展屏显示模式 - 简化版本
 * 仅用于移除黑色边框，不强制拉伸元素
 */
.extended-display {
  // 这个类现在主要由App.vue的fullscreen-mode类处理
  // 保留基本的全屏样式，但不强制拉伸内容

  // 确保弹窗在全屏下正确显示
  .el-dialog, .el-popup, .el-overlay {
    z-index: 10000 !important; // 确保弹窗在全屏内容之上
  }

  // 确保浮动窗口在全屏下正确显示
  .floating-window {
    z-index: 1000 !important;
  }
}

// ==================== 响应式适配增强 ====================

/**
 * 针对超大屏幕的额外优化
 */
@media (min-width: 3840px) {
  .extended-display {
    // 超大屏幕下的字体和间距微调
    .title {
      .tit-left {
        margin-left: calc(66px * 1.2); // 适当增加边距

        .bt {
          span:nth-child(1) {
            font-size: calc(57px * 1.1); // 略微增大主标题
          }
        }
      }

      .tit-right {
        margin-right: calc(71px * 1.2); // 适当增加边距
      }
    }
  }
}

/**
 * 确保在不同屏幕密度下的显示一致性
 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .extended-display {
    .screen-container {
      // 高DPI屏幕下的优化
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
    }
  }
}
