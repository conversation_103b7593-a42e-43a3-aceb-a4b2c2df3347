// global transition css

/* fullscreen transition */
.fullscreen-transitioning {
  // 全屏切换过渡期间的样式
  * {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  }

  // 防止过渡期间的闪烁
  .screen-container {
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1),
                width 0.3s cubic-bezier(0.4, 0, 0.2, 1),
                height 0.3s cubic-bezier(0.4, 0, 0.2, 1),
                opacity 0.15s ease-in-out !important;
  }

  // 确保标题和内容区域的平滑过渡
  .title, .content {
    transition: height 0.3s cubic-bezier(0.4, 0, 0.2, 1),
                flex 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  }
}

/* fade */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.28s;
}

.fade-enter,
.fade-leave-active {
  opacity: 0;
}

/* fade-transform */
.fade-transform--move,
.fade-transform-leave-active,
.fade-transform-enter-active {
  transition: all 0.5s;
}

.fade-transform-enter {
  opacity: 0;
  transform: translateX(-30px);
}

.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(30px);
}

/* breadcrumb transition */
.breadcrumb-enter-active,
.breadcrumb-leave-active {
  transition: all 0.5s;
}

.breadcrumb-enter,
.breadcrumb-leave-active {
  opacity: 0;
  transform: translateX(20px);
}

.breadcrumb-move {
  transition: all 0.5s;
}

.breadcrumb-leave-active {
  position: absolute;
}

/* ==================== 增强的过渡效果系统 ==================== */

/* 缩放过渡 */
.scale-enter-active,
.scale-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.scale-enter,
.scale-leave-to {
  opacity: 0;
  transform: scale(0.9);
}

/* 弹性缩放过渡 */
.bounce-scale-enter-active {
  transition: all 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.bounce-scale-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.bounce-scale-enter,
.bounce-scale-leave-to {
  opacity: 0;
  transform: scale(0.3);
}

/* 滑动过渡 - 上下 */
.slide-up-enter-active,
.slide-up-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-up-enter,
.slide-up-leave-to {
  opacity: 0;
  transform: translateY(20px);
}

.slide-down-enter-active,
.slide-down-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-down-enter,
.slide-down-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}

/* 滑动过渡 - 左右 */
.slide-left-enter-active,
.slide-left-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-left-enter,
.slide-left-leave-to {
  opacity: 0;
  transform: translateX(20px);
}

.slide-right-enter-active,
.slide-right-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-right-enter,
.slide-right-leave-to {
  opacity: 0;
  transform: translateX(-20px);
}

/* 旋转过渡 */
.rotate-enter-active,
.rotate-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.rotate-enter,
.rotate-leave-to {
  opacity: 0;
  transform: rotate(180deg) scale(0.8);
}

/* 翻转过渡 */
.flip-enter-active,
.flip-leave-active {
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.flip-enter,
.flip-leave-to {
  opacity: 0;
  transform: rotateY(90deg);
}

/* 列表过渡 */
.list-enter-active,
.list-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.list-enter,
.list-leave-to {
  opacity: 0;
  transform: translateY(10px);
}

.list-move {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.list-leave-active {
  position: absolute;
}

/* 模态框过渡 */
.modal-enter-active,
.modal-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modal-enter,
.modal-leave-to {
  opacity: 0;
}

.modal-enter .modal-container,
.modal-leave-to .modal-container {
  transform: scale(0.9) translateY(-20px);
}

/* 抽屉过渡 */
.drawer-enter-active,
.drawer-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.drawer-enter,
.drawer-leave-to {
  transform: translateX(100%);
}

/* 折叠过渡 */
.collapse-enter-active,
.collapse-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

.collapse-enter,
.collapse-leave-to {
  height: 0;
  opacity: 0;
}

/* 页面过渡 */
.page-enter-active,
.page-leave-active {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.page-enter {
  opacity: 0;
  transform: translateX(20px);
}

.page-leave-to {
  opacity: 0;
  transform: translateX(-20px);
}

/* 加载过渡 */
.loading-enter-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.loading-enter {
  opacity: 0;
  transform: scale(0.8);
}
