// 全局字体应用系统
@import './variables.module.scss';
@import './mixin.scss';

// 基础HTML元素字体优化
html {
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
}

body {
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  font-weight: var(--font-weight-normal);
}

// 标题系统 - 优化为25-30px范围
h1, .h1 {
  @include text-title;
  font-size: var(--font-size-component-title); // 37px -> 30px
  margin-bottom: 0.5em;
}

h2, .h2 {
  @include text-title;
  font-size: var(--font-size-module-title); // 30px
  margin-bottom: 0.5em;
}

h3, .h3 {
  @include text-title;
  font-size: var(--font-size-data-large); // 30px
  margin-bottom: 0.5em;
}

h4, .h4 {
  @include text-subtitle;
  font-size: var(--font-size-data-medium); // 26px
  margin-bottom: 0.5em;
}

h5, .h5 {
  @include text-subtitle;
  font-size: var(--font-size-base); // 25px
  margin-bottom: 0.5em;
}

h6, .h6 {
  @include text-body;
  font-size: var(--font-size-content); // 25px
  margin-bottom: 0.5em;
}
// 段落和文本
p {
  @include text-body;
  margin-bottom: 1em;
}

// 链接
a {
  font-size: inherit;
  line-height: inherit;
  transition: color 0.3s ease;
}

// 列表
ul, ol {
  @include text-body;

  li {
    margin-bottom: 0.25em;
  }
}

// 表格
table {
  @include text-body;

  th {
    @include text-subtitle;
    font-weight: var(--font-weight-semibold);
  }

  td {
    font-size: inherit;
    line-height: inherit;
  }
}

// 表单元素
input, textarea, select {
  @include text-body;
}

label {
  @include text-body;
  font-weight: var(--font-weight-medium);
}

// 按钮
button {
  @include text-body;
  font-weight: var(--font-weight-medium);
}
// Element Plus 组件字体优化 - 统一到25-30px范围
.el-button {
  font-size: var(--font-size-content) !important; // 25px
  font-weight: var(--font-weight-medium) !important;

  &.el-button--large {
    font-size: var(--font-size-data-large) !important; // 30px
  }

  &.el-button--small {
    font-size: var(--font-size-data-small) !important; // 25px
  }
}

.el-input {
  .el-input__inner {
    font-size: var(--font-size-content) !important; // 25px
    line-height: var(--line-height-normal) !important;
  }
}

.el-table {
  font-size: var(--font-size-content) !important; // 25px

  .el-table__header {
    th {
      font-size: var(--font-size-data-medium) !important; // 26px
      font-weight: var(--font-weight-semibold) !important;
    }
  }

  .el-table__body {
    td {
      font-size: var(--font-size-content) !important; // 25px
    }
  }
}
.el-dialog {
  .el-dialog__title {
    font-size: var(--font-size-data-large) !important; // 30px
    font-weight: var(--font-weight-semibold) !important;
  }

  .el-dialog__body {
    font-size: var(--font-size-content) !important; // 25px
    line-height: var(--line-height-normal) !important;
  }
}

.el-form {
  .el-form-item__label {
    font-size: var(--font-size-label) !important; // 25px
    font-weight: var(--font-weight-medium) !important;
  }
}

.el-menu {
  .el-menu-item {
    font-size: var(--font-size-menu) !important; // 30px
  }

  .el-submenu__title {
    font-size: var(--font-size-menu) !important; // 30px
  }
}
// 工具类 - 统一到25-30px范围
.text-xs { font-size: var(--font-size-data-small); } // 25px
.text-sm { font-size: var(--font-size-content); } // 25px
.text-base { font-size: var(--font-size-base); } // 25px
.text-lg { font-size: var(--font-size-data-medium); } // 26px
.text-xl { font-size: var(--font-size-data-large); } // 30px

.font-normal { font-weight: var(--font-weight-normal); }
.font-medium { font-weight: var(--font-weight-medium); }
.font-semibold { font-weight: var(--font-weight-semibold); }
.font-bold { font-weight: var(--font-weight-bold); }

.leading-tight { line-height: var(--line-height-tight); }
.leading-normal { line-height: var(--line-height-normal); }
.leading-relaxed { line-height: var(--line-height-relaxed); }

// 特殊组件字体优化
.panel-title {
  @include text-title;
  font-size: var(--font-size-module-title); // 30px
}

.card-title {
  @include text-subtitle;
  font-size: var(--font-size-data-medium); // 26px
}

.card-content {
  @include text-body;
}

.sidebar-menu {
  font-size: var(--font-size-content); // 25px
  line-height: var(--line-height-normal);
}
// 响应式字体调整 - 保持在25-30px范围内
@media (max-width: 768px) {
  .responsive-text {
    font-size: var(--font-size-data-small); // 25px (最小值)
  }
}

@media (min-width: 1200px) {
  .responsive-text {
    font-size: var(--font-size-data-medium); // 26px (适中值)
  }
}
