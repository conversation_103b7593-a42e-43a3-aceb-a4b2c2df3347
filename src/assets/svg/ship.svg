<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="720" height="720" viewBox="0 0 720 720">
  <defs>
    <style>
      .cls-1 {
        clip-path: url(#clip-画板_1);
      }

      .cls-2 {
        fill: #ff6868;
      }

      .cls-3 {
        fill: #d12828;
      }

      .cls-4 {
        fill: rgba(255,255,255,0);
      }
    </style>
    <clipPath id="clip-画板_1">
      <rect width="720" height="720"/>
    </clipPath>
  </defs>
  <g id="画板_1" data-name="画板 – 1" class="cls-1">
    <rect class="cls-4" width="720" height="720"/>
    <g id="组_159" data-name="组 159" transform="translate(-91.067 4.924)">
      <path id="路径_17" data-name="路径 17" class="cls-2" d="M458.125,9.387Q579.739,148.128,567.779,278.978c-2.953,31.7-5.939,66.344-8.744,101.55l-1.3,16.291c-8.252,105.6-14.863,213.14-15.257,259.765H511.968V688h-106.8V656.583H373.719q-11.713-268.845-21.655-351.635c-1.821-14.322-2.92-21.163-3.987-26.183Q338.75,153.632,458.109,9.387Z" transform="translate(0 0)"/>
      <path id="路径_18" data-name="路径 18" class="cls-3" d="M558.613,454.892q-4.971-107.342-42.064-134.361c-21.491-23.345-63.21-26.774-91.28,0-34.566,31.138-41.391,99.418-41.391,134.361Q412.153,417,436.556,417h72.512q32.753,13.608,49.545,37.9Z" transform="translate(-11.931 -95.626)"/>
      <path id="路径_19" data-name="路径 19" class="cls-3" d="M414.525,789.87m26.249,0H519.52q26.249,0,26.249,26.249v52.2q0,26.249-26.249,26.249H440.774q-26.249,0-26.249-26.249v-52.2Q414.525,789.87,440.774,789.87Z" transform="translate(-21.957 -255.31)"/>
    </g>
  </g>
</svg>
