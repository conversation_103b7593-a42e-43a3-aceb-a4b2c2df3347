<template>
  <div class="Box">
    <div class="title">
      <div class="img"></div>
      <span @click="handleTitleClick">{{ props.title }}</span>
      <slot name="unit" class="unit"></slot>
    </div>
    <slot name="content" class="content"></slot>
  </div>
</template>

<script setup lang="ts">
import { defineProps } from 'vue';
const props = defineProps({
  title: String,
  onTitleClick: Function
});

const handleTitleClick = () => {
  if (props.onTitleClick) {
    props.onTitleClick();
  }
};
</script>

<style lang="scss" scoped>
.Box {
  position: relative;
  background: #14141e;
  // background-size: 100% 100%;
  padding: 24px;
  display: flex;
  flex-direction: column;
  border-radius: 4px;
  .title {
    display: flex;
    align-items: center;
    .img {
      width: 40px;
      height: 40px;
      background: url('@/components/Box/images/titlebg.png') no-repeat center;
      background-size: 100% 100%;
      margin-right: 14px;
    }
    span {
      color: #fff;
      font-size: var(--font-size-data-large);
      font-family: 'Microsoft YaHei';
      font-weight: bold;
      cursor: pointer;
      transition: all 0.3s ease;
      position: relative;

      &:hover {
        color: #e6f3ff;
        text-shadow: 0 0 8px rgba(230, 243, 255, 0.3);
        transform: translateY(-1px);
      }

      &:active {
        transform: translateY(0);
      }
    }
  }
  .content {
    flex: 1;
  }
}
</style>
<style lang="scss">
.el-scrollbar {
  background: #35383d;
}
.el-select--large .el-select__wrapper {
  background: #35383d;
}
.el-select-dropdown__item span {
  color: #fff;
  font-size: var(--font-size-content);
  font-family: 'Microsoft YaHei';
}
.is-hovering {
  background: #35383d !important;
}
</style>
