<template>
  <div class="Bridge" :class="{ collapsed: !isContentVisible }">
    <div class="title" @click="toggleContent">桥梁 ></div>
    <div class="content" v-show="isContentVisible" @mouseenter="showScrollButtons" @mouseleave="hideScrollButtons">
      <div class="qb" @click="handleClick('all')" :class="isActive == 'all' ? 'active' : ''">全部</div>
      <div class="ql">
        <div class="scroll-container" ref="scrollContainer">
          <div class="box" v-for="item in bridgeData" @click="handleClick(item)" :key="item.id" :class="isActive == item ? 'active' : ''">
            {{ item.bridgeName }}
          </div>
        </div>
        <div class="scroll-buttons" v-show="isHovering">
          <div class="scroll-btn left" @click="scrollLeft">&lt;</div>
          <div class="scroll-btn right" @click="scrollRight">&gt;</div>
        </div>
      </div>
    </div>
    <div class="reveal" @click="handleReveal">
      <img src="@/assets/images/yj.png" alt="" />
      <span>{{ isReveal ? '隐藏桥名' : '显示桥名' }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { getBridgeLengths } from '@/api/bridge/home';
import { setViewToCoordinates, getPointById, updatePointName, addPoint, clearTypeFeatures } from '@/utils/mapMethods';
import bridgeImg from '@/assets/home/<USER>';

const isContentVisible = ref(true);
const isHovering = ref(false);
const scrollContainer = ref<HTMLElement | null>(null);

const isActive: any = ref('');

//收缩
const toggleContent = () => {
  isContentVisible.value = !isContentVisible.value;
};

//桥梁点击
const handleClick = (data: any) => {
  let bridgeId = '';
  let popupContent = '';
  isReveal.value = true;
  clearTypeFeatures('bridge_');
  if (data == 'all') {
    isActive.value = 'all';
    setViewToCoordinates([121.844237, 30.063635], 12); //聚焦舟山中心
    bridgeData.value.forEach((item: any) => {
      bridgeId = `bridge_${item.id}`;
      // 弹窗内容
      popupContent = `
          <div class="popup-title" style="min-width:700px;min-height:60px;display:flex;justify-content: space-between;align-items: center;background: linear-gradient(141deg, rgba(40, 40, 61, 0.85) 0%, rgba(0, 0, 0, 0.57) 44%, rgba(4, 3, 9, 0) 100%);padding:0px 25px;">
        <span style="color:#fff;font-size:var(--font-size-data-large);font-weight: bold;">${item.bridgeName}</span><button class="popup-close" style="font-size:var(--font-size-content)">X</button>
      </div>
      <div class="popup-content" style="min-width:700px;min-height:200px;background: linear-gradient(141deg, rgba(40, 40, 61, 0.85) 0%, rgba(0, 0, 0, 0.57) 44%, rgba(4, 3, 9, 0) 100%);padding:25px;border-top: 1px solid #accbff; ">
        <div class="ship-popup" style="width:100%;height:auto;">
              <div class="ship-info" style="font-size: var(--font-size-content);color: #fff;display:flex;justify-content: space-between;margin-bottom:20px;">
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">桥梁编号: ${item.id || ''}</span>
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">桥梁结构: ${item.bridgeType || ''}</span>
              </div>
              <div class="ship-info" style="font-size: var(--font-size-content);color: #fff;display:flex;justify-content: space-between;margin-bottom:20px;">
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">桥梁长度: ${item.bridgeLength || ''}米</span>
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">桥梁宽度: ${item.bridgeWidth || ''}米</span>
              </div>
              <div class="ship-info" style="font-size: var(--font-size-content);color: #fff;display:flex;justify-content: space-between;margin-bottom:20px;">
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">桥梁高度: ${item.bridgeHeight || ''}米</span>
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">设计载荷: ${item.designLoad || ''}墩</span>
              </div>
              <div class="ship-info" style="font-size: var(--font-size-content);color: #fff;display:flex;justify-content: space-between;margin-bottom:20px;">
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">通航等级: ${item.navigationGrade || ''}</span>
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">桥梁类型: ${item.type || ''}</span>
              </div>
              <div class="ship-info" style="font-size: var(--font-size-content);color: #fff;display:flex;justify-content: space-between;margin-bottom:20px;">
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">管理单位: ${item.deptName || ''}</span>
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">建造年限: ${item.buildDate ? item.buildDate.split(' ')[0] : ''}</span>
              </div>
              <div class="ship-info" style="font-size: var(--font-size-content);color: #fff;display:flex;justify-content: space-between;margin-bottom:20px;">
                <span style="display:inline-block;width:100%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">所属区域: ${item.region || ''}</span>
              </div>
              <div class="ship-info" style="font-size: var(--font-size-content);color: #fff;display:flex;margin-bottom:20px;">
                ${
                  item.fileUrl && item.fileUrl[0]
                    ? `<img style="width:300px;height:100px;object-fit:cover;" src="${item.fileUrl[0]}" alt="桥梁图片" />`
                    : `<div style="width:300px;height:100px;background:#2f3039;display:flex;justify-content:center;align-items:center;color:#fff;">暂无图片</div>`
                }
            </div>
      </div>
          `;
      addPoint([item.lon, item.lat], bridgeImg, 0.4, popupContent, bridgeId, item.bridgeName);
    });
  } else {
    isActive.value = data;
    bridgeId = `bridge_${data.id}`;
    // 弹窗内容
    popupContent = `
          <div class="popup-title" style="min-width:700px;min-height:60px;display:flex;justify-content: space-between;align-items: center;background: linear-gradient(141deg, rgba(40, 40, 61, 0.85) 0%, rgba(0, 0, 0, 0.57) 44%, rgba(4, 3, 9, 0) 100%);padding:0px 25px;">
        <span style="color:#fff;font-size:var(--font-size-data-large);font-weight: bold;">${data.bridgeName}</span><button class="popup-close" style="font-size:var(--font-size-content)">X</button>
      </div>
      <div class="popup-content" style="min-width:700px;min-height:200px;background: linear-gradient(141deg, rgba(40, 40, 61, 0.85) 0%, rgba(0, 0, 0, 0.57) 44%, rgba(4, 3, 9, 0) 100%);padding:25px;border-top: 1px solid #accbff; ">
        <div class="ship-popup" style="width:100%;height:auto;">
              <div class="ship-info" style="font-size: var(--font-size-content);color: #fff;display:flex;justify-content: space-between;margin-bottom:20px;">
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">桥梁编号: ${data.id || ''}</span>
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">桥梁结构: ${data.bridgeType || ''}</span>
              </div>
              <div class="ship-info" style="font-size: var(--font-size-content);color: #fff;display:flex;justify-content: space-between;margin-bottom:20px;">
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">桥梁长度: ${data.bridgeLength || ''}米</span>
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">桥梁宽度: ${data.bridgeWidth || ''}米</span>
              </div>
              <div class="ship-info" style="font-size: var(--font-size-content);color: #fff;display:flex;justify-content: space-between;margin-bottom:20px;">
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">桥梁高度: ${data.bridgeHeight || ''}米</span>
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">设计载荷: ${data.designLoad || ''}墩</span>
              </div>
              <div class="ship-info" style="font-size: var(--font-size-content);color: #fff;display:flex;justify-content: space-between;margin-bottom:20px;">
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">通航等级: ${data.navigationGrade || ''}</span>
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">桥梁类型: ${data.type || ''}</span>
              </div>
              <div class="ship-info" style="font-size: var(--font-size-content);color: #fff;display:flex;justify-content: space-between;margin-bottom:20px;">
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">管理单位: ${data.deptName || ''}</span>
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">建造年限: ${data.buildDate ? data.buildDate.split(' ')[0] : ''}</span>
              </div>
              <div class="ship-info" style="font-size: var(--font-size-content);color: #fff;display:flex;justify-content: space-between;margin-bottom:20px;">
                <span style="display:inline-block;width:100%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">所属区域: ${data.region || ''}</span>
              </div>
              <div class="ship-info" style="font-size: var(--font-size-content);color: #fff;display:flex;margin-bottom:20px;">
                ${
                  data.fileUrl && data.fileUrl[0]
                    ? `<img style="width:300px;height:100px;object-fit:cover;" src="${data.fileUrl[0]}" alt="桥梁图片" />`
                    : `<div style="width:300px;height:100px;background:#2f3039;display:flex;justify-content:center;align-items:center;color:#fff;">暂无图片</div>`
                }
            </div>
      </div>
          `;
    addPoint([data.lon, data.lat], bridgeImg, 0.4, popupContent, bridgeId, data.bridgeName);
    setViewToCoordinates([data.lon, data.lat]);
  }
};

const isReveal = ref(true);
//显示桥名
const handleReveal = () => {
  if (!isActive.value) return ElMessage.warning('请先选择桥梁');
  isReveal.value = !isReveal.value;
  if (!isReveal.value) {
    clearTypeFeatures('bridge_');
  } else {
    let bridgeId = '';
    let popupContent = '';
    if (isActive.value == 'all') {
      clearTypeFeatures('bridge_');
      bridgeData.value.forEach((item: any) => {
        bridgeId = `bridge_${item.id}`;
        // 弹窗内容
        popupContent = `
          <div class="popup-title" style="min-width:700px;min-height:60px;display:flex;justify-content: space-between;align-items: center;background: linear-gradient(141deg, rgba(40, 40, 61, 0.85) 0%, rgba(0, 0, 0, 0.57) 44%, rgba(4, 3, 9, 0) 100%);padding:0px 25px;">
        <span style="color:#fff;font-size:var(--font-size-data-large);font-weight: bold;">${item.bridgeName}</span><button class="popup-close" style="font-size:var(--font-size-content)">X</button>
      </div>
      <div class="popup-content" style="min-width:700px;min-height:200px;background: linear-gradient(141deg, rgba(40, 40, 61, 0.85) 0%, rgba(0, 0, 0, 0.57) 44%, rgba(4, 3, 9, 0) 100%);padding:25px;border-top: 1px solid #accbff; ">
        <div class="ship-popup" style="width:100%;height:auto;">
              <div class="ship-info" style="font-size: var(--font-size-content);color: #fff;display:flex;justify-content: space-between;margin-bottom:20px;">
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">桥梁编号: ${item.id || ''}</span>
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">桥梁结构类型: ${item.bridgeType || ''}</span>
              </div>
              <div class="ship-info" style="font-size: var(--font-size-content);color: #fff;display:flex;justify-content: space-between;margin-bottom:20px;">
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">桥梁长度: ${item.bridgeLength || ''}米</span>
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">桥梁宽度: ${item.bridgeWidth || ''}米</span>
              </div>
              <div class="ship-info" style="font-size: var(--font-size-content);color: #fff;display:flex;justify-content: space-between;margin-bottom:20px;">
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">桥梁高度: ${item.bridgeHeight || ''}米</span>
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">设计载荷: ${item.designLoad || ''}墩</span>
              </div>
              <div class="ship-info" style="font-size: var(--font-size-content);color: #fff;display:flex;justify-content: space-between;margin-bottom:20px;">
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">通航等级: ${item.navigationGrade || ''}</span>
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">桥梁类型: ${item.type || ''}</span>
              </div>
              <div class="ship-info" style="font-size: var(--font-size-content);color: #fff;display:flex;justify-content: space-between;margin-bottom:20px;">
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">管理单位: ${item.deptName || ''}</span>
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">建造年限: ${item.buildDate ? item.buildDate.split(' ')[0] : ''}</span>
              </div>
              <div class="ship-info" style="font-size: var(--font-size-content);color: #fff;display:flex;justify-content: space-between;margin-bottom:20px;">
                <span style="display:inline-block;width:100%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">所属区域: ${item.region || ''}</span>
              </div>
              <div class="ship-info" style="font-size: var(--font-size-content);color: #fff;display:flex;margin-bottom:20px;">
                ${
                  item.fileUrl && item.fileUrl[0]
                    ? `<img style="width:300px;height:100px;object-fit:cover;" src="${item.fileUrl[0]}" alt="桥梁图片" />`
                    : `<div style="width:300px;height:100px;background:#2f3039;display:flex;justify-content:center;align-items:center;color:#fff;">暂无图片</div>`
                }
              </div>
            </div>
      </div>
          `;
        addPoint([item.lon, item.lat], bridgeImg, 0.4, popupContent, bridgeId, item.bridgeName);
      });
    } else {
      clearTypeFeatures('bridge_');
      bridgeId = `bridge_${isActive.value.id}`;
      // 弹窗内容
      popupContent = `
          <div class="popup-title" style="min-width:700px;min-height:60px;display:flex;justify-content: space-between;align-items: center;background: linear-gradient(141deg, rgba(40, 40, 61, 0.85) 0%, rgba(0, 0, 0, 0.57) 44%, rgba(4, 3, 9, 0) 100%);padding:0px 25px;">
        <span style="color:#fff;font-size:var(--font-size-data-large);font-weight: bold;">${isActive.value.bridgeName}</span><button class="popup-close" style="font-size:var(--font-size-content)">X</button>
      </div>
      <div class="popup-content" style="min-width:700px;min-height:200px;background: linear-gradient(141deg, rgba(40, 40, 61, 0.85) 0%, rgba(0, 0, 0, 0.57) 44%, rgba(4, 3, 9, 0) 100%);padding:25px;border-top: 1px solid #accbff; ">
        <div class="ship-popup" style="width:100%;height:auto;">
              <div class="ship-info" style="font-size: var(--font-size-content);color: #fff;display:flex;justify-content: space-between;margin-bottom:20px;">
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">桥梁编号: ${isActive.value.id || ''}</span>
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">桥梁类型: ${isActive.value.bridgeType || ''}</span>
              </div>
              <div class="ship-info" style="font-size: var(--font-size-content);color: #fff;display:flex;justify-content: space-between;margin-bottom:20px;">
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">桥梁长度: ${isActive.value.bridgeLength || ''}米</span>
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">桥梁宽度: ${isActive.value.bridgeWidth || ''}米</span>
              </div>
              <div class="ship-info" style="font-size: var(--font-size-content);color: #fff;display:flex;justify-content: space-between;margin-bottom:20px;">
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">桥梁高度: ${isActive.value.bridgeHeight || ''}米</span>
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">设计载荷: ${isActive.value.designLoad || ''}墩</span>
              </div>
               <div class="ship-info" style="font-size: var(--font-size-content);color: #fff;display:flex;justify-content: space-between;margin-bottom:20px;">
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">通航等级: ${isActive.value.navigationGrade || ''}</span>
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">桥梁类型: ${isActive.value.type || ''}</span>
              </div>
              <div class="ship-info" style="font-size: var(--font-size-content);color: #fff;display:flex;justify-content: space-between;margin-bottom:20px;">
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">管理单位: ${isActive.value.deptName || ''}</span>
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">建造年限: ${isActive.value.buildDate ? isActive.value.buildDate.split(' ')[0] : ''}</span>
              </div>
              <div class="ship-info" style="font-size: var(--font-size-content);color: #fff;display:flex;justify-content: space-between;margin-bottom:20px;">
                <span style="display:inline-block;width:100%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">所属区域: ${isActive.value.region || ''}</span>
              </div>
              <div class="ship-info" style="font-size: var(--font-size-content);color: #fff;display:flex;margin-bottom:20px;">
                ${
                  isActive.value.fileUrl && isActive.value.fileUrl[0]
                    ? `<img style="width:300px;height:100px;object-fit:cover;" src="${isActive.value.fileUrl[0]}" alt="桥梁图片" />`
                    : `<div style="width:300px;height:100px;background:#2f3039;display:flex;justify-content:center;align-items:center;color:#fff;">暂无图片</div>`
                }
            </div>
      </div>
          `;
      addPoint([isActive.value.lon, isActive.value.lat], bridgeImg, 0.4, popupContent, bridgeId, isActive.value.bridgeName);
    }
  }
};
//鼠标移入事件
const showScrollButtons = () => {
  isHovering.value = true;
};

//鼠标移出事件
const hideScrollButtons = () => {
  isHovering.value = false;
};

//左移
const scrollLeft = () => {
  if (scrollContainer.value) {
    scrollContainer.value.scrollBy({
      left: -200,
      behavior: 'smooth'
    });
  }
};

//右移
const scrollRight = () => {
  if (scrollContainer.value) {
    scrollContainer.value.scrollBy({
      left: 200,
      behavior: 'smooth'
    });
  }
};

//桥的数据
const bridgeData = ref([]);

//获取桥梁长度数据
const getBridgeLengthsFc = async () => {
  const res = await getBridgeLengths({});
  if (res.code == 200) {
    bridgeData.value = res.data.map((item) => {
      return {
        ...item,
        fileUrl: JSON.parse(item.fileUrl)
      };
    });
  }
};

onMounted(() => {
  getBridgeLengthsFc();
});
</script>

<style lang="scss" scoped>
.Bridge {
  width: 37%;
  height: 103px;
  position: absolute;
  left: 56px;
  bottom: 44px;
  background: #34486a url('./images/bg.png') no-repeat center;
  background-size: 100%;
  display: flex;
  align-items: center;
  z-index: 1;
  &.collapsed {
    width: 135px;
    position: absolute;
  }
  .title {
    display: inline-block;
    height: 56px;
    width: 135px;
    line-height: 56px;
    text-align: center;
    color: #fff;
    font-size: var(--font-size-data-large);
    font-family: 'Microsoft YaHei';
    font-weight: bold;
    margin-left: 20px;
    cursor: pointer;
  }
  .content {
    position: relative;
    display: flex;
    flex: 1;
    color: #fff;
    font-family: 'Microsoft YaHei';
    font-size: var(--font-size-list-text);
    font-weight: bold;
    overflow: hidden;
    .qb {
      width: 154px;
      height: 56px;
      line-height: 56px;
      text-align: center;
      background: #566582;
      border-radius: 4px;
      cursor: pointer;
      margin: 0 10px;
    }
    .ql {
      width: calc(100% - 174px);
      width: 100%;
      height: 100%;
      position: relative;
      display: flex;
      overflow: hidden;
      .scroll-container {
        display: flex;
        overflow-x: auto;
        scrollbar-width: none;
        -ms-overflow-style: none;
        &::-webkit-scrollbar {
          display: none;
        }
      }

      .scroll-buttons {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        display: flex;
        justify-content: space-between;
        align-items: center;
        pointer-events: none;

        .scroll-btn {
          width: 30px;
          height: 56px;
          background: rgba(0, 0, 0, 0.5);
          color: #fff;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          pointer-events: auto;
          font-size: var(--font-size-content);
          transition: background-color 0.3s;

          &:hover {
            background: rgba(0, 0, 0, 0.7);
          }

          &.left {
            border-radius: 0 4px 4px 0;
          }

          &.right {
            border-radius: 4px 0 0 4px;
          }
        }
      }
    }
    .active {
      background: #34669e !important;
    }

    .box {
      flex-shrink: 0;
      min-width: 154px;
      width: auto;
      height: 56px;
      line-height: 56px;
      text-align: center;
      background: #566582;
      border-radius: 4px;
      cursor: pointer;
      margin: 0 10px;
    }
  }
  .reveal {
    display: flex;
    align-items: center;
    cursor: pointer;
    position: absolute;
    bottom: 50%;
    transform: translateY(50%);
    left: 100%;
    z-index: 1;
    img {
      width: 42px;
      height: 42px;
    }
    span {
      font-size: var(--font-size-content);
      color: #6ba3d6;
      font-family: 'Microsoft YaHei';
      white-space: nowrap;
    }
  }
}
</style>
