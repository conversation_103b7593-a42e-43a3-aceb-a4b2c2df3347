<template>
  <div :class="{ hidden: hidden }" class="custom-pagination-container">
    <div class="pagination-info">
      共 {{ total }} 条，每页
      <select v-model="pageSize" @change="handleSizeChange" class="page-size-select">
        <option v-for="size in pageSizes" :key="size" :value="size">{{ size }}</option>
      </select> 条
    </div>
    <div class="pagination-buttons">
      <button
        class="pagination-btn" :class="{ disabled: currentPage === 1 }"
        @click="handlePrevClick"
        :disabled="currentPage === 1"
      >
        上一页
      </button>
      <div class="page-numbers">
        <button
          v-for="num in visiblePages"
          :key="num"
          class="page-number-btn"
          :class="{ active: num === currentPage }"
          @click="handlePageClick(num)"
        >
          {{ num }}
        </button>
      </div>
      <button
        class="pagination-btn" :class="{ disabled: currentPage === totalPages }"
        @click="handleNextClick"
        :disabled="currentPage === totalPages"
      >
        下一页
      </button>
    </div>
    <div class="pagination-jump">
      前往
      <input
        type="number"
        v-model="jumpPage"
        min="1"
        :max="totalPages"
        class="jump-input"
      >
      页
      <button class="jump-btn" @click="handleJumpClick">确定</button>
    </div>
  </div>
</template>

<script lang="ts">
import { computed, ref, watch } from 'vue';
import { scrollTo } from '@/utils/scroll-to';
import { propTypes } from '@/utils/propTypes';

export default {
  name: 'CustomPagination'
};
</script>

<script setup lang="ts">
const props = defineProps({
  total: propTypes.number,
  page: propTypes.number.def(1),
  limit: propTypes.number.def(20),
  pageSizes: {
    type: Array,
    default: () => [10, 20, 30, 50]
  },
  // 页码按钮的数量
  pagerCount: propTypes.number.def(5),
  autoScroll: propTypes.bool.def(true),
  hidden: propTypes.bool.def(false)
});

const emit = defineEmits(['update:page', 'update:limit', 'pagination']);

const currentPage = ref(props.page);
const pageSize = ref(props.limit);
const jumpPage = ref(props.page);

// 计算总页数
const totalPages = computed(() => {
  return Math.ceil(props.total / pageSize.value) || 1;
});

// 计算可见页码
const visiblePages = computed(() => {
  const pages = [];
  const count = Math.min(props.pagerCount, totalPages.value);
  let start = Math.max(1, currentPage.value - Math.floor(count / 2));
  const end = Math.min(start + count - 1, totalPages.value);
  start = Math.max(1, end - count + 1);

  for (let i = start; i <= end; i++) {
    pages.push(i);
  }

  return pages;
});

// 监听page变化
watch(() => props.page, (newVal) => {
  currentPage.value = newVal;
  jumpPage.value = newVal;
});

// 监听limit变化
watch(() => props.limit, (newVal) => {
  pageSize.value = newVal;
});

// 处理页码变化
function handlePageClick(page: number) {
  if (page !== currentPage.value) {
    currentPage.value = page;
    jumpPage.value = page;
    emit('update:page', page);
    emit('pagination', { page, limit: pageSize.value });
    if (props.autoScroll) {
      scrollTo(0, 800);
    }
  }
}

// 处理上一页
function handlePrevClick() {
  if (currentPage.value > 1) {
    handlePageClick(currentPage.value - 1);
  }
}

// 处理下一页
function handleNextClick() {
  if (currentPage.value < totalPages.value) {
    handlePageClick(currentPage.value + 1);
  }
}

// 处理每页条数变化
function handleSizeChange() {
  const newSize = pageSize.value;
  const newPage = Math.min(currentPage.value, Math.ceil(props.total / newSize) || 1);
  currentPage.value = newPage;
  jumpPage.value = newPage;
  emit('update:limit', newSize);
  emit('update:page', newPage);
  emit('pagination', { page: newPage, limit: newSize });
  if (props.autoScroll) {
    scrollTo(0, 800);
  }
}

// 处理跳转
function handleJumpClick() {
  let page = parseInt(jumpPage.value.toString());
  if (isNaN(page) || page < 1) {
    page = 1;
  } else if (page > totalPages.value) {
    page = totalPages.value;
  }
  handlePageClick(page);
}
</script>

<style lang="scss" scoped>
.custom-pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  color: #fff;
  font-size: 24px;

  .pagination-info {
    display: flex;
    align-items: center;

    .page-size-select {
      margin: 0 8px;
      padding: 4px 8px;
      background-color: #262c3f;
      color: #fff;
      border: 1px solid #4e5969;
      border-radius: 4px;
      outline: none;
    }
  }

  .pagination-buttons {
    display: flex;
    align-items: center;

    .pagination-btn {
      padding: 6px 12px;
      background-color: #262c3f;
      color: #fff;
      border: 1px solid #4e5969;
      border-radius: 4px;
      cursor: pointer;
      margin: 0 4px;

      &:hover {
        background-color: #364158;
      }

      &.disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
    }

    .page-numbers {
      display: flex;

      .page-number-btn {
        width: 32px;
        height: 32px;
        background-color: #262c3f;
        color: #fff;
        border: 1px solid #4e5969;
        border-radius: 4px;
        cursor: pointer;
        margin: 0 4px;

        &:hover {
          background-color: #364158;
        }

        &.active {
          background-color: #0070f3;
          border-color: #0070f3;
        }
      }
    }
  }

  .pagination-jump {
    display: flex;
    align-items: center;

    .jump-input {
      width: 60px;
      margin: 0 8px;
      padding: 4px 8px;
      background-color: #262c3f;
      color: #fff;
      border: 1px solid #4e5969;
      border-radius: 4px;
      outline: none;
    }

    .jump-btn {
      padding: 4px 12px;
      background-color: #262c3f;
      color: #fff;
      border: 1px solid #4e5969;
      border-radius: 4px;
      cursor: pointer;

      &:hover {
        background-color: #364158;
      }
    }
  }
}

.custom-pagination-container.hidden {
  display: none;
}
</style>