/**
 * 悬浮窗视频播放器组件
 * 基于 VideoPlayer 核心逻辑，针对悬浮窗环境优化
 * 支持实时播放和回放功能，简化控制面板
 */
<template>
  <div class="floating-video-player">
    <!-- 视频播放容器 -->
    <div
      ref="videoContainer"
      class="video-element"
      @dblclick="handleDoubleClick"
    ></div>

    <!-- 加载状态 -->
    <div class="loading-overlay" v-if="isLoading">
      <div class="loading-spinner"></div>
      <span>加载中...</span>
    </div>

    <!-- 错误状态 -->
    <div class="error-overlay" v-if="hasError">
      <div class="error-icon">⚠</div>
      <span>{{ errorMessage }}</span>
      <button class="retry-btn" @click="retryPlay">重试</button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, onUnmounted, watch, computed } from 'vue';
import { getControlling } from '@/api/bridge/point';
import { getOptimizedConfig, getNetworkOptimizedConfig } from '@/utils/videoPlayerConfig';
import {
  addOptimizedEventListener,
  removeOptimizedEventListener
} from '@/utils/eventOptimizer';
import { useFloatingWindowStore } from '@/store/modules/floatingWindow';

/**
 * 组件属性定义
 */
interface Props {
  videoUrl: string | null; // 视频流地址
  deviceData: any; // 设备相关数据
  playbackTime?: string | null; // 回放时间点
  isPlaying?: boolean; // 是否正在播放
  isPlaybackMode?: boolean; // 是否处于回放模式
  showControls?: boolean; // 是否显示控制面板
  windowId?: string; // 悬浮窗ID，用于键盘事件控制
}

const props = withDefaults(defineProps<Props>(), {
  videoUrl: null,
  deviceData: () => ({}),
  playbackTime: null,
  isPlaying: true,
  isPlaybackMode: false,
  showControls: true,
  windowId: undefined
});

/**
 * 组件事件定义
 */
const emit = defineEmits<{
  (e: 'play'): void;
  (e: 'pause'): void;
  (e: 'error', error: any): void;
  (e: 'ready'): void;
}>();

/**
 * 声明全局 EasyPlayerPro 类型
 */
declare global {
  interface Window {
    EasyPlayerPro: any;
  }
}

// 组件状态管理
const videoContainer = ref<HTMLElement | null>(null);
const player = ref<any>(null);
const isVideoPlaying = ref(false);
const isLoading = ref(false);
const hasError = ref(false);
const errorMessage = ref('');
const playbackTimeInput = ref('');
let isDestroying = false;

// 悬浮窗管理器
const floatingWindowStore = useFloatingWindowStore();

// 计算属性
const deviceName = computed(() => {
  return props.deviceData?.label || props.deviceData?.name || props.deviceData?.cameraIndexCode || '未知设备';
});

const videoStatusText = computed(() => {
  if (hasError.value) return '播放错误';
  if (isLoading.value) return '加载中';
  if (isVideoPlaying.value) return '播放中';
  return '已暂停';
});

/**
 * 销毁播放器实例
 */
const destroyPlayer = () => {
  if (player.value && !isDestroying) {
    isDestroying = true;
    try {
      player.value.destroy();
    } catch (e) {
      console.warn('播放器销毁失败:', e);
    } finally {
      player.value = null;
      isDestroying = false;
    }
  }
};

/**
 * 初始化播放器
 */
const initPlayer = async () => {
  if (!videoContainer.value || !window.EasyPlayerPro) {
    console.warn('播放器容器或EasyPlayerPro未准备就绪');
    return;
  }

  // 销毁现有播放器
  destroyPlayer();

  try {
    // 使用优化配置创建播放器实例
    const config = getOptimizedConfig(props.isPlaybackMode);

    // 悬浮窗专用配置 - 优化延迟和网络请求
    const networkOptimizedConfig = getNetworkOptimizedConfig(props.isPlaybackMode);
    const floatingConfig = {
      ...networkOptimizedConfig,
      // 悬浮窗特定的延迟优化
      bufferTime: props.isPlaybackMode ? 0.5 : 0.3, // 减少缓冲时间，降低延迟
      videoBuffer: props.isPlaybackMode ? 150 : 100, // 减少视频缓冲区
      videoBufferDelay: props.isPlaybackMode ? 300 : 200, // 减少缓冲延迟
      // 网络请求优化
      heartTimeout: props.isPlaybackMode ? 180 : 90, // 悬浮窗进一步增加心跳超时
      timeout: props.isPlaybackMode ? 45 : 30, // 进一步增加连接超时
      networkDelay: props.isPlaybackMode ? 20000 : 15000, // 进一步增加网络延迟容忍度
      // 启用热键支持，允许键盘控制
      hotKey: true,
      supportDblclickFullscreen: false, // 禁用双击全屏，避免冲突
      // 简化控制面板
      operateBtns: {
        fullscreen: false, // 悬浮窗中禁用全屏按钮
        screenshot: false,
        stretch: false,
        play: false, // 使用自定义播放控制
        audio: true,
        record: false,
        ptz: true, // 启用PTZ控制，允许键盘控制摄像头
        quality: false,
        zoom: false,
        close: false,
        scale: false,
        logSave: false
      },
      hasControl: false, // 禁用内置控制栏，使用自定义控制
      controlAutoHide: true
    };

    player.value = new window.EasyPlayerPro(videoContainer.value, floatingConfig);

    // 设置播放器事件监听
    player.value.on('error', (e: any) => {
      console.error('悬浮窗播放器错误:', e);
      hasError.value = true;
      errorMessage.value = '视频播放失败';
      isLoading.value = false;
      emit('error', e);
    });

    player.value.on('ready', () => {
      hasError.value = false;
      isLoading.value = false;
      emit('ready');
    });

    player.value.on('play', () => {
      isVideoPlaying.value = true;
      hasError.value = false;
      isLoading.value = false; // 播放开始时清除加载状态
      emit('play');
    });

    player.value.on('pause', () => {
      isVideoPlaying.value = false;
      emit('pause');
    });

    player.value.on('loadstart', () => {
      isLoading.value = true;
      hasError.value = false;
    });

    player.value.on('loadeddata', () => {
      isLoading.value = false;
    });

    // 添加更多事件监听以确保加载状态正确更新
    player.value.on('canplay', () => {
      isLoading.value = false;
    });

    player.value.on('playing', () => {
      isVideoPlaying.value = true;
      isLoading.value = false;
    });

  } catch (e: any) {
    console.error('悬浮窗播放器初始化失败:', e);
    hasError.value = true;
    errorMessage.value = '播放器初始化失败';
    isLoading.value = false;
  }
};

/**
 * 播放视频
 */
const playVideo = async (url: string) => {
  if (!player.value || !url || isDestroying) return;

  try {
    isLoading.value = true;
    hasError.value = false;

    // 悬浮窗中减少等待时间，快速启动
    if (!player.value.isReady) {
      await new Promise((resolve) => setTimeout(resolve, 100));
    }

    await player.value.play(url);

    // 悬浮窗中减少加载超时时间
    const loadingTimeout = setTimeout(() => {
      if (isLoading.value) {
        console.warn('悬浮窗视频加载超时，强制清除加载状态');
        isLoading.value = false;
      }
    }, 3000); // 3秒超时

    // 实时模式快速跳到最新帧
    if (!props.isPlaybackMode && player.value.seekToLive) {
      setTimeout(() => {
        try {
          player.value.seekToLive();
          // 跳转后也清除加载状态
          clearTimeout(loadingTimeout);
          isLoading.value = false;
        } catch (e) {
          console.warn('跳转到最新帧失败:', e);
        }
      }, 500); // 减少等待时间
    } else {
      // 非实时模式，快速清除加载状态
      setTimeout(() => {
        clearTimeout(loadingTimeout);
        isLoading.value = false;
      }, 1000); // 减少等待时间
    }

  } catch (e: any) {
    console.error('悬浮窗视频播放失败:', e);
    hasError.value = true;
    errorMessage.value = '视频播放失败';
    isLoading.value = false;
  }
};

/**
 * 切换播放/暂停
 */
const togglePlayPause = async () => {
  if (!player.value || hasError.value) return;

  try {
    if (isVideoPlaying.value) {
      await player.value.pause();
    } else {
      await player.value.play();
    }
  } catch (e) {
    console.error('播放状态切换失败:', e);
  }
};

/**
 * 刷新视频
 */
const refreshVideo = async () => {
  if (props.videoUrl) {
    await initPlayer();
    await playVideo(props.videoUrl);
  }
};

/**
 * 重试播放
 */
const retryPlay = () => {
  hasError.value = false;
  errorMessage.value = '';
  refreshVideo();
};

/**
 * 处理回放时间变化
 */
const handlePlaybackTimeChange = () => {
  if (playbackTimeInput.value && player.value && props.isPlaybackMode) {
    // 这里需要根据具体的播放器SDK实现回放时间跳转
    console.log('回放时间变更:', playbackTimeInput.value);
  }
};

/**
 * 双击事件处理（在悬浮窗中可能需要不同的行为）
 */
const handleDoubleClick = () => {
  // 在悬浮窗中双击可以实现其他功能，比如切换控制面板显示
  console.log('悬浮窗视频双击');
};

/**
 * 处理键盘事件 - 云台控制
 * 只有当前窗口为活动窗口时才响应键盘事件
 * @param event 键盘事件对象
 */
const handleKeyDown = (event: KeyboardEvent) => {
  // 检查是否有设备数据
  if (!props.deviceData?.cameraIndexCode) return;

  // 检查当前窗口是否为活动窗口（只有活动窗口才响应键盘控制）
  if (props.windowId && floatingWindowStore.activeWindowId !== props.windowId) {
    return;
  }

  event.preventDefault(); // 阻止默认行为

  switch (event.key) {
    case 'ArrowUp':
      control('up');
      break;
    case 'ArrowDown':
      control('down');
      break;
    case 'ArrowLeft':
      control('left');
      break;
    case 'ArrowRight':
      control('right');
      break;
    case ' ': // 空格键停止
      control('stop');
      break;
  }
};

/**
 * 云台控制函数
 * @param direction 控制方向
 */
const control = (direction: string) => {
  if (!props.deviceData?.cameraIndexCode) {
    console.warn('缺少摄像头索引码，无法控制云台');
    return;
  }

  console.log(`云台控制: ${direction}`);

  // 发送开始控制命令
  getControlling({
    cameraIndexCode: props.deviceData.cameraIndexCode,
    action: 0,
    command: direction,
    speed: 40,
    presetIndex: 20
  }).then(() => {
    // 发送停止控制命令
    setTimeout(() => {
      getControlling({
        cameraIndexCode: props.deviceData.cameraIndexCode,
        action: 1,
        command: direction,
        speed: 40,
        presetIndex: 20
      });
    }, 100); // 100ms后停止
  }).catch(error => {
    console.error('云台控制失败:', error);
  });
};

// 监听视频URL变化
watch(
  () => props.videoUrl,
  async (newUrl) => {
    if (newUrl) {
      await initPlayer();
      await playVideo(newUrl);
    }
  }
);

// 监听播放模式变化
watch(
  () => props.isPlaybackMode,
  async () => {
    if (props.videoUrl) {
      await initPlayer();
      await playVideo(props.videoUrl);
    }
  }
);

// 监听播放状态变化
watch(
  () => props.isPlaying,
  async (newValue) => {
    if (player.value && !isDestroying) {
      try {
        if (newValue) {
          await player.value.play();
        } else {
          await player.value.pause();
        }
      } catch (e) {
        console.error('播放状态同步失败:', e);
      }
    }
  }
);

// 监听回放时间变化
watch(
  () => props.playbackTime,
  (newTime) => {
    if (newTime) {
      playbackTimeInput.value = newTime;
    }
  },
  { immediate: true }
);

// 组件挂载
onMounted(async () => {
  await initPlayer();
  if (props.videoUrl) {
    await playVideo(props.videoUrl);
  }

  // 添加键盘事件监听器
  document.addEventListener('keydown', handleKeyDown);
});

// 组件卸载
onUnmounted(() => {
  destroyPlayer();
  // 移除键盘事件监听器
  document.removeEventListener('keydown', handleKeyDown);
});
</script>

<style scoped>
.floating-video-player {
  position: relative;
  width: 100%;
  height: 100%;
  background: #000;
  border-radius: 4px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.device-info {
  position: absolute;
  top: 8px;
  left: 8px;
  right: 8px;
  z-index: 10;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(0, 0, 0, 0.7);
  padding: 6px 12px;
  border-radius: 4px;
  backdrop-filter: blur(4px);
}

.device-name {
  color: #fff;
  font-size: 14px;
  font-weight: bold;
  font-family: 'Microsoft YaHei';
}

.video-status {
  color: #ccc;
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 3px;
  background: rgba(255, 255, 255, 0.1);
}

.video-status.playing {
  color: #4CAF50;
  background: rgba(76, 175, 80, 0.2);
}

.video-status.error {
  color: #f44336;
  background: rgba(244, 67, 54, 0.2);
}

.video-element {
  flex: 1;
  width: 100%;
  height: 100%;
  background: #000;
  cursor: pointer;
}

.video-controls {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  padding: 12px;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.floating-video-player:hover .video-controls {
  transform: translateY(0);
}

.control-group {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.control-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 4px;
  color: #fff;
  padding: 6px 12px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.control-btn:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.05);
}

.control-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.play-pause-btn {
  font-size: 16px;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.refresh-btn {
  font-size: 14px;
}

.video-info {
  color: #ccc;
  font-size: 12px;
  margin-left: auto;
  display: flex;
  align-items: center;
  gap: 8px;
}

.keyboard-hint {
  color: #accbff;
  font-size: 14px;
  cursor: help;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.6; }
}

.playback-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.time-input {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 4px;
  color: #fff;
  padding: 4px 8px;
  font-size: 12px;
}

.time-input:focus {
  outline: none;
  border-color: #accbff;
}

.loading-overlay,
.error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #fff;
  z-index: 20;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid #accbff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 12px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-icon {
  font-size: 32px;
  color: #f44336;
  margin-bottom: 12px;
}

.retry-btn {
  background: #accbff;
  border: none;
  border-radius: 4px;
  color: #000;
  padding: 8px 16px;
  cursor: pointer;
  font-size: 14px;
  margin-top: 12px;
  transition: all 0.2s ease;
}

.retry-btn:hover {
  background: #9bb8ff;
  transform: scale(1.05);
}

/* 响应式设计 */
@media (max-width: 600px) {
  .device-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .control-group {
    flex-wrap: wrap;
  }

  .video-info {
    margin-left: 0;
    margin-top: 4px;
  }
}
</style>
