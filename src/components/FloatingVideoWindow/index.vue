/**
 * 悬浮窗视频窗口组件
 * 整合FloatingWindow和FloatingVideoPlayer
 * 提供完整的悬浮窗视频播放解决方案
 */
<template>
  <FloatingWindow :window-id="windowId">
    <template #default="{ windowData }">
      <FloatingVideoPlayer
        v-if="windowData && windowData.contentData"
        :video-url="windowData.contentData.url"
        :device-data="windowData.contentData.data"
        :playback-time="windowData.contentData.playbackTime"
        :is-playing="windowData.contentData.isPlaying"
        :is-playback-mode="windowData.contentData.isPlaybackMode"
        :show-controls="true"
        :window-id="windowId"
        @play="handleVideoPlay"
        @pause="handleVideoPause"
        @error="handleVideoError"
        @ready="handleVideoReady"
      />
    </template>
  </FloatingWindow>
</template>

<script setup lang="ts">
import { defineProps } from 'vue';
import FloatingWindow from '@/components/FloatingWindow/index.vue';
import FloatingVideoPlayer from '@/components/FloatingVideoPlayer/index.vue';

/**
 * 组件属性定义
 */
interface Props {
  windowId: string; // 悬浮窗ID
}

const props = defineProps<Props>();

/**
 * 组件事件定义
 */
const emit = defineEmits<{
  (e: 'video-play'): void;
  (e: 'video-pause'): void;
  (e: 'video-error', error: any): void;
  (e: 'video-ready'): void;
}>();

/**
 * 视频播放事件处理
 */
const handleVideoPlay = () => {
  emit('video-play');
};

/**
 * 视频暂停事件处理
 */
const handleVideoPause = () => {
  emit('video-pause');
};

/**
 * 视频错误事件处理
 */
const handleVideoError = (error: any) => {
  console.error('悬浮窗视频播放错误:', error);
  emit('video-error', error);
};

/**
 * 视频准备就绪事件处理
 */
const handleVideoReady = () => {
  emit('video-ready');
};
</script>

<style scoped>
/* 确保视频播放器填满整个窗口内容区域 */
:deep(.window-body) {
  padding: 0;
  height: calc(100% - 60px);
}
</style>
