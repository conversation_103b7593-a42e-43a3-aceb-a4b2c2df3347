<template>
  <Teleport to="body">
    <div
      v-if="windowData && windowData.visible"
      class="floating-window"
      :class="{
        'minimized': windowData.state === WindowState.MINIMIZED,
        'maximized': windowData.state === WindowState.MAXIMIZED,
        'dragging': isDragging,
        'resizing': isResizing
      }"
      :style="windowStyle"
      @mousedown="handleWindowMouseDown"
    >
      <!-- 窗口标题栏 -->
      <div
        class="window-header"
        @mousedown="handleHeaderMouseDown"
        @dblclick="toggleMaximize"
      >
        <span class="window-title">{{ windowData.title }}</span>
        <div class="window-controls">
          <button
            class="control-button minimize-button"
            @click="handleMinimize"
            title="最小化"
          >
            −
          </button>
          <button
            class="control-button maximize-button"
            @click="toggleMaximize"
            :title="windowData.state === WindowState.MAXIMIZED ? '还原' : '最大化'"
          >
            {{ windowData.state === WindowState.MAXIMIZED ? '❐' : '□' }}
          </button>
          <button
            class="control-button close-button"
            @click="handleClose"
            title="关闭"
          >
            ×
          </button>
        </div>
      </div>

      <!-- 窗口内容区域 -->
      <div class="window-body" v-show="windowData.state !== WindowState.MINIMIZED">
        <slot :window-data="windowData"></slot>
      </div>

      <!-- 调整大小控制点 -->
      <template v-if="windowData.resizable && windowData.state === WindowState.NORMAL">
        <div class="resize-handle resize-n" @mousedown="handleResizeStart('n', $event)"></div>
        <div class="resize-handle resize-s" @mousedown="handleResizeStart('s', $event)"></div>
        <div class="resize-handle resize-w" @mousedown="handleResizeStart('w', $event)"></div>
        <div class="resize-handle resize-e" @mousedown="handleResizeStart('e', $event)"></div>
        <div class="resize-handle resize-nw" @mousedown="handleResizeStart('nw', $event)"></div>
        <div class="resize-handle resize-ne" @mousedown="handleResizeStart('ne', $event)"></div>
        <div class="resize-handle resize-sw" @mousedown="handleResizeStart('sw', $event)"></div>
        <div class="resize-handle resize-se" @mousedown="handleResizeStart('se', $event)"></div>
      </template>
    </div>
  </Teleport>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, onUnmounted } from 'vue';
import { useFloatingWindowStore } from '@/store/modules/floatingWindow';
import { WindowState } from '@/types/floatingWindow';
import type { WindowPosition, WindowSize } from '@/types/floatingWindow';

interface Props {
  windowId: string;
}

const props = defineProps<Props>();

// 状态管理
const floatingWindowStore = useFloatingWindowStore();
const windowData = computed(() => floatingWindowStore.getWindow(props.windowId));

// 拖拽状态
const isDragging = ref(false);
const dragStartPos = ref<{ x: number; y: number }>({ x: 0, y: 0 });
const dragStartWindowPos = ref<WindowPosition>({ x: 0, y: 0 });

// 调整大小状态
const isResizing = ref(false);
const resizeDirection = ref<string>('');
const resizeStartPos = ref<{ x: number; y: number }>({ x: 0, y: 0 });
const resizeStartSize = ref<WindowSize>({ width: 0, height: 0 });
const resizeStartWindowPos = ref<WindowPosition>({ x: 0, y: 0 });

// 计算窗口样式
const windowStyle = computed(() => {
  if (!windowData.value) return {};

  const { position, size, zIndex, state } = windowData.value;

  if (state === WindowState.MAXIMIZED) {
    return {
      position: 'fixed',
      left: '0px',
      top: '0px',
      width: '100vw',
      height: '100vh',
      zIndex: zIndex
    };
  }

  if (state === WindowState.MINIMIZED) {
    return {
      position: 'fixed',
      left: `${position.x}px`,
      top: `${position.y}px`,
      width: `${size.width}px`,
      height: '40px', // 只显示标题栏
      zIndex: zIndex
    };
  }

  return {
    position: 'fixed',
    left: `${position.x}px`,
    top: `${position.y}px`,
    width: `${size.width}px`,
    height: `${size.height}px`,
    zIndex: zIndex
  };
});

/**
 * 处理窗口鼠标按下事件（激活窗口）
 */
const handleWindowMouseDown = (event: MouseEvent) => {
  event.stopPropagation();
  floatingWindowStore.activateWindow(props.windowId);
};

/**
 * 处理标题栏鼠标按下事件（开始拖拽）
 */
const handleHeaderMouseDown = (event: MouseEvent) => {
  if (!windowData.value?.draggable || windowData.value.state === WindowState.MAXIMIZED) return;

  event.preventDefault();
  event.stopPropagation();

  isDragging.value = true;
  dragStartPos.value = { x: event.clientX, y: event.clientY };
  dragStartWindowPos.value = { ...windowData.value.position };

  document.addEventListener('mousemove', handleDragMove);
  document.addEventListener('mouseup', handleDragEnd);
};

/**
 * 处理拖拽移动
 */
const handleDragMove = (event: MouseEvent) => {
  if (!isDragging.value || !windowData.value) return;

  const deltaX = event.clientX - dragStartPos.value.x;
  const deltaY = event.clientY - dragStartPos.value.y;

  const newPosition = {
    x: Math.max(0, Math.min(window.innerWidth - windowData.value.size.width,
        dragStartWindowPos.value.x + deltaX)),
    y: Math.max(0, Math.min(window.innerHeight - 40,
        dragStartWindowPos.value.y + deltaY))
  };

  floatingWindowStore.updateWindow(props.windowId, { position: newPosition });
};

/**
 * 处理拖拽结束
 */
const handleDragEnd = () => {
  isDragging.value = false;
  document.removeEventListener('mousemove', handleDragMove);
  document.removeEventListener('mouseup', handleDragEnd);
};

/**
 * 开始调整大小
 */
const handleResizeStart = (direction: string, event: MouseEvent) => {
  if (!windowData.value?.resizable) return;

  event.preventDefault();
  event.stopPropagation();

  isResizing.value = true;
  resizeDirection.value = direction;
  resizeStartPos.value = { x: event.clientX, y: event.clientY };
  resizeStartSize.value = { ...windowData.value.size };
  resizeStartWindowPos.value = { ...windowData.value.position };

  document.addEventListener('mousemove', handleResizeMove);
  document.addEventListener('mouseup', handleResizeEnd);
};

/**
 * 处理调整大小移动
 */
const handleResizeMove = (event: MouseEvent) => {
  if (!isResizing.value || !windowData.value) return;

  const deltaX = event.clientX - resizeStartPos.value.x;
  const deltaY = event.clientY - resizeStartPos.value.y;

  let newSize = { ...resizeStartSize.value };
  let newPosition = { ...resizeStartWindowPos.value };

  const minSize = windowData.value.minSize || { width: 320, height: 240 };
  const maxSize = windowData.value.maxSize || { width: window.innerWidth, height: window.innerHeight };

  // 根据调整方向计算新尺寸和位置
  if (resizeDirection.value.includes('e')) {
    newSize.width = Math.max(minSize.width, Math.min(maxSize.width, resizeStartSize.value.width + deltaX));
  }
  if (resizeDirection.value.includes('w')) {
    const newWidth = Math.max(minSize.width, Math.min(maxSize.width, resizeStartSize.value.width - deltaX));
    const widthDiff = newWidth - resizeStartSize.value.width;
    newSize.width = newWidth;
    newPosition.x = resizeStartWindowPos.value.x - widthDiff;
  }
  if (resizeDirection.value.includes('s')) {
    newSize.height = Math.max(minSize.height, Math.min(maxSize.height, resizeStartSize.value.height + deltaY));
  }
  if (resizeDirection.value.includes('n')) {
    const newHeight = Math.max(minSize.height, Math.min(maxSize.height, resizeStartSize.value.height - deltaY));
    const heightDiff = newHeight - resizeStartSize.value.height;
    newSize.height = newHeight;
    newPosition.y = resizeStartWindowPos.value.y - heightDiff;
  }

  floatingWindowStore.updateWindow(props.windowId, { size: newSize, position: newPosition });
};

/**
 * 处理调整大小结束
 */
const handleResizeEnd = () => {
  isResizing.value = false;
  resizeDirection.value = '';
  document.removeEventListener('mousemove', handleResizeMove);
  document.removeEventListener('mouseup', handleResizeEnd);
};

/**
 * 切换最大化状态
 */
const toggleMaximize = () => {
  if (!windowData.value) return;

  if (windowData.value.state === WindowState.MAXIMIZED) {
    floatingWindowStore.restoreWindow(props.windowId);
  } else {
    floatingWindowStore.maximizeWindow(props.windowId);
  }
};

/**
 * 最小化窗口
 */
const handleMinimize = () => {
  floatingWindowStore.minimizeWindow(props.windowId);
};

/**
 * 关闭窗口
 */
const handleClose = () => {
  floatingWindowStore.closeWindow(props.windowId);
};

// 清理事件监听器
onUnmounted(() => {
  document.removeEventListener('mousemove', handleDragMove);
  document.removeEventListener('mouseup', handleDragEnd);
  document.removeEventListener('mousemove', handleResizeMove);
  document.removeEventListener('mouseup', handleResizeEnd);
});
</script>

<style scoped>
.floating-window {
  background: linear-gradient(141deg, rgba(40, 40, 61, 0.85) 0%, rgba(0, 0, 0, 0.57) 44%, rgba(4, 3, 9, 0) 100%);
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(172, 203, 255, 0.3);
  overflow: hidden;
  transition: all 0.2s ease;
  user-select: none;
}

.floating-window.dragging {
  transition: none;
  cursor: move;
}

.floating-window.resizing {
  transition: none;
}

.floating-window.maximized {
  border-radius: 0;
}

.floating-window.minimized {
  border-radius: 8px;
}

.window-header {
  padding: 12px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(172, 203, 255, 0.3);
  cursor: move;
  background: rgba(40, 40, 61, 0.2);
}

.window-title {
  font-size: 16px;
  font-weight: bold;
  color: #fff;
  font-family: 'Microsoft YaHei';
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.window-controls {
  display: flex;
  gap: 8px;
}

.control-button {
  width: 28px;
  height: 28px;
  border: none;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
  transition: all 0.2s ease;
}

.control-button:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.1);
}

.minimize-button:hover {
  background: rgba(255, 193, 7, 0.3);
}

.maximize-button:hover {
  background: rgba(40, 167, 69, 0.3);
}

.close-button:hover {
  background: rgba(220, 53, 69, 0.3);
}

.window-body {
  padding: 16px;
  height: calc(100% - 60px);
  overflow: auto;
}

/* 调整大小控制点 */
.resize-handle {
  position: absolute;
  background: transparent;
  z-index: 10;
}

.resize-n, .resize-s {
  left: 8px;
  right: 8px;
  height: 4px;
  cursor: ns-resize;
}

.resize-n {
  top: -2px;
}

.resize-s {
  bottom: -2px;
}

.resize-w, .resize-e {
  top: 8px;
  bottom: 8px;
  width: 4px;
  cursor: ew-resize;
}

.resize-w {
  left: -2px;
}

.resize-e {
  right: -2px;
}

.resize-nw, .resize-ne, .resize-sw, .resize-se {
  width: 8px;
  height: 8px;
}

.resize-nw {
  top: -2px;
  left: -2px;
  cursor: nw-resize;
}

.resize-ne {
  top: -2px;
  right: -2px;
  cursor: ne-resize;
}

.resize-sw {
  bottom: -2px;
  left: -2px;
  cursor: sw-resize;
}

.resize-se {
  bottom: -2px;
  right: -2px;
  cursor: se-resize;
}

/* 调整大小控制点悬停效果 */
.resize-handle:hover {
  background: rgba(172, 203, 255, 0.3);
}

/* 动画效果 */
.floating-window:not(.dragging):not(.resizing) {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 最小化状态样式 */
.floating-window.minimized .window-header {
  border-bottom: none;
}

/* 滚动条样式 */
.window-body::-webkit-scrollbar {
  width: 6px;
}

.window-body::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.window-body::-webkit-scrollbar-thumb {
  background: rgba(172, 203, 255, 0.3);
  border-radius: 3px;
}

.window-body::-webkit-scrollbar-thumb:hover {
  background: rgba(172, 203, 255, 0.5);
}
</style>
