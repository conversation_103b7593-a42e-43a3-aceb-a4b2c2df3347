/**
 * 悬浮窗管理器组件
 * 负责渲染和管理所有悬浮窗实例
 * 根据窗口内容类型选择合适的组件进行渲染
 */
<template>
  <div class="floating-window-manager">
    <!-- 渲染所有可见的悬浮窗 -->
    <template v-for="window in visibleWindows" :key="window.id">
      <!-- 视频播放器悬浮窗 -->
      <FloatingVideoWindow
        v-if="window.contentType === 'video-player'"
        :window-id="window.id"
        @video-play="handleVideoPlay(window)"
        @video-pause="handleVideoPause(window)"
        @video-error="handleVideoError(window, $event)"
        @video-ready="handleVideoReady(window)"
      />

      <!-- 其他类型的悬浮窗可以在这里添加 -->
      <FloatingWindow
        v-else
        :window-id="window.id"
      >
        <template #default="{ windowData }">
          <div class="unknown-content">
            <p>未知的窗口内容类型: {{ window.contentType }}</p>
            <pre>{{ JSON.stringify(windowData?.contentData, null, 2) }}</pre>
          </div>
        </template>
      </FloatingWindow>
    </template>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useFloatingWindowStore } from '@/store/modules/floatingWindow';
import FloatingWindow from '@/components/FloatingWindow/index.vue';
import FloatingVideoWindow from '@/components/FloatingVideoWindow/index.vue';
import type { FloatingWindow as FloatingWindowType } from '@/types/floatingWindow';

// 悬浮窗管理器
const floatingWindowStore = useFloatingWindowStore();

// 计算属性：获取所有可见的悬浮窗
const visibleWindows = computed(() => floatingWindowStore.visibleWindows);

/**
 * 视频播放事件处理
 */
const handleVideoPlay = (window: FloatingWindowType) => {
  // 可以在这里添加全局的视频播放处理逻辑
};

/**
 * 视频暂停事件处理
 */
const handleVideoPause = (window: FloatingWindowType) => {
  // 可以在这里添加全局的视频暂停处理逻辑
};

/**
 * 视频错误事件处理
 */
const handleVideoError = (window: FloatingWindowType, error: any) => {
  // 可以在这里添加全局的错误处理逻辑，比如显示通知
};

/**
 * 视频准备就绪事件处理
 */
const handleVideoReady = (window: FloatingWindowType) => {
  // 可以在这里添加全局的视频就绪处理逻辑
};
</script>

<style scoped>
.floating-window-manager {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none; /* 允许点击穿透到下层元素 */
  z-index: 1000; /* 确保悬浮窗在其他内容之上 */
}

/* 悬浮窗本身需要能够接收点击事件 */
.floating-window-manager :deep(.floating-window) {
  pointer-events: auto;
}

.unknown-content {
  padding: 16px;
  color: #666;
  text-align: center;
}

.unknown-content pre {
  background: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  text-align: left;
  font-size: 12px;
  overflow: auto;
  max-height: 200px;
}
</style>
