<template>
  <div class="FlowRate" :class="{ collapsed: !isContentVisible }">
    <div class="content" v-show="isContentVisible">
      <div class="box" v-for="(item, index) in flowDict" :key="item" :class="`box${index + 1}`">
        <div class="img"></div>
        <div class="list">
          <span>{{ item.dictLabel }}</span>
          <span>{{ item.dictValue }}</span>
        </div>
      </div>
    </div>
    <div class="title" @click="toggleContent">< 流量</div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { getFlowDict } from '@/api/bridge/home';

const isContentVisible = ref(true);

const toggleContent = () => {
  isContentVisible.value = !isContentVisible.value;
};

const flowDict = ref([]);

onMounted(() => {
  getFlowDict().then((res) => {
    if (res.code === 200) {
      flowDict.value = res.rows;
    }
  });
});
</script>

<style lang="scss" scoped>
.FlowRate {
  width: 24%;
  height: 102px;
  position: absolute;
  bottom: 44px;
  right: 32%;
  background: #0f0f18;
  display: flex;
  align-items: center;
  z-index: 1;
  &.collapsed {
    width: 135px;
  }
  .title {
    color: #fff;
    font-size: var(--font-size-data-large);
    font-family: 'Microsoft YaHei';
    font-weight: bold;
    margin-right: 29px;
    cursor: pointer;
  }
  .content {
    color: #fff;
    font-family: 'Microsoft YaHei';
    display: flex;
    flex: 1;
    justify-content: space-around;
    .box {
      display: flex;
      .img {
        width: 44px;
        height: 44px;
      }
      .list {
        display: flex;
        flex-direction: column;
        span:nth-child(1) {
          font-size: var(--font-size-data-medium);
        }
        span:nth-child(2) {
          font-size: var(--font-size-data-medium);
          font-weight: bold;
        }
      }
    }
    .box1 {
      background: url('@/components/FlowRate/images/l.png') no-repeat;
      box-sizing: 100%;
    }
    .box2 {
      background: url('@/components/FlowRate/images/h.png') no-repeat;
      box-sizing: 100%;
    }
    .box3 {
      background: url('@/components/FlowRate/images/hong.png') no-repeat;
      box-sizing: 100%;
    }
  }
}
</style>
