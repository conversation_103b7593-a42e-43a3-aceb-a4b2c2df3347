<template>
  <div id="base-map" class="map"></div>

  <!-- 执法船监控弹窗 -->
  <PopUp :visible="showMonitorModal" title="执法船监控" :width="'100%'" @update:visible="showMonitorModal = $event" class="PopUp1">
    <div class="modal-content">
      <VideoList :monitor-list="monitorList" :mmsi="selectedMmsi" />
    </div>
  </PopUp>
  <!-- 历史轨迹弹窗 -->
  <PopUp :visible="showModal2" title="历史轨迹" @update:visible="showModal2 = $event" class="PopUp2">
    <div class="modal-content">
      <div class="map-container" id="historyMapContainer"></div>
    </div>
  </PopUp>
</template>

<script setup lang="ts">
import { h, ref, onMounted, onBeforeUnmount, nextTick, watch } from 'vue';
import {
  getMapInstance,
  initMap,
  addPoint,
  getPointById,
  updatePointCoordinates,
  initFullscreenListener,
  handleFullscreenChange,
  createElectronicFence,
  cleanupEventListeners,
  clearTypeFeatures,
  clearTrack,
  connectTrackPoints,
  connectTrackPointsWithArrows,
  setViewToCoordinates
} from '@/utils/mapMethods';
import { getShipRealtime, getNavigationEnv, getUrlVideo, getBridgeRange, getHistoryIot, getRadarSensingData } from '@/api/bridge/point';
import shipImg from '@/assets/svg/ship.svg';
import chuan2Img from '@/assets/home/<USER>';
import sxtImg from '@/assets/forewarning/sxt.png';
import carImg from '@/assets/svg/car_danger.png';
import cheImg from '@/assets/home/<USER>';
import ldImg from '@/assets/home/<USER>';
import carGPS from '@/assets/svg/car_danger.svg';
import { createApp } from 'vue';
import PopUp from '@/components/PopUp/index.vue';
import VideoList from '@/components/VideoList/index.vue';

// 定时器
let timer: any | null = null;
let retryCount = 0;
const MAX_RETRIES = 5;
const TIMEOUT_DURATION = 600000; // 30秒
// 地图相关
let map: any = null;

// 初始化地图
const initHistoryMap = () => {
  return new Promise((resolve) => {
    setTimeout(() => {
      try {
        // 每次打开都重新初始化地图
        map = initMap('historyMapContainer');
        // 确保地图容器有正确的尺寸
        map.updateSize();
        resolve(map);
      } catch (error) {
        resolve(null);
      }
    }, 100);
  });
};
const showModal2 = ref(false);

// 防抖函数
const debounce = (func: Function, wait: number) => {
  let timeout: NodeJS.Timeout;
  return function executedFunction(...args: any[]) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

// 监控弹窗相关
const showMonitorModal = ref(false);
const monitorList = ref([]);
const selectedMmsi = ref<string | null>(null);

//获取执法船点位数据
const getShipRealtimeFc = async () => {
  const res: any = await getShipRealtime({});
  if (res.code === 200) {
    res.data.forEach((item: any) => {
      let pointImg = '';
      let salace = 0;
      const pointId = `point_${item.mmsi}`;
      if (
        item.mmsi == '413435150' ||
        item.mmsi == '413234960' ||
        item.mmsi == '413304390' ||
        item.mmsi == '413288130' ||
        item.mmsi == '414403740' ||
        item.mmsi == '413366040'
      ) {
        pointImg = shipImg;
        salace = 0.1;
      } else {
        pointImg = '';
        salace = 1;
      }
      //判断有没有
      const existingPoint = getPointById(pointId);

      if (existingPoint) {
        // 更新点位坐标
        updatePointCoordinates(existingPoint, [item.lon, item.lat]);
      } else {
        // 弹窗内容
        const popupContent = `
          <div class="popup-title" style="min-width:460px;min-height:60px;display:flex;justify-content: space-between;align-items: center;background: linear-gradient(141deg, rgba(40, 40, 61, 0.85) 0%, rgba(0, 0, 0, 0.57) 44%, rgba(4, 3, 9, 0) 100%);padding:0px 25px;">
            <div style="display: flex; align-items: center; gap: 10px;">
              <span style="color:#fff;font-size:25px;font-weight: bold;">${item.name}</span>
              ${
                item.mmsi == '413435150' ||
                item.mmsi == '413234960' ||
                item.mmsi == '413304390' ||
                item.mmsi == '413288130' ||
                item.mmsi == '414403740' ||
                item.mmsi == '413366040'
                  ? '<img src="' +
                    sxtImg +
                    '" style="width: 30px; height: 30px; cursor: pointer;" title="查看监控" class="monitor-icon" data-mmsi="' +
                    item.mmsi +
                    '" />'
                  : ''
              }
            </div>
            <button class="popup-close" style="font-size:20px">X</button>
          </div>
          <div class="popup-content" style="width:auto;min-height:200px;background: linear-gradient(141deg, rgba(40, 40, 61, 0.85) 0%, rgba(0, 0, 0, 0.57) 44%, rgba(4, 3, 9, 0) 100%);padding:25px;border-top: 1px solid #accbff; ">
            <div class="ship-popup" style="min-width:700px;width:auto;height:auto;">
              <div class="ship-info" style="font-size: 20px;color: #fff;display:flex;justify-content: space-between;margin-bottom:20px;">
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">MMSI: ${item.mmsi}</span>
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">呼号:${item.callsign} </span>
              </div>
              <div class="ship-info" style="font-size: 20px;color: #fff;display:flex;justify-content: space-between;margin-bottom:20px;">
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">IMO: </span>
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">经度: ${item.lon},${item.lat}</span>
              </div>
              <div class="ship-info" style="font-size: 20px;color: #fff;display:flex;justify-content: space-between;margin-bottom:20px;">
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">类型:${handleShipType(item.shipType)} </span>
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">航速:${item.sog} </span>
              </div>
              <div class="ship-info" style="font-size: 20px;color: #fff;display:flex;justify-content: space-between;margin-bottom:20px;">
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">船长:${item.toBow + item.toStern} </span>
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">目的地:${item.destination} </span>
              </div>
               <div class="ship-info" style="font-size: 20px;color: #fff;display:flex;justify-content: space-between;margin-bottom:20px;">
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">船宽:${item.toPort + item.toStarboard} </span>
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">预到时间: </span>
              </div>
              <div class="ship-info" style="font-size: 20px;color: #fff;display:flex;justify-content: space-between;margin-bottom:20px;">
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">吃水: </span>
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">更新时间:${item.updateTime} </span>
              </div>
              <div class="ship-info" style="font-size: 20px;color: #fff;display:flex;justify-content: space-between;margin-bottom:20px;">
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">航向:${item.cog} </span>
                 <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;cursor: pointer;color:#5470c6" class="history-track" data-mmsi="${item.mmsi}" >查看历史轨迹</span>
              </div>
            </div>
          </div>
        `;
        // .toFixed(6)
        addPoint([item.lon, item.lat], pointImg, salace, popupContent, pointId, '', item);
      }
    });
  }
};

//获取电子围栏数据
const getNavigationEnvFc = async () => {
  const res = await getNavigationEnv();
  const res2 = await getBridgeRange(); //五个桥的在舶范围
  if (res.code == 200) {
    res.data.forEach((item: any) => {
      createElectronicFence({
        id: item.id,
        name: item.name,
        borderColor: item.borderColor,
        borderWidth: item.borderWidth,
        fillColor: item.fillColor,
        regions: item.regions
      });
    });
  }
  if (res2.code == 200) {
    res2.data.forEach((item) => {
      createElectronicFence({
        id: item.id,
        name: item.name,
        borderColor: item.borderColor,
        borderWidth: item.borderWidth,
        fillColor: item.fillColor,
        regions: item.regions
      });
    });
  }
};
//处理船类型
const handleShipType = (type: string) => {
  if (type == '1') {
    return '客船';
  } else if (type == '2') {
    return '货船';
  } else if (type == '3') {
    return '渔船';
  } else if (type == '4') {
    return '作业船';
  } else if (type == '5') {
    return '拖船';
  } else {
    return '其他';
  }
};

//获取雷达扫描数据
const getRadarSensingDataFc = async () => {
  const res = await getRadarSensingData();
  if (res.code == 200) {
    res.rows.forEach((item: any) => {
      const pointId = `pointLd_${item.sensingId}`;
      //判断有没有
      const existingPoint = getPointById(pointId);

      if (existingPoint) {
        // 更新点位坐标
        updatePointCoordinates(existingPoint, [item.longitude, item.latitude]);
      } else {
        // 弹窗内容
        //   const popupContent = `
        //     <div class="popup-title" style="min-width:700px;min-height:60px;display:flex;justify-content: space-between;align-items: center;background: linear-gradient(141deg, rgba(40, 40, 61, 0.85) 0%, rgba(0, 0, 0, 0.57) 44%, rgba(4, 3, 9, 0) 100%);padding:0px 25px;">
        //   <span style="color:#fff;font-size:25px;font-weight: bold;">${item.radarDeviceId}</span><button class="popup-close" style="font-size:20px">X</button>
        // </div>
        // <div class="popup-content" style="min-width:700px;min-height:200px;background: linear-gradient(141deg, rgba(40, 40, 61, 0.85) 0%, rgba(0, 0, 0, 0.57) 44%, rgba(4, 3, 9, 0) 100%);padding:25px;border-top: 1px solid #accbff; ">
        //   <div class="ship-popup" style="width:100%;height:auto;">
        //         <div class="ship-info" style="width:100%;font-size: 20px;color: #fff;display:flex;justify-content: space-between;margin-bottom:20px;">
        //           <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">感知时间: ${item.sensingTime || ''}</span>
        //           <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">经纬度: ${(item.longitude, item.latitude || '')}</span>
        //         </div>
        //       </div>
        // </div>
        //   `;
        // .toFixed(6)
        addPoint([item.longitude, item.latitude], ldImg, 0.8, '', pointId, '', item);
      }
    });
  }
};

//调用五次接口还是失败就不用调用了
const getRealtimeDataWithRetry = async () => {
  try {
    // 同时获取船舶和车辆数据
    await Promise.all([getShipRealtimeFc()]);
    // 如果成功，重置重试计数
    retryCount = 0;
    // 设置下一次调用
    timer = setTimeout(getRealtimeDataWithRetry, TIMEOUT_DURATION);
  } catch (error) {
    retryCount++;

    if (retryCount >= MAX_RETRIES) {
      // 达到最大重试次数，清除定时器
      if (timer) {
        clearTimeout(timer);
        timer = null;
      }
    } else {
      // 如果还没达到最大重试次数，继续尝试
      timer = setTimeout(getRealtimeDataWithRetry, TIMEOUT_DURATION);
    }
  }
};

// 获取监控列表数据
const getMonitorList = async () => {
  const res: any = await getUrlVideo();
  if (res) {
    monitorList.value = res || [];
  }
};

// 打开监控弹窗
const openMonitorModal = async () => {
  showMonitorModal.value = true;
  // 确保数据已加载
  if (monitorList.value.length === 0) {
    await getMonitorList();
  }
  // 数据加载完成后再设置mmsi，触发监听
  nextTick(() => {
    selectedMmsi.value = selectedMmsi.value;
  });
};

//查看历史轨迹
const openViewHistory = async (mmsi) => {
  showModal2.value = true;
  let salace = 0.5;
  try {
    await initHistoryMap();
    const res = await getHistoryIot({ mmsi });
    // 清除之前的轨迹
    clearTrack(`shipHistory${mmsi}`);
    // 存储点坐标的数组
    const points: [number, number][] = [];
    if (map) {
      // 添加历史轨迹点
      res.data.forEach((item, index) => {
        addPoint([item.lon, item.lat], '', salace, '', `historyship_${index}`, item.createTime, { isHistoryPoint: true, color: '#00ff00' });
        // 收集点坐标
        points.push([item.lon, item.lat]);
      });
      // 连接点坐标，添加箭头指向
      connectTrackPointsWithArrows(`shipHistory${mmsi}`, points, '#bfbfbf', 3, '#bfbfbf', 9);
      setViewToCoordinates([res.data[0].lon, res.data[0].lat]);
    }
  } catch (error) {
    console.error('初始化地图失败:', error);
  }
};

// 使用 onMounted 生命周期钩子
onMounted(() => {
  initMap('base-map');
  initFullscreenListener();
  getShipRealtimeFc();
  getNavigationEnvFc();
  getRadarSensingDataFc();
  timer = setTimeout(getRealtimeDataWithRetry, TIMEOUT_DURATION);

  // 添加事件委托监听
  document.addEventListener('click', handleMonitorIconClick);
  // 添加事件委托监听
  document.addEventListener('click', handleHistoryClick);
});

onBeforeUnmount(() => {
  try {
    if (timer) {
      clearTimeout(timer); // 清除定时器
      timer = null;
    }

    // 清理地图事件监听器
    cleanupEventListeners();

    // 移除全屏事件监听
    document.removeEventListener('fullscreenchange', handleFullscreenChange);
    document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);
    document.removeEventListener('mozfullscreenchange', handleFullscreenChange);
    document.removeEventListener('MSFullscreenChange', handleFullscreenChange);
    // 移除监控图标点击事件监听
    document.removeEventListener('click', handleMonitorIconClick);
    // 移除查看历史轨迹点击事件监听
    document.removeEventListener('click', handleHistoryClick);
  } catch (error) {
    console.warn('组件销毁时清理资源出错:', error);
  }
});

// 监听弹窗关闭
watch(showModal2, (newVal) => {
  if (!newVal) {
    // 弹窗关闭时清除地图上的历史轨迹要素
    clearTypeFeatures('historyship_');
    // 销毁地图实例
    if (map) {
      map.setTarget(undefined);
      map = null;
    }
  }
});

// 处理监控图标点击事件
const handleMonitorIconClick = (event: MouseEvent) => {
  const target = event.target as HTMLElement;
  if (target.classList.contains('monitor-icon')) {
    const mmsi = target.getAttribute('data-mmsi');
    if (mmsi) {
      // 先设置mmsi，再打开弹窗
      selectedMmsi.value = mmsi;
      openMonitorModal();
    }
  }
};

// 处理查看历史轨迹点击事件
const handleHistoryClick = (event: MouseEvent) => {
  const target = event.target as HTMLElement;
  if (target.classList.contains('history-track')) {
    const mmsi = target.getAttribute('data-mmsi');
    if (mmsi) {
      openViewHistory(mmsi);
    }
  }
};
</script>

<style scoped lang="scss">
.map {
  width: 100%;
  height: 100%;
  :deep(.dark-layer) {
    filter: contrast(102%) brightness(93%) saturate(103%) sepia(65%) grayscale(22%) invert(100%);
  }

  :deep(.monitor-icon) {
    transition: transform 0.3s;
    &:hover {
      transform: scale(1.2);
    }
  }
}
.PopUp1 {
  width: 100%;
  height: 100%;
  .modal-content {
    width: 100%;
    height: 100%;
  }
}
.PopUp2 {
  width: 70% !important;
  height: 100%;
  .modal-content {
    width: 100%;
    height: 100%;
    position: relative;
    overflow: hidden;
    .map-container {
      width: 100%;
      height: 100%;
      background: #1a1a1a;
      position: relative;
      z-index: 1;
      :deep(.dark-layer) {
        filter: contrast(102%) brightness(93%) saturate(103%) sepia(65%) grayscale(22%) invert(100%);
      }
    }
  }
}
</style>
