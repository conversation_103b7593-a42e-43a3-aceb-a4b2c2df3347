<template>
  <div class="mapRight">
    <!-- 将弹窗移到工具栏外部 -->
    <PopUp :visible="showSelectionResult" title="圈选船舶统计" @update:visible="showSelectionResult = $event" class="PopUp2">
      <div class="selection-result-content">
        <!-- 统计信息 -->
        <div class="statistics-section">
          <h3>统计信息</h3>
          <div class="stats-grid">
            <div class="stat-item">
              <span class="stat-label">总船舶数：</span>
              <span class="stat-value">{{ selectedShips.length }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">执法船：</span>
              <span class="stat-value">{{ lawEnforcementCount }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">其他船舶：</span>
              <span class="stat-value">{{ otherShipsCount }}</span>
            </div>
          </div>
        </div>

        <!-- 搜索框 -->
        <div class="search-section">
          <el-input v-model="searchMmsi" placeholder="请输入MMSI号进行搜索" prefix-icon="Search" clearable @input="filterShips" />
        </div>

        <!-- 船舶列表 -->
        <div class="ships-list-section">
          <h3>船舶列表</h3>
          <div class="ships-list">
            <div v-for="ship in filteredShips" :key="ship.mmsi" class="ship-item" @click="focusOnShip(ship)">
              <div class="ship-header">
                <span class="ship-name">{{ ship.name }}</span>
                <span class="ship-mmsi">MMSI: {{ ship.mmsi }}</span>
              </div>
              <div class="ship-details">
                <span>类型: {{ getShipTypeName(ship.shipType) }}</span>
                <span>航速: {{ ship.sog }} 节</span>
                <span>坐标: {{ ship.lon }}, {{ ship.lat }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </PopUp>
    <div class="box" @click="monitoringPoint"></div>
    <div class="box" @click="showPanel = true"></div>
    <div :title="`测距`" class="box" @click="startMeasure"></div>
    <div :title="`测面积`" class="box" @click="startAreaMeasure"></div>

    <!-- 圈选功能按钮 -->
    <div :title="`圈选船舶`" class="box selection-box" @click="toggleSelectionMenu">
      <!-- 选择窗口 -->
      <div v-if="showSelectionMenu" class="selection-menu" @click.stop>
        <div class="menu-item" @click="startCircleSelection">
          <i class="circle-icon"></i>
          圆形圈选
        </div>
        <div class="menu-item" @click="startRectSelection">
          <i class="rect-icon"></i>
          矩形圈选
        </div>
      </div>
    </div>

    <div class="box" @click="showModal2 = true"></div>
    <div class="box" @click="toggleFullscreen"></div>
    <!-- <div class="box"></div>
    <div class="box"></div>
    <div class="box"></div> -->
    <Panel v-if="showPanel" @close="showPanel = false" />
    <!-- 已移动到工具栏外部 -->

    <!-- 船舶类型弹窗 -->
    <PopUp :visible="showModal2" title="船舶类型" @update:visible="showModal2 = $event" class="PopUp1">
      <div class="modal-content">
        <div class="line">
          <div class="line-box">
            <div class="dot dot1"></div>
            客船
          </div>
          <div class="line-box">
            <div class="dot dot2"></div>
            货船
          </div>
        </div>
        <div class="line">
          <div class="line-box">
            <div class="dot dot3"></div>
            作业船
          </div>
          <div class="line-box">
            <div class="dot dot4"></div>
            拖船
          </div>
        </div>
        <div class="line">
          <div class="line-box">
            <div class="dot dot5"></div>
            渔船
          </div>
          <div class="line-box">
            <div class="dot"></div>
            其他
          </div>
        </div>
      </div>
    </PopUp>

    <!-- 感知设备弹窗 -->
    <PopUp :visible="isShow" title="感知设备" @update:visible="isShow = $event" class="PopUp3">
      <div class="modal-content">
        <div class="line">
          <div class="line-box">
            <div class="dot dot1"></div>
            AIS
          </div>
          <div class="line-box">
            <div class="dot dot2"></div>
            雷达
          </div>
        </div>
        <div class="line">
          <div class="line-box">
            <div class="dot dot3"></div>
            气象
          </div>
          <div class="line-box">
            <div class="dot dot4"></div>
            隧道
          </div>
        </div>
        <div class="line">
          <div class="line-box">
            <div class="dot dot5"></div>
            监控
          </div>
          <div class="line-box">
            <div class="dot dot6"></div>
            卡口
          </div>
        </div>
        <div class="line">
          <div class="line-box" style="width: 100%">
            <div class="dot dot7"></div>
            大桥健康监测传感器
          </div>
        </div>
      </div>
    </PopUp>
  </div>
</template>

<script setup lang="ts">
import Panel from '@/components/Panel/index.vue';
import PopUp from '@/components/PopUp/index.vue';
import { ref, computed, onMounted, onUnmounted, watch } from 'vue';
import {
  startMeasure,
  startAreaMeasure,
  toggleFullscreen,
  startShipSelection,
  endShipSelection,
  getPointById,
  updatePointCoordinates,
  addPoint,
  clearTypeFeatures
} from '@/utils/mapMethods';
import aisImg from '@/assets/point/ais.png';
import jkImg from '@/assets/point/jk.png';
import sdImg from '@/assets/point/sd.png';
import kkImg from '@/assets/point/kk.png';
import ldsbImg from '@/assets/point/ldsb.png';
import qxImg from '@/assets/point/qx.png';
import dqjkImg from '@/assets/point/dqjk.png';
import { getByType, getCheckpointDevice, getSensingData } from '@/api/bridge/home';
import csjk from './images/testjk.png';

const showPanel = ref(false);
const showModal2 = ref(true);

//感知设备
const isShow = ref(false);
const monitoringPoint = async () => {
  isShow.value = !isShow.value;
  if (isShow.value) {
    const [res1, res2, res3] = await Promise.all([
      getByType({}), // ais 监控 隧道
      getCheckpointDevice({}), // 卡口
      getSensingData({}) // 气象 雷达 大桥健康监测感知设备
    ]);
    //ais 监控 隧道
    if (res1.code == 200) {
      let pointImg = '';
      let popupContent = '';
      res1.data.forEach((item) => {
        const pointId = `perception_a${item.id}`;
        if (item.type == 1) {
          //ais
          pointImg = aisImg;
        } else if (item.type == 2) {
          //视频监控
          pointImg = jkImg;
          popupContent = `
            <div class="popup-title" style="min-width:460px;min-height:60px;display:flex;justify-content: space-between;align-items: center;background: linear-gradient(141deg, rgba(40, 40, 61, 0.85) 0%, rgba(0, 0, 0, 0.57) 44%, rgba(4, 3, 9, 0) 100%);padding:0px 25px;">
              <div style="display: flex; align-items: center; gap: 10px;">
                <span style="color:#fff;font-size:25px;font-weight: bold;">${item.deviceName || '未知'}</span>
              </div>
              <button class="popup-close" style="font-size:20px">X</button>
            </div>
            <div class="popup-content" style="width:auto;min-height:200px;background: linear-gradient(141deg, rgba(40, 40, 61, 0.85) 0%, rgba(0, 0, 0, 0.57) 44%, rgba(4, 3, 9, 0) 100%);padding:25px;border-top: 1px solid #accbff; ">
              <div class="ship-popup" style="min-width:700px;width:auto;height:auto;">
                <div class="ship-info" style="font-size: 20px;color: #fff;display:flex;justify-content: space-between;margin-bottom:20px;">
                    <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">设备状态: ${item.status}</span>
                   <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">设备编号: ${item.id}</span>

                </div>
                <div class="ship-info" style="font-size: 20px;color: #fff;display:flex;justify-content: space-between;margin-bottom:20px;">
                  <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">安装位置: ${item.installLocation}</span>
                  <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">经纬度:${item.lon},${item.lat} </span>
                </div>
                <div class="ship-info" style="font-size: 20px;color: #fff;display:flex;justify-content: space-between;margin-bottom:20px;">
                  <img style="width:600px;height:200px;object-fit:cover;" src="${csjk}" alt="" />
                </div>
              </div>
            </div>
          `;
        } else if (item.type == 3) {
          //隧道
          pointImg = sdImg;
        }
        // 检查是否已存在该点位
        const existingPoint = getPointById(pointId);
        if (existingPoint) {
          // 更新点位坐标
          updatePointCoordinates(existingPoint, [item.lon, item.lat]);
        } else {
          addPoint([item.lon, item.lat], pointImg, 1, popupContent, pointId, item.deviceName);
        }
      });
    }
    //卡口
    if (res2.code == 200) {
      res2.rows.forEach((item) => {
        const pointId = `perception_b${item.deviceId}`;
        // 检查是否已存在该点位
        const existingPoint = getPointById(pointId);
        if (existingPoint) {
          // 更新点位坐标
          updatePointCoordinates(existingPoint, [item.lon, item.lat]);
        } else {
          addPoint([item.lon, item.lat], kkImg, 1, '', pointId, item.deviceName, item);
        }
      });
    }
    //气象 雷达 大桥健康监测感知设备
    if (res3.code == 200) {
      let pointImg = '';
      res3.rows.forEach((item) => {
        if (item.type == 1) {
          //雷达
          pointImg = ldsbImg;
        } else if (item.type == 2) {
          //气象
          pointImg = qxImg;
        } else if (item.type == 3) {
          //健康监测传感器
          pointImg = dqjkImg;
        }
        const pointId = `perception_c${item.dataId}`;
        // 检查是否已存在该点位
        const existingPoint = getPointById(pointId);
        if (existingPoint) {
          // 更新点位坐标
          updatePointCoordinates(existingPoint, [item.lon, item.lat]);
        } else {
          addPoint([item.lon, item.lat], pointImg, 1, '', pointId, item.deviceCode);
        }
      });
    }
  } else {
    clearTypeFeatures('perception_'); //清除所有点位
  }
};

// 圈选功能相关状态
const showSelectionMenu = ref(false);
const showSelectionResult = ref(false);
const selectedShips = ref<any[]>([]);
const searchMmsi = ref('');
const filteredShips = ref<any[]>([]);

// 计算属性
const lawEnforcementCount = computed(() => {
  return selectedShips.value.filter((ship) => ['413435150', '413234960', '413304390', '413288130', '414403740', '413366040'].includes(ship.mmsi))
    .length;
});

const otherShipsCount = computed(() => {
  return selectedShips.value.length - lawEnforcementCount.value;
});

// 圈选功能方法
const toggleSelectionMenu = () => {
  showSelectionMenu.value = !showSelectionMenu.value;
};

const startCircleSelection = () => {
  showSelectionMenu.value = false;
  startShipSelection('Circle', handleSelectionResult);
};

const startRectSelection = () => {
  showSelectionMenu.value = false;
  startShipSelection('Rectangle', handleSelectionResult);
};

const handleSelectionResult = (ships: any[]) => {
  selectedShips.value = ships;
  filteredShips.value = ships;
  showSelectionResult.value = true;
};

const filterShips = () => {
  if (!searchMmsi.value.trim()) {
    filteredShips.value = selectedShips.value;
  } else {
    filteredShips.value = selectedShips.value.filter((ship) => ship.mmsi.toString().includes(searchMmsi.value.trim()));
  }
};

const focusOnShip = (ship: any) => {
  // 这里可以添加聚焦到船舶的逻辑
  console.log('Focus on ship:', ship);
};

const getShipTypeName = (type: string) => {
  const typeMap: { [key: string]: string } = {
    '1': '客船',
    '2': '货船',
    '3': '渔船',
    '4': '作业船',
    '5': '拖船'
  };
  return typeMap[type] || '其他';
};

// 处理键盘事件
const handleKeyDown = (event: KeyboardEvent) => {
  if (event.key === 'Escape') {
    if (showSelectionMenu.value) {
      showSelectionMenu.value = false;
    } else {
      toggleFullscreen();
    }
  }
};

// 点击外部关闭选择菜单
const handleClickOutside = (event: MouseEvent) => {
  const target = event.target as HTMLElement;
  if (!target.closest('.selection-box')) {
    showSelectionMenu.value = false;
  }
};

onMounted(() => {
  document.addEventListener('keydown', handleKeyDown);
  document.addEventListener('click', handleClickOutside);
});

onUnmounted(() => {
  // 移除键盘事件监听
  document.removeEventListener('keydown', handleKeyDown);
  document.removeEventListener('click', handleClickOutside);
  // 清理圈选功能
  endShipSelection();
});

// 监听感知设备弹窗关闭
watch(isShow, (newVal) => {
  if (!newVal) {
    // 弹窗关闭时清除点位
    clearTypeFeatures('perception_');
  }
});
</script>

<style lang="scss" scoped>
.mapRight {
  width: 60px;
  height: 612px;
  position: absolute;
  right: 32%;
  bottom: 11%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  z-index: 1;

  .box {
    width: 60px;
    height: 60px;
    border-radius: 4px;
    padding: 5px;
  }
  .box:nth-child(1) {
    background: #3f4146 url('./images/gzsb.png') no-repeat center;
    cursor: pointer;
  }
  .box:nth-child(2) {
    background: #3f4146 url('./images/dw.png') no-repeat center;
    cursor: pointer;
  }
  .box:nth-child(3) {
    background: #3f4146 url('./images/cj.png') no-repeat center;
    cursor: pointer;
  }
  .box:nth-child(4) {
    background: #3f4146 url('./images/4.png') no-repeat center;
    cursor: pointer;
  }

  /* 圈选按钮样式 */
  .selection-box {
    position: relative;
    background: #3f4146;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .selection-box::before {
    content: '🎯';
    font-size: 24px;
    color: #fff;
  }

  .box:nth-child(6) {
    background: #3f4146 url('./images/6.png') no-repeat center;
    cursor: pointer;
  }
  .box:nth-child(7) {
    background: #3f4146 url('./images/qp.png') no-repeat center;
    cursor: pointer;
  }
  .box:nth-child(8) {
    background: #3f4146 url('./images/7.png') no-repeat center;
    cursor: pointer;
  }
  .box:nth-child(9) {
    background: #3f4146 url('./images/8.png') no-repeat center;
    cursor: pointer;
  }
  .box:nth-child(10) {
    background: #3f4146 url('./images/9.png') no-repeat center;
    cursor: pointer;
  }

  /* 选择菜单样式 */
  .selection-menu {
    position: absolute;
    top: 0;
    left: -200px;
    width: 180px;
    background: rgba(63, 65, 70, 0.95);
    border-radius: 8px;
    padding: 10px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    z-index: 1000;
  }

  .selection-menu .menu-item {
    display: flex;
    align-items: center;
    padding: 12px;
    color: #fff;
    cursor: pointer;
    border-radius: 4px;
    transition: background-color 0.3s;
  }

  .selection-menu .menu-item:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }

  .selection-menu .menu-item:not(:last-child) {
    margin-bottom: 8px;
  }

  .selection-menu .circle-icon,
  .selection-menu .rect-icon {
    width: 20px;
    height: 20px;
    margin-right: 10px;
    border: 2px solid #fff;
    display: inline-block;
  }

  .selection-menu .circle-icon {
    border-radius: 50%;
  }

  .selection-menu .rect-icon {
    border-radius: 2px;
  }

  .PopUp1 {
    max-width: 400px !important;
    left: -355%;
    top: 76%;
    height: 300px;
    overflow: hidden;
    .modal-content {
      width: 100%;
      height: 100%;
      .line {
        width: 100%;
        height: 60px;
        display: flex;
        flex-wrap: wrap;
        .line-box {
          width: 50%;
          height: 100%;
          font-size: 25px;
          color: #fff;
          display: flex;
          align-items: center;
          .dot {
            width: 0;
            height: 0;
            border-left: 20px solid transparent;
            border-right: 20px solid transparent;
            border-bottom: 40px solid #02ff65;
            margin-right: 10px;
          }
          .dot1 {
            border-bottom: 40px solid #b91218;
          }
          .dot2 {
            border-bottom: 40px solid #eac507;
          }
          .dot3 {
            border-bottom: 40px solid #d84d2e;
          }
          .dot4 {
            border-bottom: 40px solid #d569b2;
          }
          .dot5 {
            border-bottom: 40px solid #29acd2;
          }
        }
      }
    }
  }

  .PopUp3 {
    max-width: 400px !important;
    left: -355%;
    top: 16% !important;
    overflow: hidden;
    .modal-content {
      width: 100%;
      height: 100%;
      .line {
        width: 100%;
        height: 60px;
        display: flex;
        flex-wrap: wrap;
        margin-bottom: 10px;
        .line-box {
          width: 50%;
          height: 100%;
          font-size: 25px;
          color: #fff;
          display: flex;
          align-items: center;
          .dot {
            width: 60px;
            height: 60px;
            margin-right: 10px;
          }
          .dot1 {
            background: url('@/assets/point/ais.png') no-repeat center;
            background-size: 100% 100%;
          }
          .dot2 {
            background: url('@/assets/point/ldsb.png') no-repeat center;
            background-size: 100% 100%;
          }
          .dot3 {
            background: url('@/assets/point/qx.png') no-repeat center;
            background-size: 100% 100%;
          }
          .dot4 {
            background: url('@/assets/point/sd.png') no-repeat center;
            background-size: 100% 100%;
          }
          .dot5 {
            background: url('@/assets/point/jk.png') no-repeat center;
            background-size: 100% 100%;
          }
          .dot6 {
            background: url('@/assets/point/kk.png') no-repeat center;
            background-size: 100% 100%;
          }
          .dot7 {
            background: url('@/assets/point/dqjk.png') no-repeat center;
            background-size: 100% 100%;
          }
        }
      }
    }
  }

  /* 圈选结果弹窗样式 */
  .PopUp2 {
    width: 1600px !important; /* 宽度设置为原来的两倍 */
    max-height: 90vh !important; /* 最大高度设置为视口的90% */
    height: 90vh !important; /* 高度设置为视口的90% */
    position: fixed !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    z-index: 1001 !important;
    margin: 0 !important;
  }

  .PopUp2 .selection-result-content {
    padding: 20px;
    max-height: 70vh;
    overflow-y: auto;
  }

  .PopUp2 .statistics-section {
    margin-bottom: 20px;
  }

  .PopUp2 .statistics-section h3 {
    color: #fff;
    margin-bottom: 15px;
    font-size: 18px;
  }

  .PopUp2 .stats-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
  }

  .PopUp2 .stat-item {
    background: rgba(255, 255, 255, 0.1);
    padding: 15px;
    border-radius: 8px;
    text-align: center;
  }

  .PopUp2 .stat-label {
    display: block;
    color: #ccc;
    font-size: 14px;
    margin-bottom: 5px;
  }

  .PopUp2 .stat-value {
    display: block;
    color: #fff;
    font-size: 24px;
    font-weight: bold;
  }

  .PopUp2 .search-section {
    margin-bottom: 20px;
  }

  .PopUp2 .ships-list-section h3 {
    color: #fff;
    margin-bottom: 15px;
    font-size: 18px;
  }

  .PopUp2 .ships-list {
    max-height: 400px;
    overflow-y: auto;
  }

  .PopUp2 .ship-item {
    background: rgba(255, 255, 255, 0.1);
    margin-bottom: 10px;
    padding: 15px;
    border-radius: 8px;
    cursor: pointer;
    transition: background-color 0.3s;
  }

  .PopUp2 .ship-item:hover {
    background: rgba(255, 255, 255, 0.2);
  }

  .PopUp2 .ship-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
  }

  .PopUp2 .ship-name {
    color: #fff;
    font-weight: bold;
    font-size: 16px;
  }

  .PopUp2 .ship-mmsi {
    color: #ccc;
    font-size: 14px;
  }

  .PopUp2 .ship-details {
    display: flex;
    gap: 20px;
    color: #ccc;
    font-size: 14px;
  }

  .PopUp2 .ship-details span {
    flex: 1;
  }
}
</style>
