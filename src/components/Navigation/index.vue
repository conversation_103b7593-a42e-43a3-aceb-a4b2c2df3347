<template>
  <div class="navigation">
    <el-menu
      active-text-color="#fff"
      class="el-menu-vertical-demo"
      :default-active="activeMenu"
      text-color="#d1d4d8"
      @open="handleOpen"
      @close="handleClose"
    >
      <el-menu-item index="1" @click="goNews('/home')">
        <el-icon><img class="imgBg" src="./images/dqgl.png" alt="" /></el-icon>
        <span>大桥概览</span>
      </el-menu-item>
      <el-menu-item index="2" @click="goNews('/perception')">
        <el-icon><img class="imgBg" src="./images/tsgz.png" alt="" /></el-icon>
        <span> 态势感知</span>
      </el-menu-item>
      <el-menu-item index="3" @click="goNews('/forewarning')">
        <el-icon><img class="imgBg" src="./images/jzyj.png" alt="" /></el-icon>
        <span>精准预警</span>
      </el-menu-item>
      <el-menu-item index="4" @click="goNews('/conduct')">
        <el-icon><img class="imgBg" src="./images/xtzh.png" alt="" /></el-icon>
        <span>协同指挥</span>
      </el-menu-item>
      <el-sub-menu index="5">
        <template #title>
          <el-icon><img class="imgBg" src="./images/wdzt.png" alt="" /></el-icon>
          <span>五大专题</span>
        </template>
        <el-menu-item index="5-1" @click="goNews('/dailyWork')">日常工作场景</el-menu-item>
        <el-menu-item index="5-2" @click="goNews('/flowRate')">大流量场景</el-menu-item>
        <el-menu-item index="5-3" @click="goNews('/emergency')">应急联动场景</el-menu-item>
        <el-menu-item index="5-4" @click="goNews('/secialVehicle')">特种车辆应用场景</el-menu-item>
        <el-menu-item index="5-5" @click="goNews('/bridgeArea')">涉桥海域监测场景</el-menu-item>
      </el-sub-menu>
    </el-menu>
  </div>
</template>

<script setup lang="ts">
import { useRouter, useRoute } from 'vue-router';
import { computed } from 'vue';

const router = useRouter();
const route = useRoute();

// 根据当前路由计算选中的菜单项
const activeMenu = computed(() => {
  const path = route.path;
  const menuMap = {
    '/home': '1',
    '/perception': '2',
    '/forewarning': '3',
    '/conduct': '4',
    '/dailyWork': '5-1',
    '/flowRate': '5-2',
    '/emergency': '5-3',
    '/secialVehicle': '5-4',
    '/bridgeArea': '5-5'
  };
  return menuMap[path] || '1';
});

//路由跳转
const goNews = (path) => {
  router.push(path);
};
const handleOpen = (key: string, keyPath: string[]) => {
  // console.log(key, keyPath);
};
const handleClose = (key: string, keyPath: string[]) => {
  // console.log(key, keyPath);
};
</script>

<style scoped lang="scss">
.navigation {
  width: 490px;
  position: absolute;
  left: 56px;
  top: 43px;
  z-index: 1;
  .el-menu {
    background: none;
    border: none;
    height: auto;
  }
  ::v-deep .el-menu--inline {
    background: linear-gradient(90deg, rgba(80, 111, 157, 0) 0%, rgba(18, 67, 136, 0.32) 49%, rgba(75, 103, 143, 0) 100%);
    .el-menu-item {
      padding-left: 136px;
      font-size: var(--font-size-menu);
      font-family: 'Microsoft YaHei';
      font-weight: bold;
    }
  }
  .is-active {
    background-color: #14161d !important;
  }
  .el-menu-item,
  .el-sub-menu {
    margin-bottom: 10px !important;
    background: linear-gradient(90deg, rgba(80, 111, 157, 0) 0%, rgba(18, 67, 136, 0.32) 49%, rgba(75, 103, 143, 0) 100%);
    span {
      font-size: var(--font-size-menu);
      font-family: 'Microsoft YaHei';
      font-weight: bold;
    }
  }
  .el-menu-item {
    height: 97px;
  }
  /* 移除鼠标悬停时的背景色 */
  .el-menu-item:hover {
    background-color: #14161d !important;
  }
  .el-sub-menu:hover {
    background-color: #14161d !important;
  }
  .imgBg {
    width: 56x;
    height: 56px;
  }
  .el-icon {
    margin-left: 92px;
    margin-right: 42px;
  }
}
</style>
<style>
.el-sub-menu .el-sub-menu__title:hover {
  background-color: #14161d !important;
}
.el-sub-menu__title {
  height: 96px;
}
.el-sub-menu .el-sub-menu__icon-arrow {
  font-size: var(--font-size-menu);
  right: 25%;
}
</style>
