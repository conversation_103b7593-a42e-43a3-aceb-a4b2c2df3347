/**
 * 网络状态指示器组件
 * 显示网络优化状态和请求队列信息
 */
<template>
  <div class="network-status" :class="{ 'expanded': isExpanded }">
    <!-- 状态指示器 -->
    <div class="status-indicator" @click="toggleExpanded">
      <div class="status-icon" :class="networkStatusClass">
        <span class="icon">{{ networkStatusIcon }}</span>
      </div>
      <div class="status-text" v-if="isExpanded">
        <span class="network-type">{{ networkStatusText }}</span>
        <span class="latency">{{ latencyText }}</span>
      </div>
    </div>

    <!-- 详细信息面板 -->
    <div class="details-panel" v-if="isExpanded">
      <!-- 请求队列状态 -->
      <div class="queue-status">
        <h4>请求队列状态</h4>
        <div class="queue-items">
          <div 
            v-for="(status, priority) in queueStatus" 
            :key="priority"
            class="queue-item"
            :class="getQueueItemClass(priority)"
          >
            <span class="priority-name">{{ getPriorityName(priority) }}</span>
            <div class="queue-stats">
              <span class="active">活跃: {{ status.active }}</span>
              <span class="queued">队列: {{ status.queued }}</span>
              <span class="max">最大: {{ status.maxConcurrent }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 网络统计 -->
      <div class="network-stats">
        <h4>网络统计</h4>
        <div class="stats-grid">
          <div class="stat-item">
            <span class="label">延迟</span>
            <span class="value">{{ Math.round(networkStatus.latency) }}ms</span>
          </div>
          <div class="stat-item">
            <span class="label">瓦片缓存</span>
            <span class="value">{{ optimizationStats.cacheSize }}</span>
          </div>
          <div class="stat-item">
            <span class="label">待处理瓦片</span>
            <span class="value">{{ optimizationStats.pendingTileRequests }}</span>
          </div>
        </div>
      </div>

      <!-- 控制按钮 -->
      <div class="controls">
        <button class="control-btn" @click="refreshStats">刷新统计</button>
        <button class="control-btn" @click="clearCache">清理缓存</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { 
  getNetworkOptimizationStats, 
  networkMonitor,
  mapTileThrottler 
} from '@/utils/networkOptimizer';

// 组件状态
const isExpanded = ref(false);
const networkStatus = ref({ latency: 0, isSlowNetwork: false, bandwidth: 0 });
const queueStatus = ref({});
const optimizationStats = ref({
  queueStatus: {},
  networkStatus: { latency: 0, isSlowNetwork: false, bandwidth: 0 },
  cacheSize: 0,
  pendingTileRequests: 0
});

let updateInterval: number | null = null;

// 计算属性
const networkStatusClass = computed(() => {
  if (networkStatus.value.isSlowNetwork) return 'slow';
  if (networkStatus.value.latency > 500) return 'medium';
  return 'good';
});

const networkStatusIcon = computed(() => {
  if (networkStatus.value.isSlowNetwork) return '🔴';
  if (networkStatus.value.latency > 500) return '🟡';
  return '🟢';
});

const networkStatusText = computed(() => {
  if (networkStatus.value.isSlowNetwork) return '慢速网络';
  if (networkStatus.value.latency > 500) return '中速网络';
  return '快速网络';
});

const latencyText = computed(() => {
  return `${Math.round(networkStatus.value.latency)}ms`;
});

/**
 * 切换展开状态
 */
const toggleExpanded = () => {
  isExpanded.value = !isExpanded.value;
};

/**
 * 获取优先级名称
 */
const getPriorityName = (priority: string): string => {
  const names: { [key: string]: string } = {
    'CRITICAL': '关键请求',
    'HIGH': '高优先级',
    'NORMAL': '普通请求',
    'LOW': '低优先级',
    'BACKGROUND': '后台请求'
  };
  return names[priority] || priority;
};

/**
 * 获取队列项样式类
 */
const getQueueItemClass = (priority: string): string => {
  const classes: { [key: string]: string } = {
    'CRITICAL': 'critical',
    'HIGH': 'high',
    'NORMAL': 'normal',
    'LOW': 'low',
    'BACKGROUND': 'background'
  };
  return classes[priority] || 'normal';
};

/**
 * 更新统计信息
 */
const updateStats = () => {
  try {
    const stats = getNetworkOptimizationStats();
    optimizationStats.value = stats;
    queueStatus.value = stats.queueStatus;
    networkStatus.value = stats.networkStatus;
  } catch (error) {
    console.warn('Failed to update network stats:', error);
  }
};

/**
 * 刷新统计
 */
const refreshStats = () => {
  updateStats();
  // 重新检测网络状态
  networkMonitor.detectNetworkStatus();
};

/**
 * 清理缓存
 */
const clearCache = () => {
  mapTileThrottler.clearCache();
  updateStats();
};

// 组件挂载
onMounted(() => {
  updateStats();
  // 每5秒更新一次统计信息
  updateInterval = window.setInterval(updateStats, 5000);
});

// 组件卸载
onUnmounted(() => {
  if (updateInterval) {
    clearInterval(updateInterval);
  }
});
</script>

<style scoped>
.network-status {
  position: fixed;
  top: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 8px;
  padding: 8px;
  color: #fff;
  font-size: 12px;
  z-index: 9999;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.network-status.expanded {
  width: 300px;
  padding: 16px;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  user-select: none;
}

.status-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

.status-icon.good {
  background: rgba(76, 175, 80, 0.2);
}

.status-icon.medium {
  background: rgba(255, 193, 7, 0.2);
}

.status-icon.slow {
  background: rgba(244, 67, 54, 0.2);
}

.status-text {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.network-type {
  font-weight: bold;
}

.latency {
  color: #ccc;
  font-size: 11px;
}

.details-panel {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.queue-status h4,
.network-stats h4 {
  margin: 0 0 8px 0;
  font-size: 13px;
  color: #accbff;
}

.queue-items {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.queue-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 8px;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.05);
}

.queue-item.critical {
  border-left: 3px solid #f44336;
}

.queue-item.high {
  border-left: 3px solid #ff9800;
}

.queue-item.normal {
  border-left: 3px solid #2196f3;
}

.queue-item.low {
  border-left: 3px solid #4caf50;
}

.queue-item.background {
  border-left: 3px solid #9e9e9e;
}

.priority-name {
  font-size: 11px;
  font-weight: bold;
}

.queue-stats {
  display: flex;
  gap: 8px;
  font-size: 10px;
}

.queue-stats span {
  color: #ccc;
}

.network-stats {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  gap: 2px;
  padding: 4px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
}

.stat-item .label {
  font-size: 10px;
  color: #ccc;
}

.stat-item .value {
  font-size: 12px;
  font-weight: bold;
  color: #accbff;
}

.controls {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  gap: 8px;
}

.control-btn {
  flex: 1;
  padding: 6px 12px;
  background: rgba(172, 203, 255, 0.2);
  border: none;
  border-radius: 4px;
  color: #fff;
  font-size: 11px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.control-btn:hover {
  background: rgba(172, 203, 255, 0.3);
  transform: scale(1.05);
}
</style>
