<template>
  <div class="coordinate-panel">
    <div class="coordinate-item">
      <span>经度：</span>
      <el-input v-model="longitude" readonly @click="copyValue(longitude)" />
    </div>
    <div class="coordinate-item">
      <span>纬度：</span>
      <el-input v-model="latitude" readonly @click="copyValue(latitude)" />
    </div>
    <div class="coordinate-buttons">
      <el-button type="primary" @click="pickCoordinate">拾取</el-button>
      <el-button type="success" @click="locateCoordinate">定位</el-button>
      <el-button type="danger" @click="$emit('close')">关闭</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue';
import { ElMessage } from 'element-plus';
import { getMapInstance, setViewToCoordinates } from '@/utils/mapMethods';

const longitude = ref('');
const latitude = ref('');
let map = null;
let pickHandler = null;

onMounted(() => {
  map = getMapInstance();
});

onBeforeUnmount(() => {
  if (map && pickHandler) {
    map.un('click', pickHandler);
  }
});

function pickCoordinate() {
  ElMessage.success('请点击地图拾取坐标');
  if (map && pickHandler) {
    map.un('click', pickHandler);
  }
  pickHandler = function (event) {
    const coord = map.getEventCoordinate(event.originalEvent);
    // console.log('test', coord, event.originalEvent);
    longitude.value = coord[0].toFixed(6);
    latitude.value = coord[1].toFixed(6);
    ElMessage.success('坐标拾取成功');
    map.un('click', pickHandler);
    pickHandler = null;
  };
  map.on('click', pickHandler);
}

function locateCoordinate() {
  if (!longitude.value || !latitude.value) {
    ElMessage.warning('请先拾取坐标');
    return;
  }
  setViewToCoordinates([parseFloat(longitude.value), parseFloat(latitude.value)]);
}

function copyValue(value) {
  if (!value) {
    ElMessage.warning('没有可复制的内容');
    return;
  }

  // 创建临时文本区域
  const textArea = document.createElement('textarea');
  textArea.value = value;
  textArea.style.position = 'fixed';
  textArea.style.left = '-999999px';
  textArea.style.top = '-999999px';
  document.body.appendChild(textArea);

  try {
    // 选择文本
    textArea.select();
    textArea.setSelectionRange(0, textArea.value.length);

    // 执行复制命令
    const successful = document.execCommand('copy');
    if (successful) {
      ElMessage.success('复制成功');
    } else {
      ElMessage.error('复制失败');
    }
  } catch (err) {
    ElMessage.error('复制失败');
  } finally {
    // 移除临时文本区域
    document.body.removeChild(textArea);
  }
}
</script>

<style scoped>
.coordinate-panel {
  position: absolute;
  top: 0;
  left: -506%;
  padding: 20px;
  border-radius: 6px;
  background: #14141e;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  width: 300px;
}
.coordinate-item {
  margin-bottom: 16px;
  display: flex;
  align-items: center;
}
.coordinate-item span {
  width: 80px;
  flex-shrink: 0;
  color: #fff;
  font-size: var(--font-size-content);
}
.coordinate-buttons {
  display: flex;
  gap: 0px;
  margin-top: 10px;
}
.coordinate-buttons :deep(.el-button) {
  font-size: var(--font-size-content);
}
.coordinate-item :deep(.el-input__inner) {
  font-size: var(--font-size-content);
  height: 40px;
  cursor: pointer;
}
.coordinate-item :deep(.el-input-group__append) {
  padding: 0;
  background-color: transparent;
}
.coordinate-item :deep(.el-input-group__append .el-button) {
  border: none;
  padding: 0 15px;
  height: 40px;
  font-size: var(--font-size-content);
}
</style>
