<template>
  <div class="modal" :style="{ width: typeof width === 'number' ? width + 'px' : width }" ref="modalRef" v-if="visible">
    <div class="modal-header">
      <span>{{ title }}</span>
      <button class="close-button" @click="close">×</button>
    </div>
    <div class="modal-body">
      <slot></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, defineProps, defineEmits, nextTick } from 'vue';

const props = defineProps({
  title: {
    type: String,
    default: '弹窗标题'
  },
  visible: {
    type: Boolean,
    default: false
  },
  width: {
    type: [String, Number],
    default: '1300px'
  }
});

const emits = defineEmits(['update:visible']);

const close = () => {
  // 使用nextTick确保状态更新的可靠性
  nextTick(() => {
    emits('update:visible', false);
  });
};

const modalRef = ref<HTMLElement | null>(null);
</script>

<style scoped>
.modal {
  /* background: linear-gradient(141deg, rgba(40, 40, 61, 0.85) 0%, rgba(0, 0, 0, 0.57) 44%, rgba(4, 3, 9, 0) 100%); */
  background: #14141e;
  border-radius: 50px;
  max-width: 90%;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: absolute;
  z-index: 1000;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

.modal-header {
  padding: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #accbff;
}

.modal-header span {
  font-size: var(--font-size-popup-title);
  font-weight: var(--font-weight-bold);
  color: #fff;
  font-family: 'Microsoft YaHei';
  margin-left: 20px;
}

.close-button {
  background: none;
  border: none;
  font-size: var(--font-size-popup-title);
  cursor: pointer;
  color: #fff;
}

.modal-body {
  padding: 16px;
  height: calc(100% - 100px);
  font-size: var(--font-size-content);
  line-height: var(--line-height-normal);
  color: #fff;
}
</style>
