<template>
  <div class="table-container">
    <!-- 表格 -->
    <el-table
      :data="tableData"
      :max-height="'500'"
      style="width: 100%; height: 100%"
      v-loading="loading"
      element-loading-background="rgba(40, 40, 50, 0.5)"
      header-row-class-name="custom-header"
      :cell-style="{ color: '#fff', fontSize: '24px', fontFamily: 'Microsoft YaHei' }"
      class="custom-table"
      @selection-change="handleSelectionChange"
    >
      <!-- 多选列 -->
      <el-table-column v-if="props.multiSelect" type="selection" width="55" align="center" />
      <!-- 动态列 -->
      <el-table-column
        v-for="column in columns"
        :key="column.prop"
        :prop="column.prop"
        :label="column.label"
        :width="column.width"
        :align="column.align || 'center'"
      >
        <!-- 自定义列内容 -->
        <template #default="scope" v-if="column.slot">
          <slot :name="column.slot" :row="scope.row" :index="scope.$index"></slot>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-pagination
      class="pagination"
      background
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
      :page-size="pageSize"
      :current-page="currentPage"
      :pager-count="4"
      :page-sizes="[10, 20, 50, 100]"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';

// 定义表格列的接口
interface TableColumn {
  prop: string; // 列对应的字段名
  label: string; // 列标题
  width?: string | number; // 列宽度
  align?: 'left' | 'center' | 'right'; // 列对齐方式
  slot?: string; // 自定义插槽名称
}

// 定义组件的 Props
const props = defineProps({
  columns: {
    type: Array as () => TableColumn[],
    required: true
  },
  fetchData: {
    type: Function, // 用于获取数据的函数
    required: true
  },
  searchParams: {
    type: Object, // 搜索条件
    default: () => ({})
  },
  multiSelect: {
    type: Boolean,
    default: false
  }
});

// 表格数据
const tableData = ref<any[]>([]);

// 分页相关状态
const total = ref(0); // 总条数
const pageSize = ref(10); // 每页条数
const currentPage = ref(1); // 当前页码

// 加载状态
const loading = ref(false);

// 选中行
const selectedRows = ref<any[]>([]);
const emit = defineEmits(['update:selectedRows']);

const handleSelectionChange = (val: any[]) => {
  selectedRows.value = val;
  emit('update:selectedRows', val);
};

// 获取表格数据

const getTableData = async () => {
  try {
    loading.value = true;
    const res = await props.fetchData({
      ...props.searchParams, // 将搜索条件传递给 fetchData
      page: currentPage.value,
      pageSize: pageSize.value
    });
    console.log('获取表格数据成功:', res);
    tableData.value = res.data; // 假设返回的数据格式为 { data: [], total: 100 }
    total.value = res.total;
  } catch (error) {
    console.error('获取表格数据失败:', error);
  } finally {
    loading.value = false;
  }
};

// 分页大小改变
const handleSizeChange = (size: number) => {
  pageSize.value = size;
  currentPage.value = 1; // 重置为第一页
  getTableData();
};

// 当前页码改变
const handleCurrentChange = (page: number) => {
  currentPage.value = page;
  getTableData();
};

// 监听搜索条件变化
watch(
  () => props.searchParams,
  () => {
    currentPage.value = 1; // 重置为第一页
    getTableData();
  },
  { deep: true }
);

// 初始化时加载数据
onMounted(() => {
  getTableData();
});
</script>

<style scoped lang="scss">
.table-container {
  background: none;
  border-radius: 4px;
  padding: 20px;
  box-sizing: border-box;
  /* 去除表格所有边框 */
  :deep(.custom-table) {
    border: none !important;
  }
  :deep(.custom-table .el-table__cell::before),
  :deep(.custom-table .el-table__cell::after) {
    display: none !important; /* 去除单元格的伪元素边框 */
  }
  :deep(.custom-table .el-table__header-wrapper),
  :deep(.custom-table .el-table__body-wrapper),
  :deep(.custom-table .el-table__footer-wrapper) {
    border: none !important;
  }
  :deep(.custom-table .el-table__cell) {
    border: none !important;
  }
  :deep(.custom-table .el-table__header) {
    border: none !important;
  }

  :deep(.custom-table .el-table__body) {
    border: none !important;
  }

  :deep(.custom-table .el-table__row) {
    border: none !important;
  }
}
:deep(.custom-table th),
:deep(.custom-table td),
:deep(.custom-table tr) {
  border: none !important;
  height: 100px;
  line-height: 60px;
}

/* 表头样式 */
:deep(.custom-header th) {
  background: #383b49 !important;
  border: none !important;
  color: #fff;
  font-size: 24px;
  font-family: 'Microsoft YaHei';
  height: 100px;
}

:deep(.el-table tr) {
  background: linear-gradient(141deg, rgba(40, 40, 61, 0.85) 0%, rgba(0, 0, 0, 0.57) 44%, rgba(4, 3, 9, 0) 100%) !important;
  color: #fff;
  font-size: 24px;
  font-family: 'Microsoft YaHei';
  border: none !important;
    height: 100px;
}

/* 表格单元格样式 */
:deep(.el-table__body-wrapper) {
  overflow-y: auto; /* 支持滚动 */
  border: none !important;
    height: 100px;
}

:deep(.el-table__body-wrapper .el-table__cell) {
    height: 100px;
  background: transparent !important;
  border: none !important;
}

/* 分页样式 */
.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: center;
  :deep(.btn-prev),
  :deep(.btn-prev:disabled),
  :deep(.btn-next),
  :deep(.btn-next:disabled) {
    background: #4b4f54;
  }
}
:deep(.el-select__wrapper) {
  background: #4b4f54;
  span {
    color: #fff;
  }
}
:deep(.el-input__wrapper) {
  background: #4b4f54;
}
:deep(.el-pagination .el-select) {
  width: 96px;
}
:deep(.el-pagination__total),
:deep(.el-pagination__jump),
:deep(.el-pagination__sizes),
:deep(.el-pagination__prev),
:deep(.el-pagination__next),
:deep(.el-pagination__jump .el-input__inner) {
  color: #fff;
  font-size: 18px;
  font-family: 'Microsoft YaHei';
}

:deep(.el-pagination.is-background .el-pager li) {
  background-color: #4b4f54;
  color: #fff;
}

:deep(.el-pagination.is-background .el-pager li.is-active) {
  background-color: #409eff;
}
</style>
