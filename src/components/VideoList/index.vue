<template>
  <div class="VideoList">
    <div class="monitor-container">
      <!-- 左侧监控列表 -->
      <div class="monitor-list">
        <!-- 监控分组列表 -->
        <div class="con-list">
          <div v-for="group in monitorList" :key="group.label" class="monitor-group">
            <!-- 分组标题，可点击展开/收起 -->
            <div class="group-header" @click="toggleGroup(group)">
              <span class="group-icon" :class="{ 'is-expanded': group.expanded }">▶</span>
              {{ group.label }}
            </div>
            <!-- 分组下的监控列表 -->
            <div v-show="group.expanded" class="group-children">
              <div
                v-for="item in group.children"
                :key="item.cameraIndexCode"
                :class="[
                  'monitor-item',
                  {
                    active: selectedMonitor === item,
                    'is-selected': activeVideos.some((v) => v.cameraIndexCode === item.cameraIndexCode)
                  }
                ]"
                @click="handleSelectMonitor(item)"
              >
                {{ item.label }}
              </div>
            </div>
          </div>
        </div>
        <!-- 云台控制面板 -->
        <div class="con-btn">
          <div class="control-panel">
            <!-- 方向控制按钮 -->
            <div class="control-circle">
              <div class="control-arrow up" @click="control('up')">
                <i class="arrow-icon"></i>
              </div>
              <div class="control-arrow right" @click="control('right')">
                <i class="arrow-icon"></i>
              </div>
              <div class="control-arrow down" @click="control('down')">
                <i class="arrow-icon"></i>
              </div>
              <div class="control-arrow left" @click="control('left')">
                <i class="arrow-icon"></i>
              </div>
              <!-- 开始/停止控制按钮 -->
              <div class="control-center" :class="{ start: goStatus }" @click="control('GO')"></div>
            </div>
            <!-- 变焦控制按钮 -->
            <div class="sx" @click="control('ZOOM_OUT')"></div>
            <div class="fd" @click="control('ZOOM_IN')"></div>
          </div>
        </div>
      </div>

      <!-- 右侧视频区域 -->
      <div class="video-container">
        <!-- 视频网格，支持拖拽调整位置 -->
        <div class="video-grid" :style="gridStyle">
          <div
            v-for="(video, index) in activeVideos"
            :key="video.label"
            :class="[
              'video-cell',
              {
                current: selectedMonitor?.cameraIndexCode == video.cameraIndexCode
              }
            ]"
            @click="handleSelectVideo(video)"
            draggable="true"
            @dragstart="handleDragStart($event, video.label)"
            @dragover.prevent
            @drop="handleDrop($event, video.label)"
          >
            <div class="video-wrapper">
              <!-- 关闭按钮 -->
              <div class="close-btn" @click.stop="handleCloseVideo(video.label)">×</div>
              <!-- 视频控制面板：实时/回放切换和时间选择 -->
              <div class="video-control">
                <!-- 摄像头名称显示 -->
                <div class="camera-name">
                  {{ video.label }}
                </div>
                <div class="mode-switch">
                  <span :class="{ active: !video.isPlaybackMode }" @click.stop="toggleVideoMode(video, false)">实时</span>
<!--                  <span :class="{ active: video.isPlaybackMode }" @click.stop="toggleVideoMode(video, true)">回放</span>-->
                  <span @click.stop="routerHaiKang(video)">回放</span>
                </div>
                <!-- 回放时间选择器 -->
                <div class="time-picker" v-if="video.isPlaybackMode">
                  <el-date-picker
                    v-model="video.playbackTime"
                    type="datetime"
                    placeholder="选择回放时间"
                    format="YYYY-MM-DD HH:mm:ss"
                    value-format="YYYY-MM-DD HH:mm:ss"
                    :disabled-date="disabledDate"
                    :disabled-hours="disabledHours"
                    @change="(time) => handleVideoTimeChange(time, video)"
                    size="large"
                  />
                </div>
              </div>
              <!-- 暂无回放提示 -->
              <div v-if="video.isPlaybackMode && !video.url" class="no-playback-tip">暂无回放</div>
              <!-- 视频播放器组件 -->
              <VideoPlayer
                :url="video.url"
                :data="video"
                :showDirectionControl="false"
                :width="'100%'"
                :height="'100%'"
                :playbackTime="video.isPlaybackMode ? video.playbackTime : null"
                :isPlaying="video.isPlaybackMode && video.isPlaying"
                :isPlaybackMode="video.isPlaybackMode"
                @play="() => handleVideoPlay(video)"
                @pause="() => handleVideoPause(video)"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import VideoPlayer from '@/components/VideoPlayer/index.vue';
import { getControlling, getBackUrl } from '@/api/bridge/point';
import { ElMessage } from 'element-plus';

/**
 * 视频对象接口定义
 */
interface Video {
  id?: string; // 视频ID
  label: string; // 视频标签/名称
  cameraIndexCode: string | null; // 摄像头索引码
  url: string | null; // 视频流地址
  mmsi: string | null; // 船舶MMSI号
  children?: Video[]; // 子视频列表（用于分组）
  expanded?: boolean; // 是否展开（用于分组）
  isPlaybackMode?: boolean; // 是否处于回放模式
  playbackTime?: string | null; // 回放时间
  isPlaying?: boolean; // 是否正在播放
}

/**
 * 组件属性定义
 */
const props = defineProps<{
  monitorList: Video[]; // 监控列表数据
  mmsi?: string; // 当前选中的船舶MMSI号
}>();

// 组件状态管理
const selectedMonitor = ref<Video | null>(null); // 当前选中的监控
const goStatus = ref(false); // 云台控制状态
const activeVideos = ref<Video[]>([]); // 当前显示的视频列表
let draggedName = ''; // 拖拽中的视频名称

/**
 * 初始化分组展开状态
 */
props.monitorList.forEach((group) => {
  group.expanded = true;
});

/**
 * 处理拖拽开始事件
 * @param event 拖拽事件对象
 * @param label 被拖拽视频的标签
 */
const handleDragStart = (event: DragEvent, label: string) => {
  draggedName = label;
  if (event.dataTransfer) {
    event.dataTransfer.effectAllowed = 'move';
    event.dataTransfer.setData('text/plain', label);
  }
  if (event.target instanceof HTMLElement) {
    event.target.style.opacity = '0.8';
  }
};

/**
 * 处理拖拽放置事件
 * @param event 拖拽事件对象
 * @param targetLabel 目标视频的标签
 */
const handleDrop = (event: DragEvent, targetLabel: string) => {
  event.preventDefault();
  if (draggedName === targetLabel) return;

  // 交换视频位置
  const newVideos = [...activeVideos.value];
  const fromIndex = newVideos.findIndex((v) => v.label === draggedName);
  const toIndex = newVideos.findIndex((v) => v.label === targetLabel);

  if (fromIndex !== -1 && toIndex !== -1) {
    const temp = newVideos[fromIndex];
    newVideos[fromIndex] = newVideos[toIndex];
    newVideos[toIndex] = temp;
    activeVideos.value = newVideos;
  }

  // 重置拖拽状态
  draggedName = '';
  if (event.target instanceof HTMLElement) {
    event.target.style.opacity = '1';
  }
};

/**
 * 监听MMSI和监控列表变化
 * 当MMSI变化时，自动加载对应船舶的所有监控
 */
watch(
  [() => props.mmsi, () => props.monitorList],
  ([newMmsi, newMonitorList]) => {
    if (!newMmsi || !newMonitorList.length) return;

    // 清空当前选中的视频
    activeVideos.value = [];
    selectedMonitor.value = null;

    // 遍历所有分组，找到对应mmsi的所有监控
    newMonitorList.forEach((group) => {
      if (group.mmsi === newMmsi && group.children) {
        group.expanded = true;
        group.children.forEach((monitor) => {
          if (monitor.cameraIndexCode) {
            // 初始化视频控制属性
            const newVideo = {
              ...monitor,
              isPlaybackMode: false,
              playbackTime: null,
              isPlaying: false
            };
            activeVideos.value.push(newVideo);
          }
        });
      }
    });

    // 选中第一个监控
    if (activeVideos.value.length > 0) {
      selectedMonitor.value = activeVideos.value[0];
    }
  },
  { immediate: true, deep: true }
);

/**
 * 切换分组展开/收起状态
 * @param group 要切换状态的分组
 */
const toggleGroup = (group: Video) => {
  group.expanded = !group.expanded;
};

/**
 * 计算视频网格布局样式
 * 根据视频数量动态调整网格布局
 */
const gridStyle = computed(() => {
  const count = activeVideos.value.length;
  if (count <= 0) return {};

  let cols, rows;
  // 根据视频数量优化布局
  switch (count) {
    case 1:
      cols = 1;
      rows = 1;
      break;
    case 2:
      cols = 2;
      rows = 1;
      break;
    case 3:
      cols = 3;
      rows = 1;
      break;
    case 4:
      cols = 2;
      rows = 2;
      break;
    case 5:
    case 6:
      cols = 3;
      rows = 2;
      break;
    case 7:
    case 8:
    case 9:
      cols = 3;
      rows = 3;
      break;
    default:
      cols = Math.ceil(Math.sqrt(count * 1.5));
      rows = Math.ceil(count / cols);
  }

  return {
    gridTemplateColumns: `repeat(${cols}, 1fr)`,
    gridTemplateRows: `repeat(${rows}, 1fr)`
  };
});

/**
 * 处理监控选择事件
 * @param monitor 选中的监控对象
 */
const handleSelectMonitor = (monitor: Video) => {
  if (!monitor.cameraIndexCode) return;

  selectedMonitor.value = monitor;

  if (!activeVideos.value.find((v) => v.cameraIndexCode === monitor.cameraIndexCode)) {
    // 添加新监控时初始化控制属性
    const newVideo = {
      ...monitor,
      isPlaybackMode: false,
      playbackTime: null,
      isPlaying: false
    };
    activeVideos.value.push(newVideo);
  }
};

/**
 * 处理视频选择事件
 * @param video 选中的视频对象
 */
const handleSelectVideo = (video: Video) => {
  // 更新选中状态
  selectedMonitor.value = video;

  // 确保左侧列表中的对应项也被选中
  props.monitorList.forEach((group) => {
    if (group.children) {
      const monitor = group.children.find((m) => m.cameraIndexCode === video.cameraIndexCode);
      if (monitor) {
        // 确保分组是展开的
        group.expanded = true;
        // 更新左侧选中状态
        selectedMonitor.value = monitor;
      }
    }
  });
};

/**
 * 处理视频关闭事件
 * @param videoLabel 要关闭的视频标签
 */
const handleCloseVideo = (videoLabel: string) => {
  const videoToRemove = activeVideos.value.find((v) => v.label === videoLabel);
  if (videoToRemove) {
    activeVideos.value = activeVideos.value.filter((v) => v.label !== videoLabel);
    if (selectedMonitor.value?.label === videoLabel) {
      selectedMonitor.value = null;
    }
  }
};

/**
 * 处理云台控制
 * @param direction 控制方向
 */
const control = (direction) => {
  if (!selectedMonitor.value) {
    ElMessage.warning('请选择控制的监控');
    return;
  }

  if (direction === 'GO') {
    goStatus.value = !goStatus.value;
    // 发送开始/停止控制命令
    getControlling({
      cameraIndexCode: selectedMonitor.value.cameraIndexCode,
      action: goStatus.value ? 0 : 1,
      command: 'left',
      speed: 40,
      presetIndex: 20
    });
  } else {
    // 发送方向控制命令
    getControlling({
      cameraIndexCode: selectedMonitor.value.cameraIndexCode,
      action: 0,
      command: direction,
      speed: 40,
      presetIndex: 20
    }).then(() => {
      // 发送停止命令
      getControlling({
        cameraIndexCode: selectedMonitor.value.cameraIndexCode,
        action: 1,
        command: direction,
        speed: 40,
        presetIndex: 20
      });
    });
  }
};

/**
 * 禁用未来时间
 * @param time 要检查的时间
 */
const disabledDate = (time: Date) => {
  return time.getTime() > Date.now();
};

/**
 * 禁用未来小时
 */
const disabledHours = () => {
  const now = new Date();
  const hours = [];
  for (let i = now.getHours() + 1; i < 24; i++) {
    hours.push(i);
  }
  return hours;
};

/**
 * 切换视频的实时/回放模式
 * @param video 要切换模式的视频
 * @param isPlayback 是否切换到回放模式
 */
const toggleVideoMode = (video: Video, isPlayback: boolean) => {
  video.isPlaybackMode = isPlayback;
  if (!isPlayback) {
    // 退出回放模式时重置状态
    video.playbackTime = null;
    video.isPlaying = false;
    // 恢复实时播放地址
    const realtimeUrl = video.url?.replace('_back', '');
    if (realtimeUrl) {
      video.url = realtimeUrl;
    }
  }
};

/**
 * 处理视频时间变化
 * @param time 选择的时间
 * @param video 要更新的视频
 */
const handleVideoTimeChange = async (time: string | null, video: Video) => {
  if (time && video.cameraIndexCode) {
    try {
      // 计算开始和结束时间（结束时间是选择的时间，开始时间是24小时前）
      const endTime = new Date(time);
      // const beginTime = new Date(endTime.getTime() - 24 * 3600000); // 往前推24小时
      //往前推一分钟
      const beginTime = new Date(endTime.getTime() - 60000);
      const formatTime = (date: Date) => {
        return date.toISOString().replace('Z', '+08:00');
      };

      // 获取回放地址
      const response = await getBackUrl({
        cameraIndexCode: video.cameraIndexCode,
        beginTime: formatTime(beginTime),
        endTime: formatTime(endTime)
      });

      const newUrl = response?.data || response;

      if (newUrl) {
        video.url = newUrl;
        video.playbackTime = time;
        // 自动开始播放
        video.isPlaying = true;
      } else {
        // 如果没有获取到URL，直接显示暂无回放并返回
        video.url = null;
        video.isPlaying = false;
        ElMessage.warning('该时间段暂无回放');
        return;
      }
    } catch (error) {
      console.error('获取回放地址失败:', error);
      video.url = null;
      video.isPlaying = false;
      ElMessage.error('获取回放地址失败');
    }
  }
};

// 船舶名称到IP地址的映射
const shipUrlMapping: Record<string, string> = {
  '舟桥巡3号': '***********',
  '舟桥巡5号': '***********',
  '舟桥巡6号': '***********',
  '舟桥巡7号': '***********',
  '舟桥巡8号': '***********',
  '舟桥巡9号': '***********'
};

const routerHaiKang = (video: Video) => {
  const fullLabel = video.label;
  // 提取船舶名称前缀（如：舟桥巡6号-三民008-半球 -> 舟桥巡6号）
  const shipName = fullLabel.split('-')[0];
  const ip = shipUrlMapping[shipName];

  if (ip) {
    window.open(
      `http://${ip}/`,
      '_blank',
      "noopener=yes,noreferrer=yes"
    );
  } else {
    // 如果没有找到对应的IP，显示提示信息
    ElMessage.warning(`未找到船舶 ${shipName} 的回放地址`);
    console.warn(`未配置船舶 ${shipName} 的回放地址，完整标签：${fullLabel}，可用的船舶：`, Object.keys(shipUrlMapping));
  }
};

/**
 * 处理视频播放事件
 * @param video 正在播放的视频
 */
const handleVideoPlay = (video: Video) => {
  video.isPlaying = true;
};

/**
 * 处理视频暂停事件
 * @param video 暂停播放的视频
 */
const handleVideoPause = (video: Video) => {
  video.isPlaying = false;
};
</script>

<style lang="scss">
/* 全局样式，不使用 scoped */
.el-picker-panel {
  background: #fff !important;
  .el-date-picker__header .el-date-picker__header-label {
    font-size: 25px !important;
    width: auto !important;
    display: inline-block !important;
  }
  .el-picker-panel__content {
    font-size: 25px !important;
    background: #fff !important;

    .el-date-table {
      background: #fff !important;

      th {
        font-size: 25px !important;
        // padding: 8px 0 !important;
      }

      td {
        font-size: 25px !important;
        // padding: 8px 0 !important;

        .el-date-table-cell__text {
          font-size: 25px !important;
          width: auto !important;
          display: inline-block !important;
        }
      }
    }

    // .el-time-panel {
    //   background: #fff !important;

    //   .el-scrollbar {
    //     background: #fff !important;
    //   }

    //   .el-time-panel__content {
    //     font-size: 25px !important;
    //     background: #fff !important;
    //   }

    //   .el-time-spinner__item {
    //     font-size: 25px !important;
    //     height: 32px !important;
    //     line-height: 32px !important;
    //     color: #333 !important;
    //   }
    // }
  }

  .el-picker-panel__footer {
    padding: 8px 12px !important;
    font-size: 25px !important;
    background: #fff !important;
    border-top: 1px solid #eee !important;

    .el-button {
      font-size: 25px !important;
      padding: 8px 16px !important;
      width: auto !important;
      display: inline-block !important;
    }
  }
}
.el-time-panel {
  background: #fff !important;
  .el-scrollbar {
    background: #fff !important;
  }
  .el-time-spinner__item {
    // width: auto !important;
    // display: inline-block !important;
    font-size: 20px !important;
  }
}
</style>

<style scoped lang="scss">
.VideoList {
  width: 100%;
  height: 100%;

  .monitor-container {
    display: flex;
    width: 100%;
    height: 100%;
    gap: 20px;
    position: relative;
  }

  .monitor-list {
    width: 600px;
    height: 100%;
    border-radius: 4px;
    padding: 10px;
    overflow: hidden;
    .con-list {
      height: 70%;
      overflow-y: scroll;
      &::-webkit-scrollbar {
        width: 0px;
        height: 0px;
      }
    }
    .con-btn {
      width: 100%;
      height: 30%;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 20px;
      .control-panel {
        width: 300px;
        height: 300px;
        position: relative;
      }

      .control-circle {
        width: 100%;
        height: 100%;
        position: relative;
        background: rgba(0, 0, 0, 0.6);
        border-radius: 50%;
        border: 2px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
      }

      .control-arrow {
        position: absolute;
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        transition: all 0.3s;
        // border: 2px solid rgba(255, 255, 255, 0.3);

        &:hover {
          background: rgba(24, 144, 255, 1);
          transform: scale(1.1);
        }

        &:active {
          transform: scale(0.95);
        }

        .arrow-icon {
          color: white;
          font-size: 24px;
          font-weight: bold;
        }

        &.up {
          top: 0;
          left: 50%;
          transform: translateX(-50%);
          background: url('@/assets/dailyWork/top.png') no-repeat center;
          background-size: 100% 100%;
        }

        &.right {
          top: 50%;
          right: 0;
          transform: translateY(-50%);
          background: url('@/assets/dailyWork/right.png') no-repeat center;
          background-size: 100% 100%;
        }

        &.down {
          bottom: 0;
          left: 50%;
          transform: translateX(-50%);
          background: url('@/assets/dailyWork/bottom.png') no-repeat center;
          background-size: 100% 100%;
        }

        &.left {
          top: 50%;
          left: 0;
          transform: translateY(-50%);
          background: url('@/assets/dailyWork/left.png') no-repeat center;
          background-size: 100% 100%;
        }
      }
      .sx {
        position: absolute;
        bottom: 0;
        left: -40px;
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        transition: all 0.3s;
        background: url('@/assets/dailyWork/sx.png') no-repeat center;
        background-size: 100% 100%;
      }
      .fd {
        position: absolute;
        bottom: 0;
        right: -40px;
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        transition: all 0.3s;
        background: url('@/assets/dailyWork/fd.png') no-repeat center;
        transform: rotate(90deg);
        background-size: 100% 100%;
      }
      .control-center {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 80px;
        height: 80px;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        transition: all 0.3s;
        background: red;

        &:hover {
          background: rgba(255, 0, 0, 1);
          transform: translate(-50%, -50%) scale(1.1);
        }

        &:active {
          transform: translate(-50%, -50%) scale(0.95);
        }

        span {
          color: white;
          font-size: 18px;
          font-weight: bold;
        }
      }
      .start {
        background: green;
      }
    }

    .monitor-group {
      margin-bottom: 10px;

      .group-header {
        padding: 10px;
        background: rgba(0, 0, 0, 0.3);
        border-radius: 4px;
        cursor: pointer;
        color: #fff;
        font-size: 35px;
        font-weight: bold;
        display: flex;
        align-items: center;
        transition: all 0.3s;

        &:hover {
          background: rgba(0, 0, 0, 0.5);
        }

        .group-icon {
          margin-right: 8px;
          transition: transform 0.3s;
          font-size: 28px;

          &.is-expanded {
            transform: rotate(90deg);
          }
        }
      }

      .group-children {
        padding-left: 20px;
        margin-top: 5px;
      }
    }

    .monitor-item {
      padding: 8px 10px;
      cursor: pointer;
      border-radius: 4px;
      margin-bottom: 5px;
      transition: all 0.3s;
      color: #fff;
      font-size: 30px;
      font-family: 'Microsoft YaHei';
      background: rgba(255, 255, 255, 0.1);

      &:hover {
        background: rgba(255, 255, 255, 0.2);
      }

      &.is-selected {
        background: rgba(24, 144, 255, 0.3);
        border-left: 3px solid #1890ff;
      }

      &.active {
        background: #1890ff;
        color: white;
        border-left: 3px solid #fff;
      }
    }
  }

  .video-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 10px;
  }

  .video-grid {
    height: 100% !important;
    flex: 1;
    display: grid;
    gap: 0;
    background: #000;
    padding: 0;
    border-radius: 4px;
    overflow: hidden;
  }

  .video-cell {
    // aspect-ratio: 16/9;
    background: #1a1a1a;
    position: relative;
    cursor: move;
    min-width: 0;
    max-height: 100%;
    border: 1px solid rgba(255, 255, 255, 0.1);

    .video-wrapper {
      width: 100%;
      height: 100%;
      position: relative;

      .video-control {
        position: absolute;
        top: 10px;
        left: 10px;
        z-index: 10;
        display: flex;
        gap: 15px;
        background: rgba(0, 0, 0, 0.7);
        padding: 8px 12px;
        border-radius: 6px;

        .camera-name {
          color: #fff;
          font-size: 18px;
          font-weight: 600;
          padding: 6px 12px;
          background: rgba(24, 144, 255, 0.8);
          border-radius: 4px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: 200px;
          display: flex;
          align-items: center;
        }

        .mode-switch {
          display: flex;
          gap: 8px;
          background: rgba(255, 255, 255, 0.1);
          padding: 4px;
          border-radius: 4px;

          span {
            padding: 6px 16px;
            color: #fff;
            cursor: pointer;
            border-radius: 4px;
            font-size: 20px;
            font-weight: 500;
            transition: all 0.3s;
            min-width: 60px;
            text-align: center;

            &:hover {
              background: rgba(255, 255, 255, 0.2);
            }

            &.active {
              background: #1890ff;
              font-weight: bold;
            }
          }
        }

        .time-picker {
          width: 320px;
          :deep(.el-input__wrapper) {
            background: rgba(255, 255, 255, 0.1);
            box-shadow: none;
            padding: 0 12px;
            height: 40px;
            width: 320px !important;
          }

          :deep(.el-input__inner) {
            color: #fff;
            font-size: 25px;
            height: 40px;
            line-height: 40px;
            width: 240px !important;
          }

          :deep(.el-input__prefix) {
            color: #fff;
            font-size: 25px;
          }

          :deep(.el-input__suffix) {
            color: #fff;
            font-size: 25px;
          }
        }
      }

      .no-playback-tip {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: rgba(0, 0, 0, 0.7);
        color: #fff;
        padding: 10px 20px;
        border-radius: 4px;
        font-size: 16px;
        z-index: 5;
      }
    }

    .close-btn {
      position: absolute;
      top: 10px;
      right: 10px;
      width: 32px;
      height: 32px;
      background: rgba(0, 0, 0, 0.6);
      color: #fff;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      z-index: 10;
      font-size: 60px;
      font-weight: bold;
      transition: all 0.3s;
      user-select: none;

      &:hover {
        background: rgba(255, 0, 0, 0.8);
        transform: scale(1.1);
      }
    }
  }
  .current {
    border: 4px solid #1890ff !important;
    box-sizing: border-box;
  }

  // 添加拖拽时的样式
  .video-cell[draggable='true']:active {
    opacity: 0.8;
    cursor: grabbing;
    border: 1px solid rgba(255, 255, 255, 0.3);
  }

  .video-cell[draggable='true']:hover {
    cursor: grab;
    border: 1px solid rgba(255, 255, 255, 0.2);
  }
}
</style>
