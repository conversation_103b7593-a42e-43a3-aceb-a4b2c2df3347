/** * 视频播放器组件 * 基于 EasyPlayerPro 实现的视频播放器，支持实时播放和回放功能 * 支持全屏模式下的键盘控制 */
<template>
  <div class="video-player-container">
    <!-- 视频播放容器 - 添加双击事件 -->
    <div
      ref="videoContainer"
      class="video-element"
      @dblclick="handleDoubleClick"
    ></div>
    <!-- 遮罩层已移除，避免影响视频播放 -->
    <!-- 方向控制面板（已注释） -->
    <!-- <div class="direction-control" v-if="showDirectionControl">
      <button @click="control('up')">上</button>
      <button @click="control('down')">下</button>
      <button @click="control('stop')">停止</button>
      <button @click="control('left')">左</button>
      <button @click="control('right')">右</button>
    </div> -->
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, onUnmounted, watch } from 'vue';
import { getControlling } from '@/api/bridge/point';
import {
  mergeConfig,
  performanceConfig,
  eventListenerConfig,
  getOptimizedConfig
} from '@/utils/videoPlayerConfig';
import {
  addOptimizedEventListener,
  removeOptimizedEventListener,
  createThrottledHandler
} from '@/utils/eventOptimizer';
import {
  networkMonitor,
  getNetworkOptimizationStats
} from '@/utils/networkOptimizer';
import { useFloatingWindowStore } from '@/store/modules/floatingWindow';

/**
 * 组件属性定义
 */
interface Props {
  url: string | null; // 视频流地址
  data: any; // 视频相关数据（包含摄像头信息等）
  showDirectionControl: boolean; // 是否显示方向控制面板
  width: string; // 播放器宽度
  height: string; // 播放器高度
  playbackTime?: string | null; // 回放时间点
  isPlaying?: boolean; // 是否正在播放
  isPlaybackMode?: boolean; // 是否处于回放模式
}

// 设置属性默认值
const props = withDefaults(defineProps<Props>(), {
  url: null,
  data: () => ({}),
  showDirectionControl: true,
  width: '100%',
  height: '100%',
  playbackTime: null,
  isPlaying: false,
  isPlaybackMode: false
});

/**
 * 组件事件定义
 */
const emit = defineEmits<{
  (e: 'play'): void; // 播放事件
  (e: 'pause'): void; // 暂停事件
}>();

/**
 * 声明全局 EasyPlayerPro 类型
 */
declare global {
  interface Window {
    EasyPlayerPro: any; // EasyPlayerPro 播放器实例类型
  }
}

// 组件状态管理
const videoContainer = ref(null); // 视频容器引用
const player = ref(null); // 播放器实例
const isFullscreen = ref(false); // 全屏状态
let playTimeout = null; // 播放超时定时器
let isDestroying = false; // 销毁状态标记

// 悬浮窗管理器
const floatingWindowStore = useFloatingWindowStore();

/**
 * 销毁播放器实例
 * 确保播放器资源被正确释放
 */
const destroyPlayer = async () => {
  if (!player.value || isDestroying) return;

  isDestroying = true;
  try {
    // 先停止播放
    try {
      await player.value.pause();
    } catch (e) {
      console.warn('暂停播放失败:', e);
    }

    // 等待一小段时间确保暂停完成
    await new Promise((resolve) => setTimeout(resolve, 100));

    // 销毁实例
    try {
      await player.value.destroy();
    } catch (e) {
      console.warn('播放器销毁失败:', e);
    }
  } finally {
    player.value = null;
    isDestroying = false;
  }
};

/**
 * 初始化播放器
 * 创建新的播放器实例并设置相关配置
 */
const initPlayer = async () => {
  // 如果正在销毁，等待销毁完成
  if (isDestroying) {
    await new Promise((resolve) => setTimeout(resolve, 200));
  }

  // 确保先销毁现有实例
  await destroyPlayer();

  // 检查播放器库是否加载
  if (!window.EasyPlayerPro) {
    console.error('播放器库未加载');
    return;
  }

  // 检查容器是否就绪
  if (!videoContainer.value || !videoContainer.value.isConnected) {
    console.error('视频容器未就绪');
    return;
  }

  try {
    // 使用优化配置创建播放器实例，包含事件处理优化
    const config = getOptimizedConfig(props.isPlaybackMode);

    // 可以在这里添加特定的自定义配置
    const customConfig = {
      // watermark: {
      //   text: { content: 'easyplayer-pro' },
      //   right: 10,
      //   top: 10
      // }
    };

    player.value = new window.EasyPlayerPro(videoContainer.value, {
      ...config,
      ...customConfig
    });

    // 设置播放器事件监听 - 优化性能监控
    player.value.on('error', (e: any) => {
      console.error('播放器错误:', e);
    });

    player.value.on('ready', () => {
    });

    // 添加播放相关事件监听
    player.value.on('play', () => {
      emit('play');
    });

    player.value.on('playing', () => {
    });

    player.value.on('loadstart', () => {
    });

    player.value.on('loadeddata', () => {
    });

    // 添加性能监控事件
    player.value.on('stats', (stats: any) => {
      // 监控播放统计信息，包括延迟、帧率等
      if (stats.delay && stats.delay > performanceConfig.delayWarningThreshold) {
        console.warn(`检测到高延迟: ${stats.delay}ms，超过阈值 ${performanceConfig.delayWarningThreshold}ms`);
      }
      if (stats.fps && stats.fps < performanceConfig.fpsWarningThreshold) {
        console.warn(`检测到低帧率: ${stats.fps}fps，低于阈值 ${performanceConfig.fpsWarningThreshold}fps`);
      }
    });

    player.value.on('videoInfo', (info: any) => {
    });

    // 监听缓冲事件
    player.value.on('buffer', (bufferInfo: any) => {
      if (bufferInfo.bufferTime && bufferInfo.bufferTime > performanceConfig.bufferWarningThreshold) {
        console.warn(`缓冲时间过长: ${bufferInfo.bufferTime}ms`);
      }
    });

    // 监听网络状态
    player.value.on('kBps', (bandwidth: any) => {
    });

    // 监听播放性能
    player.value.on('performance', (perf: any) => {
    });
  } catch (e: any) {
    console.error('播放器初始化错误:', e);
  }
};

/**
 * 播放视频 - 网络状态自适应播放
 * @param url 视频流地址
 */
const playVideo = async (url: string) => {
  if (!player.value || !url || isDestroying) return;

  try {
    // 检查网络状态
    const networkStatus = networkMonitor.getNetworkStatus();

    // 根据网络状态调整等待时间
    const waitTime = networkStatus.isSlowNetwork ? 1000 : 500;
    if (!player.value.isReady) {
      await new Promise((resolve) => setTimeout(resolve, waitTime));
    }

    // 记录播放开始时间，用于延迟监控
    const playStartTime = Date.now();

    await player.value.play(url);

    const playTime = Date.now() - playStartTime;

    // 输出网络优化统计信息
    const networkStats = getNetworkOptimizationStats();

    // 如果是实时模式且网络状况良好，尝试跳到最新帧
    if (!props.isPlaybackMode && player.value.seekToLive && !networkStatus.isSlowNetwork) {
      setTimeout(() => {
        try {
          player.value.seekToLive();
        } catch (e) {
          console.warn('跳转到最新帧失败:', e);
        }
      }, networkStatus.isSlowNetwork ? 3000 : 1500);
    }

  } catch (e: any) {
    console.error('视频播放失败:', e);
    // 播放失败时输出网络状态用于调试
    console.log('播放失败时的网络状态:', networkMonitor.getNetworkStatus());
  }
};

/**
 * 监听 URL 变化
 * 当 URL 变化时重新初始化播放器并播放视频
 */
watch(
  () => props.url,
  async (newUrl) => {
    if (newUrl) {
      await initPlayer();
      await playVideo(newUrl);
    }
  }
);

/**
 * 监听回放模式变化
 * 切换回放模式时重新初始化播放器
 */
watch(
  () => props.isPlaybackMode,
  async (newValue) => {
    if (props.url) {
      await initPlayer();
      await playVideo(props.url);
    }
  }
);

/**
 * 监听播放状态变化
 * 控制视频的播放和暂停
 */
watch(
  () => props.isPlaying,
  async (newValue) => {
    if (player.value && !isDestroying) {
      try {
        if (newValue) {
          await player.value.play();
        } else {
          await player.value.pause();
        }
      } catch (e: any) {
        console.error('播放状态切换失败:', e);
      }
    }
  }
);

/**
 * 监听回放时间变化
 * 处理回放时间点的跳转
 */
watch(
  () => props.playbackTime,
  (newTime) => {
    if (newTime && player.value) {
      // 需要根据具体的播放器 SDK 来实现回放功能
      // 例如，如果使用海康威视的播放器：
      // player.value.seek(newTime);
      // player.value.play();
    }
  }
);

/**
 * 处理键盘事件
 * 在全屏模式下响应方向键控制
 * @param event 键盘事件对象
 */
const handleKeyDown = (event) => {
  if (!isFullscreen.value) return;

  switch (event.key) {
    case 'ArrowUp':
      control('up');
      break;
    case 'ArrowDown':
      control('down');
      break;
    case 'ArrowLeft':
      control('left');
      break;
    case 'ArrowRight':
      control('right');
      break;
  }
};

/**
 * 处理全屏状态变化
 * 在全屏状态下添加键盘事件监听
 */
const handleFullscreenChange = () => {
  isFullscreen.value = document.fullscreenElement !== null;
  if (isFullscreen.value) {
    document.addEventListener('keydown', handleKeyDown);
  } else {
    document.removeEventListener('keydown', handleKeyDown);
  }
};

/**
 * 视频监控控制
 * 发送云台控制命令
 * @param direction 控制方向
 */
const control = (direction) => {
  // 发送开始控制命令
  getControlling({
    cameraIndexCode: props.data.cameraIndexCode,
    action: 0,
    command: direction,
    speed: 40,
    presetIndex: 20
  }).then(() => {
    // 发送停止控制命令
    getControlling({
      cameraIndexCode: props.data.cameraIndexCode,
      action: 1,
      command: direction,
      speed: 40,
      presetIndex: 20
    });
  });
};

/**
 * 播放事件处理
 */
const handlePlay = () => {
  emit('play');
};

/**
 * 暂停事件处理
 */
const handlePause = () => {
  emit('pause');
};

/**
 * 双击打开悬浮窗处理函数
 * 实现双击视频播放器打开悬浮窗功能，停止原视频以节省带宽
 */
const handleDoubleClick = async () => {
  if (!props.url || !props.data) {
    console.warn('无法创建悬浮窗：缺少视频URL或设备数据');
    return;
  }

  try {
    // 完全停止当前视频播放器以节省带宽
    if (player.value && !isDestroying) {
      try {
        await destroyPlayer();
      } catch (e) {
        console.warn('停止原视频失败:', e);
      }
    }

    // 生成设备名称，优先使用label，其次使用name或cameraIndexCode
    const deviceName = props.data.label || props.data.name || props.data.cameraIndexCode || '未知设备';

    // 创建悬浮窗
    const windowId = floatingWindowStore.createWindow({
      title: `视频监控 - ${deviceName}`,
      contentType: 'video-player',
      contentData: {
        url: props.url,
        data: props.data,
        playbackTime: props.playbackTime,
        isPlaying: props.isPlaying,
        isPlaybackMode: props.isPlaybackMode
      },
      size: { width: 800, height: 600 },
      minSize: { width: 400, height: 300 }
    });

    // 监听悬浮窗关闭事件，重新初始化并播放原视频
    floatingWindowStore.onWindowClose(windowId, async () => {
      if (!isDestroying && props.url) {
        try {
          await initPlayer();
          await playVideo(props.url);
        } catch (e) {
          console.warn('重新加载原视频失败:', e);
        }
      }
    });

  } catch (e) {
    console.error('创建悬浮窗失败:', e);
  }
};

/**
 * 被动wheel事件处理函数（使用节流优化）
 * 解决Chrome非被动事件监听器警告
 */
const handleWheelEvent = createThrottledHandler((event: WheelEvent) => {
  // 被动处理wheel事件，不阻止默认行为
  // 这里可以添加自定义的滚轮处理逻辑，如缩放等
}, 50);

/**
 * 被动触摸事件处理函数（使用节流优化）
 */
const handleTouchStart = createThrottledHandler((event: TouchEvent) => {
  // 被动处理触摸开始事件
}, 50);

const handleTouchMove = createThrottledHandler((event: TouchEvent) => {
  // 被动处理触摸移动事件
}, 16); // 约60fps

const handleTouchEnd = createThrottledHandler((event: TouchEvent) => {
  // 被动处理触摸结束事件
}, 50);

/**
 * 重试播放函数已移除
 */

/**
 * 组件挂载时初始化
 */
onMounted(async () => {
  await initPlayer();
  if (props.url) {
    await playVideo(props.url);
  }

  // 添加全屏变化监听（使用优化的事件监听器）
  addOptimizedEventListener(document, 'fullscreenchange', handleFullscreenChange);
  addOptimizedEventListener(document, 'webkitfullscreenchange', handleFullscreenChange);
  addOptimizedEventListener(document, 'mozfullscreenchange', handleFullscreenChange);
  addOptimizedEventListener(document, 'MSFullscreenChange', handleFullscreenChange);

  // 为视频容器添加被动事件监听器，覆盖EasyPlayerPro的默认行为
  if (videoContainer.value) {
    // 添加优化的被动事件监听器，解决Chrome警告
    addOptimizedEventListener(videoContainer.value, 'wheel', handleWheelEvent);
    addOptimizedEventListener(videoContainer.value, 'touchstart', handleTouchStart);
    addOptimizedEventListener(videoContainer.value, 'touchmove', handleTouchMove);
    addOptimizedEventListener(videoContainer.value, 'touchend', handleTouchEnd);
  }
});

/**
 * 组件卸载时清理
 */
onUnmounted(async () => {
  const passiveEventOptions = { passive: true };

  if (playTimeout) {
    clearTimeout(playTimeout);
  }
  await destroyPlayer();

  // 移除全屏变化监听
  removeOptimizedEventListener(document, 'fullscreenchange', handleFullscreenChange);
  removeOptimizedEventListener(document, 'webkitfullscreenchange', handleFullscreenChange);
  removeOptimizedEventListener(document, 'mozfullscreenchange', handleFullscreenChange);
  removeOptimizedEventListener(document, 'MSFullscreenChange', handleFullscreenChange);

  // 移除被动事件监听器
  if (videoContainer.value) {
    removeOptimizedEventListener(videoContainer.value, 'wheel', handleWheelEvent);
    removeOptimizedEventListener(videoContainer.value, 'touchstart', handleTouchStart);
    removeOptimizedEventListener(videoContainer.value, 'touchmove', handleTouchMove);
    removeOptimizedEventListener(videoContainer.value, 'touchend', handleTouchEnd);
  }
});
</script>

<style lang="scss" scoped>
/**
 * 视频播放器容器样式
 */
.video-player-container {
  position: relative;
  width: 100%;
  height: 100%;
  background-color: #000;
}

/**
 * 视频元素容器样式 - 优化双击体验
 */
.video-element {
  width: 100%;
  height: 100%;
  min-width: 400px;
  min-height: 200px;
  cursor: pointer; /* 添加指针样式提示可点击 */
  user-select: none; /* 防止双击时选中文本 */
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/**
 * 遮罩层样式已移除，避免影响视频播放
 */

/**
 * 动画样式已移除
 */

/**
 * 方向控制面板样式（已注释）
 */
.direction-control {
  width: 400px;
  height: 80px;
  z-index: 999;
  position: relative;
  pointer-events: auto;
  display: flex;

  button {
    background-color: rgba(0, 0, 0, 0.5);
    margin: 10px;
    color: white;
    border: 1px solid #fff;
    border-radius: 4px;
    padding: 8px 12px;
    cursor: pointer;
    height: 100%;
    font-size: 25px;

    &:hover {
      background-color: rgba(0, 0, 0, 0.7);
    }
  }
}
</style>
