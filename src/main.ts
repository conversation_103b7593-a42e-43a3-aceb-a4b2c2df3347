import { createApp } from 'vue';
// global css
import 'virtual:uno.css';
import '@/assets/styles/index.scss';
import 'element-plus/theme-chalk/dark/css-vars.css';
import '@/styles/dark.scss'

// App、router、store
import App from './App.vue';
import store from './store';
import router from './router';
import './styles/fonts.scss'
// 自定义指令
import directive from './directive';

// 注册插件
import plugins from './plugins/index'; // plugins

// 高亮组件
// import 'highlight.js/styles/a11y-light.css';
import 'highlight.js/styles/atom-one-dark.css';
import 'highlight.js/lib/common';
import HighLight from '@highlightjs/vue-plugin';

// svg图标
import 'virtual:svg-icons-register';
import ElementIcons from '@/plugins/svgicon';

// permission control
import './permission';

// 国际化
import i18n from '@/lang/index';

// vxeTable
import VXETable from 'vxe-table';
import 'vxe-table/lib/style.css';
VXETable.config({
  zIndex: 999999
});

// 修改 el-dialog 默认点击遮照为不关闭
import { ElDialog } from 'element-plus';
ElDialog.props.closeOnClickModal.default = false;

// 初始化全局事件优化，解决Chrome非被动事件监听器警告
import { initGlobalEventOptimization } from '@/utils/eventOptimizer';
initGlobalEventOptimization();

// 初始化网络优化，解决天地图请求影响视频性能问题
import { initNetworkOptimization } from '@/utils/networkOptimizer';
initNetworkOptimization();

// 初始化动效管理器
import { AnimationPlugin, animationManager } from '@/utils/animationManager';

const app = createApp(App);

app.use(HighLight);
app.use(ElementIcons);
app.use(router);
app.use(store);
app.use(i18n);
app.use(VXETable);
app.use(plugins);
app.use(AnimationPlugin);
// 自定义指令
directive(app);

app.mount('#app');
