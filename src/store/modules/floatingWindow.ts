import { defineStore } from 'pinia';
import { ref, computed, readonly } from 'vue';
import type {
  FloatingWindow,
  CreateWindowOptions,
  UpdateWindowOptions,
  WindowPosition,
  WindowSize
} from '@/types/floatingWindow';
import { WindowState } from '@/types/floatingWindow';

/**
 * 悬浮窗管理器状态管理
 */
export const useFloatingWindowStore = defineStore('floatingWindow', () => {
  // 状态定义
  const windows = ref<Map<string, FloatingWindow>>(new Map());
  const activeWindowId = ref<string>('');
  const maxZIndex = ref<number>(1000);
  const nextWindowOffset = ref<number>(0);

  // 窗口关闭事件监听器
  const windowCloseCallbacks = ref<Map<string, (() => void)[]>>(new Map());

  // 默认窗口配置
  const DEFAULT_WINDOW_SIZE: WindowSize = { width: 640, height: 480 };
  const DEFAULT_WINDOW_POSITION: WindowPosition = { x: 100, y: 100 };
  const WINDOW_OFFSET_STEP = 30;
  const MIN_WINDOW_SIZE: WindowSize = { width: 320, height: 240 };

  // 计算属性
  const windowList = computed(() => Array.from(windows.value.values()));
  const visibleWindows = computed(() => windowList.value.filter(w => w.visible));
  const activeWindow = computed(() =>
    activeWindowId.value ? windows.value.get(activeWindowId.value) : null
  );

  /**
   * 生成唯一窗口ID
   */
  const generateWindowId = (): string => {
    return `window_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  };

  /**
   * 计算新窗口位置，避免重叠
   */
  const calculateWindowPosition = (preferredPosition?: Partial<WindowPosition>): WindowPosition => {
    if (preferredPosition?.x !== undefined && preferredPosition?.y !== undefined) {
      return { x: preferredPosition.x, y: preferredPosition.y };
    }

    const offset = nextWindowOffset.value * WINDOW_OFFSET_STEP;
    const position = {
      x: DEFAULT_WINDOW_POSITION.x + offset,
      y: DEFAULT_WINDOW_POSITION.y + offset
    };

    // 确保窗口在屏幕范围内
    const maxX = window.innerWidth - DEFAULT_WINDOW_SIZE.width;
    const maxY = window.innerHeight - DEFAULT_WINDOW_SIZE.height;

    if (position.x > maxX) position.x = DEFAULT_WINDOW_POSITION.x;
    if (position.y > maxY) position.y = DEFAULT_WINDOW_POSITION.y;

    nextWindowOffset.value = (nextWindowOffset.value + 1) % 10;
    return position;
  };

  /**
   * 获取下一个z-index值
   */
  const getNextZIndex = (): number => {
    maxZIndex.value += 1;
    return maxZIndex.value;
  };

  /**
   * 创建新窗口
   */
  const createWindow = (options: CreateWindowOptions): string => {
    const windowId = generateWindowId();
    const now = Date.now();

    const newWindow: FloatingWindow = {
      id: windowId,
      title: options.title,
      position: calculateWindowPosition(options.position),
      size: { ...DEFAULT_WINDOW_SIZE, ...options.size },
      state: options.state || WindowState.NORMAL,
      zIndex: getNextZIndex(),
      visible: true,
      draggable: options.draggable !== false,
      resizable: options.resizable !== false,
      minSize: options.minSize || MIN_WINDOW_SIZE,
      maxSize: options.maxSize,
      contentType: options.contentType,
      contentData: options.contentData,
      createdAt: now,
      lastActiveAt: now
    };

    windows.value.set(windowId, newWindow);
    activeWindowId.value = windowId;

    return windowId;
  };

  /**
   * 关闭窗口
   */
  const closeWindow = (windowId: string): boolean => {
    if (!windows.value.has(windowId)) return false;

    // 触发窗口关闭回调
    const callbacks = windowCloseCallbacks.value.get(windowId);
    if (callbacks) {
      callbacks.forEach(callback => {
        try {
          callback();
        } catch (error) {
          console.error('窗口关闭回调执行失败:', error);
        }
      });
      // 清理回调
      windowCloseCallbacks.value.delete(windowId);
    }

    windows.value.delete(windowId);

    // 如果关闭的是活跃窗口，切换到最近活跃的窗口
    if (activeWindowId.value === windowId) {
      const remainingWindows = windowList.value
        .filter(w => w.visible)
        .sort((a, b) => b.lastActiveAt - a.lastActiveAt);

      activeWindowId.value = remainingWindows.length > 0 ? remainingWindows[0].id : '';
    }

    return true;
  };

  /**
   * 更新窗口
   */
  const updateWindow = (windowId: string, options: UpdateWindowOptions): boolean => {
    const window = windows.value.get(windowId);
    if (!window) return false;

    const updatedWindow = { ...window };

    if (options.position) updatedWindow.position = options.position;
    if (options.size) updatedWindow.size = options.size;
    if (options.state !== undefined) updatedWindow.state = options.state;
    if (options.visible !== undefined) updatedWindow.visible = options.visible;
    if (options.title) updatedWindow.title = options.title;
    if (options.contentData !== undefined) updatedWindow.contentData = options.contentData;

    updatedWindow.lastActiveAt = Date.now();
    windows.value.set(windowId, updatedWindow);

    return true;
  };

  /**
   * 激活窗口（置顶）
   */
  const activateWindow = (windowId: string): boolean => {
    const window = windows.value.get(windowId);
    if (!window) return false;

    // 更新z-index和活跃时间
    const updatedWindow = {
      ...window,
      zIndex: getNextZIndex(),
      lastActiveAt: Date.now()
    };

    windows.value.set(windowId, updatedWindow);
    activeWindowId.value = windowId;

    return true;
  };

  /**
   * 最小化窗口
   */
  const minimizeWindow = (windowId: string): boolean => {
    return updateWindow(windowId, { state: WindowState.MINIMIZED });
  };

  /**
   * 最大化窗口
   */
  const maximizeWindow = (windowId: string): boolean => {
    return updateWindow(windowId, { state: WindowState.MAXIMIZED });
  };

  /**
   * 恢复窗口
   */
  const restoreWindow = (windowId: string): boolean => {
    return updateWindow(windowId, { state: WindowState.NORMAL });
  };

  /**
   * 获取窗口
   */
  const getWindow = (windowId: string): FloatingWindow | undefined => {
    return windows.value.get(windowId);
  };

  /**
   * 关闭所有窗口
   */
  const closeAllWindows = (): void => {
    windows.value.clear();
    activeWindowId.value = '';
    nextWindowOffset.value = 0;
  };

  /**
   * 按内容类型获取窗口
   */
  const getWindowsByContentType = (contentType: string): FloatingWindow[] => {
    return windowList.value.filter(w => w.contentType === contentType);
  };

  /**
   * 检查窗口是否存在
   */
  const hasWindow = (windowId: string): boolean => {
    return windows.value.has(windowId);
  };

  /**
   * 监听窗口关闭事件
   */
  const onWindowClose = (windowId: string, callback: () => void): void => {
    if (!windowCloseCallbacks.value.has(windowId)) {
      windowCloseCallbacks.value.set(windowId, []);
    }
    windowCloseCallbacks.value.get(windowId)!.push(callback);
  };

  /**
   * 移除窗口关闭事件监听器
   */
  const offWindowClose = (windowId: string, callback?: () => void): void => {
    const callbacks = windowCloseCallbacks.value.get(windowId);
    if (!callbacks) return;

    if (callback) {
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    } else {
      // 如果没有指定回调，清除所有回调
      windowCloseCallbacks.value.delete(windowId);
    }
  };

  return {
    // 状态
    windows: readonly(windows),
    activeWindowId: readonly(activeWindowId),
    maxZIndex: readonly(maxZIndex),

    // 计算属性
    windowList,
    visibleWindows,
    activeWindow,

    // 方法
    createWindow,
    closeWindow,
    updateWindow,
    activateWindow,
    minimizeWindow,
    maximizeWindow,
    restoreWindow,
    getWindow,
    closeAllWindows,
    getWindowsByContentType,
    hasWindow,
    onWindowClose,
    offWindowClose
  };
});

export default useFloatingWindowStore;
