/* 暗黑模式变量 */
html.dark {
  // 背景颜色
  --el-bg-color: #141414;
  --el-bg-color-page: #0a0a0a;
  --el-bg-color-overlay: #1d1e1f;
  
  // 文字颜色
  --el-text-color-primary: rgba(255, 255, 255, 0.85);
  --el-text-color-regular: rgba(255, 255, 255, 0.65);
  --el-text-color-secondary: rgba(255, 255, 255, 0.45);
  
  // 边框颜色
  --el-border-color-light: #434343;
  --el-border-color-extra-light: #303030;
  
  // 填充颜色
  --el-fill-color: rgba(255, 255, 255, 0.04);
  --el-fill-color-light: rgba(255, 255, 255, 0.08);
  
  // 卡片颜色
  --el-card-bg-color: var(--el-bg-color-overlay);
  
  // 表格颜色
  --el-table-tr-bg-color: var(--el-fill-color);
  --el-table-header-bg-color: var(--el-bg-color-page);
  
  // 弹窗颜色
  --el-dialog-bg-color: var(--el-bg-color-overlay);
  
  // 菜单颜色
  --el-menu-bg-color: var(--el-bg-color-overlay);
  --el-menu-hover-bg-color: var(--el-fill-color-light);
  
  // 按钮颜色
  --el-button-hover-bg-color: rgba(255, 255, 255, 0.08);
  
  // 输入框颜色
  --el-input-bg-color: var(--el-bg-color-overlay);
  --el-input-border-color: var(--el-border-color-light);
  
  // 分页组件
  --el-pagination-bg-color: var(--el-bg-color-overlay);
  
  // 下拉菜单
  --el-dropdown-menu-bg-color: var(--el-bg-color-overlay);
  
  // 时间线组件
  --el-timeline-node-background-color: var(--el-bg-color-page);
} 