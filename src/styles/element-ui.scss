// @forward 'element-plus/theme-chalk/src/common/var.scss' with (
//   $colors: (
//     'primary': (
//       'base': #409EFF,
//     )
//   )
// );

// @use "element-plus/theme-chalk/src/dark/css-vars.scss" as *; 

// 自定义 ElMessageBox 样式
// :root {
//   --el-messagebox-width: 500px;
//   --el-messagebox-padding: 30px;
//   --el-messagebox-border-radius: 8px;
//   --el-messagebox-font-size: 20px;
//   --el-messagebox-content-font-size: 18px;
//   --el-messagebox-content-padding: 20px 0;
//   --el-messagebox-title-font-size: 24px;
//   --el-messagebox-title-color: #fff;
//   --el-messagebox-content-color: #fff;
//   --el-messagebox-bg-color: #353638;
//   --el-messagebox-border-color: #4c4e53;
// }

// .el-overlay-message-box {
//   .el-message-box {
//     background: var(--el-messagebox-bg-color);
//     border: 1px solid var(--el-messagebox-border-color);
    
//     .el-message-box__header {
//       padding: 20px 30px;
      
//       .el-message-box__title {
//         font-size: var(--el-messagebox-title-font-size);
//         color: var(--el-messagebox-title-color);
//       }
      
//       .el-message-box__headerbtn {
//         top: 20px;
//         right: 20px;
        
//         .el-message-box__close {
//           font-size: 24px;
//           color: #fff;
//         }
//       }
//     }
    
//     .el-message-box__content {
//       padding: var(--el-messagebox-content-padding);
//       font-size: var(--el-messagebox-content-font-size);
//       color: var(--el-messagebox-content-color);
//     }
    
//     .el-message-box__btns {
//       padding: 20px 30px;
      
//       .el-button {
//         font-size: 18px;
//         padding: 12px 30px;
        
//         &--primary {
//           background: #446397;
//           border-color: #446397;
          
//           &:hover {
//             background: #365484;
//             border-color: #365484;
//           }
//         }
        
//         &--default {
//           background: #4c4e53;
//           border-color: #4c4e53;
//           color: #fff;
          
//           &:hover {
//             background: #5a5c62;
//             border-color: #5a5c62;
//           }
//         }
//       }
//     }
//   }
// } 