/* 数字式LED显示字体 */
@font-face {
  font-family: 'DS-Digital';
  src: url('@/assets/fonts/DS-DIGI-1.TTF') format('truetype');
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'DS-Digital';
  src: url('@/assets/fonts/DS-DIGIB-2.TTF') format('truetype');
  font-weight: bold;
  font-style: normal;
}

@font-face {
  font-family: 'DS-Digital';
  src: url('@/assets/fonts/DS-DIGII-3.TTF') format('truetype');
  font-weight: normal;
  font-style: italic;
}

@font-face {
  font-family: 'DS-Digital';
  src: url('@/assets/fonts/DS-DIGIT-4.TTF') format('truetype');
  font-weight: bold;
  font-style: italic;
}

/* 微软雅黑字体 */
@font-face {
  font-family: 'Microsoft YaHei';
  src: url('@/assets/fonts/MSYH.TTC') format('truetype');
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'Microsoft YaHei';
  src: url('@/assets/fonts/MSYHBD.TTC') format('truetype');
  font-weight: bold;
  font-style: normal;
}

@font-face {
  font-family: 'Microsoft YaHei Light';
  src: url('@/assets/fonts/MSYHL.TTC') format('truetype');
  font-weight: 300;
  font-style: normal;
}

/* 标题特殊字体 */
@font-face {
  font-family: 'Title3';
  src: url('@/assets/fonts/title3.0.TTF') format('truetype');
  font-weight: normal;
  font-style: normal;
} 