/**
 * 悬浮窗位置信息
 */
export interface WindowPosition {
  x: number;
  y: number;
}

/**
 * 悬浮窗尺寸信息
 */
export interface WindowSize {
  width: number;
  height: number;
}

/**
 * 悬浮窗状态枚举
 */
export enum WindowState {
  NORMAL = 'normal',
  MINIMIZED = 'minimized',
  MAXIMIZED = 'maximized'
}

/**
 * 悬浮窗配置信息
 */
export interface FloatingWindow {
  /** 窗口唯一标识 */
  id: string;
  /** 窗口标题 */
  title: string;
  /** 窗口位置 */
  position: WindowPosition;
  /** 窗口尺寸 */
  size: WindowSize;
  /** 窗口状态 */
  state: WindowState;
  /** 层级索引 */
  zIndex: number;
  /** 是否可见 */
  visible: boolean;
  /** 是否可拖拽 */
  draggable: boolean;
  /** 是否可调整大小 */
  resizable: boolean;
  /** 最小尺寸 */
  minSize?: WindowSize;
  /** 最大尺寸 */
  maxSize?: WindowSize;
  /** 窗口内容类型 */
  contentType: string;
  /** 窗口内容数据 */
  contentData?: any;
  /** 创建时间 */
  createdAt: number;
  /** 最后活跃时间 */
  lastActiveAt: number;
}

/**
 * 创建悬浮窗的参数
 */
export interface CreateWindowOptions {
  /** 窗口标题 */
  title: string;
  /** 初始位置（可选，默认自动计算） */
  position?: Partial<WindowPosition>;
  /** 初始尺寸（可选，使用默认值） */
  size?: Partial<WindowSize>;
  /** 窗口状态（可选，默认正常） */
  state?: WindowState;
  /** 是否可拖拽（可选，默认true） */
  draggable?: boolean;
  /** 是否可调整大小（可选，默认true） */
  resizable?: boolean;
  /** 最小尺寸（可选） */
  minSize?: WindowSize;
  /** 最大尺寸（可选） */
  maxSize?: WindowSize;
  /** 窗口内容类型 */
  contentType: string;
  /** 窗口内容数据 */
  contentData?: any;
}

/**
 * 窗口更新参数
 */
export interface UpdateWindowOptions {
  /** 窗口位置 */
  position?: WindowPosition;
  /** 窗口尺寸 */
  size?: WindowSize;
  /** 窗口状态 */
  state?: WindowState;
  /** 是否可见 */
  visible?: boolean;
  /** 窗口标题 */
  title?: string;
  /** 窗口内容数据 */
  contentData?: any;
}
