/**
 * 动效管理器 - 统一管理交互动效和用户偏好
 */

export interface AnimationConfig {
  enabled: boolean;
  reducedMotion: boolean;
  intensity: number; // 0.5 | 1 | 1.5
  performanceMode: 'normal' | 'high-performance' | 'accessibility';
}

export class AnimationManager {
  private config: AnimationConfig;
  private observers: ((config: AnimationConfig) => void)[] = [];

  constructor() {
    this.config = this.getDefaultConfig();
    this.init();
  }

  /**
   * 获取默认配置
   */
  private getDefaultConfig(): AnimationConfig {
    return {
      enabled: true,
      reducedMotion: this.detectReducedMotionPreference(),
      intensity: 1,
      performanceMode: 'normal'
    };
  }

  /**
   * 检测用户的减少动效偏好
   */
  private detectReducedMotionPreference(): boolean {
    if (typeof window !== 'undefined' && window.matchMedia) {
      return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
    }
    return false;
  }

  /**
   * 初始化动效管理器
   */
  private init() {
    this.loadUserPreferences();
    this.applyConfiguration();
    this.setupMediaQueryListener();
    this.detectPerformanceCapabilities();
  }

  /**
   * 加载用户偏好设置
   */
  private loadUserPreferences() {
    try {
      const saved = localStorage.getItem('animation-preferences');
      if (saved) {
        const preferences = JSON.parse(saved);
        this.config = { ...this.config, ...preferences };
      }
    } catch (error) {
      console.warn('Failed to load animation preferences:', error);
    }
  }

  /**
   * 保存用户偏好设置
   */
  private saveUserPreferences() {
    try {
      localStorage.setItem('animation-preferences', JSON.stringify(this.config));
    } catch (error) {
      console.warn('Failed to save animation preferences:', error);
    }
  }

  /**
   * 应用配置到CSS变量
   */
  private applyConfiguration() {
    if (typeof document === 'undefined') return;

    const root = document.documentElement;
    
    root.style.setProperty('--animations-enabled', this.config.enabled ? '1' : '0');
    root.style.setProperty('--reduced-motion', this.config.reducedMotion ? '1' : '0');
    root.style.setProperty('--animation-intensity', this.config.intensity.toString());
    root.style.setProperty('--performance-mode', this.config.performanceMode);

    // 应用全局类
    document.body.classList.toggle('no-animations', !this.config.enabled);
    document.body.classList.toggle('reduced-animations', this.config.reducedMotion);
    document.body.classList.toggle('high-performance-mode', this.config.performanceMode === 'high-performance');
  }

  /**
   * 设置媒体查询监听器
   */
  private setupMediaQueryListener() {
    if (typeof window === 'undefined' || !window.matchMedia) return;

    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    const handleChange = (e: MediaQueryListEvent) => {
      this.config.reducedMotion = e.matches;
      this.applyConfiguration();
      this.notifyObservers();
    };

    // 现代浏览器
    if (mediaQuery.addEventListener) {
      mediaQuery.addEventListener('change', handleChange);
    } else {
      // 旧版浏览器兼容
      mediaQuery.addListener(handleChange);
    }
  }

  /**
   * 检测设备性能能力
   */
  private detectPerformanceCapabilities() {
    if (typeof navigator === 'undefined') return;

    // 检测设备内存
    const deviceMemory = (navigator as any).deviceMemory;
    if (deviceMemory && deviceMemory < 4) {
      this.config.performanceMode = 'high-performance';
    }

    // 检测网络连接
    const connection = (navigator as any).connection;
    if (connection && connection.effectiveType && 
        ['slow-2g', '2g', '3g'].includes(connection.effectiveType)) {
      this.config.performanceMode = 'high-performance';
    }

    // 检测电池状态
    if ('getBattery' in navigator) {
      (navigator as any).getBattery().then((battery: any) => {
        if (battery.level < 0.2 || battery.charging === false) {
          this.config.performanceMode = 'high-performance';
          this.applyConfiguration();
        }
      });
    }
  }

  /**
   * 通知观察者配置变更
   */
  private notifyObservers() {
    this.observers.forEach(observer => observer(this.config));
  }

  /**
   * 获取当前配置
   */
  getConfig(): AnimationConfig {
    return { ...this.config };
  }

  /**
   * 更新配置
   */
  updateConfig(updates: Partial<AnimationConfig>) {
    this.config = { ...this.config, ...updates };
    this.applyConfiguration();
    this.saveUserPreferences();
    this.notifyObservers();
  }

  /**
   * 启用动效
   */
  enableAnimations() {
    this.updateConfig({ enabled: true });
  }

  /**
   * 禁用动效
   */
  disableAnimations() {
    this.updateConfig({ enabled: false });
  }

  /**
   * 切换动效状态
   */
  toggleAnimations() {
    this.updateConfig({ enabled: !this.config.enabled });
  }

  /**
   * 设置动效强度
   */
  setIntensity(intensity: number) {
    const clampedIntensity = Math.max(0.5, Math.min(1.5, intensity));
    this.updateConfig({ intensity: clampedIntensity });
  }

  /**
   * 设置性能模式
   */
  setPerformanceMode(mode: AnimationConfig['performanceMode']) {
    this.updateConfig({ performanceMode: mode });
  }

  /**
   * 添加配置变更观察者
   */
  addObserver(observer: (config: AnimationConfig) => void) {
    this.observers.push(observer);
  }

  /**
   * 移除配置变更观察者
   */
  removeObserver(observer: (config: AnimationConfig) => void) {
    const index = this.observers.indexOf(observer);
    if (index > -1) {
      this.observers.splice(index, 1);
    }
  }

  /**
   * 检查是否应该播放动画
   */
  shouldAnimate(): boolean {
    return this.config.enabled && !this.config.reducedMotion;
  }

  /**
   * 获取动画持续时间（考虑强度设置）
   */
  getDuration(baseDuration: number): number {
    if (!this.shouldAnimate()) return 0;
    return baseDuration * this.config.intensity;
  }

  /**
   * 应用动效到元素
   */
  applyAnimation(element: HTMLElement, animationClass: string, duration?: number) {
    if (!this.shouldAnimate()) return;

    element.classList.add(animationClass);
    
    if (duration) {
      element.style.animationDuration = `${this.getDuration(duration)}ms`;
    }

    // 动画结束后清理
    const handleAnimationEnd = () => {
      element.classList.remove(animationClass);
      element.style.animationDuration = '';
      element.removeEventListener('animationend', handleAnimationEnd);
    };

    element.addEventListener('animationend', handleAnimationEnd);
  }

  /**
   * 获取动效统计信息
   */
  getStats() {
    return {
      config: this.getConfig(),
      isSupported: typeof window !== 'undefined' && 'CSS' in window,
      reducedMotionSupported: typeof window !== 'undefined' && 
        window.matchMedia && window.matchMedia('(prefers-reduced-motion)').media !== 'not all',
      observersCount: this.observers.length
    };
  }
}

// 创建全局实例
export const animationManager = new AnimationManager();

// 导出便捷函数
export const enableAnimations = () => animationManager.enableAnimations();
export const disableAnimations = () => animationManager.disableAnimations();
export const toggleAnimations = () => animationManager.toggleAnimations();
export const shouldAnimate = () => animationManager.shouldAnimate();
export const applyAnimation = (element: HTMLElement, animationClass: string, duration?: number) => 
  animationManager.applyAnimation(element, animationClass, duration);

// Vue 3 插件
export const AnimationPlugin = {
  install(app: any) {
    app.config.globalProperties.$animation = animationManager;
    app.provide('animationManager', animationManager);
  }
};
