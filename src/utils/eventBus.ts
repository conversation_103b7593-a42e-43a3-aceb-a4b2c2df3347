import { ref, onMounted, onUnmounted } from 'vue';

// 创建事件总线类
class EventBus {
  private events: Map<string, Array<Function>>;

  constructor() {
    this.events = new Map();
  }

  // 订阅事件
  on(eventName: string, callback: Function): void {
    if (!this.events.has(eventName)) {
      this.events.set(eventName, []);
    }
    this.events.get(eventName)?.push(callback);
  }

  // 触发事件
  emit(eventName: string, ...args: any[]): void {
    if (this.events.has(eventName)) {
      this.events.get(eventName)?.forEach(callback => {
        callback(...args);
      });
    }
  }

  // 取消订阅
  off(eventName: string, callback?: Function): void {
    if (this.events.has(eventName)) {
      if (callback) {
        const callbacks = this.events.get(eventName);
        const index = callbacks?.indexOf(callback);
        if (index !== -1 && index !== undefined) {
          callbacks?.splice(index, 1);
        }
      } else {
        this.events.delete(eventName);
      }
    }
  }
}

// 创建并导出事件总线实例
const eventBus = new EventBus();
export default eventBus;

// 导出Vue组合式API的钩子函数，便于组件使用
export function useEventBus() {
  // 事件订阅钩子
  function onEvent(eventName: string, callback: Function) {
    eventBus.on(eventName, callback);

    // 返回清理函数
    return () => {
      eventBus.off(eventName, callback);
    };
  }

  // 事件触发函数
  function emitEvent(eventName: string, ...args: any[]) {
    eventBus.emit(eventName, ...args);
  }

  return {
    onEvent,
    emitEvent
  };
}