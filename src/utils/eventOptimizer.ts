/**
 * 事件监听器优化工具
 * 解决Chrome非被动事件监听器警告
 */

/**
 * 被动事件类型列表
 */
const PASSIVE_EVENTS = [
  'wheel',
  'mousewheel',
  'touchstart',
  'touchmove',
  'touchend',
  'scroll',
  'resize'
];

/**
 * 需要非被动处理的事件类型
 */
const NON_PASSIVE_EVENTS = [
  'keydown',
  'keyup',
  'contextmenu',
  'dragstart',
  'drop'
];

/**
 * 检查事件是否应该使用被动监听器
 * @param eventType 事件类型
 * @returns 是否应该使用被动监听器
 */
export function shouldUsePassiveListener(eventType: string): boolean {
  return PASSIVE_EVENTS.includes(eventType.toLowerCase());
}

/**
 * 获取事件监听器选项
 * @param eventType 事件类型
 * @param forcePassive 强制使用被动监听器
 * @returns 事件监听器选项
 */
export function getEventListenerOptions(
  eventType: string, 
  forcePassive: boolean = false
): AddEventListenerOptions {
  const isPassive = forcePassive || shouldUsePassiveListener(eventType);
  
  return {
    passive: isPassive,
    capture: false
  };
}

/**
 * 优化的事件监听器添加函数
 * @param element 目标元素
 * @param eventType 事件类型
 * @param handler 事件处理函数
 * @param options 自定义选项
 */
export function addOptimizedEventListener(
  element: Element | Document | Window,
  eventType: string,
  handler: EventListener,
  options?: AddEventListenerOptions
): void {
  const optimizedOptions = options || getEventListenerOptions(eventType);
  
  try {
    element.addEventListener(eventType, handler, optimizedOptions);
  } catch (error) {
    // 降级处理：如果浏览器不支持passive选项，使用默认方式
    console.warn(`Failed to add optimized event listener for ${eventType}:`, error);
    element.addEventListener(eventType, handler);
  }
}

/**
 * 优化的事件监听器移除函数
 * @param element 目标元素
 * @param eventType 事件类型
 * @param handler 事件处理函数
 */
export function removeOptimizedEventListener(
  element: Element | Document | Window,
  eventType: string,
  handler: EventListener
): void {
  try {
    element.removeEventListener(eventType, handler);
  } catch (error) {
    console.warn(`Failed to remove event listener for ${eventType}:`, error);
  }
}

/**
 * 批量添加优化的事件监听器
 * @param element 目标元素
 * @param events 事件配置数组
 */
export function addMultipleOptimizedEventListeners(
  element: Element | Document | Window,
  events: Array<{
    type: string;
    handler: EventListener;
    options?: AddEventListenerOptions;
  }>
): void {
  events.forEach(({ type, handler, options }) => {
    addOptimizedEventListener(element, type, handler, options);
  });
}

/**
 * 批量移除优化的事件监听器
 * @param element 目标元素
 * @param events 事件配置数组
 */
export function removeMultipleOptimizedEventListeners(
  element: Element | Document | Window,
  events: Array<{
    type: string;
    handler: EventListener;
  }>
): void {
  events.forEach(({ type, handler }) => {
    removeOptimizedEventListener(element, type, handler);
  });
}

/**
 * 创建防抖的事件处理函数
 * @param handler 原始事件处理函数
 * @param delay 防抖延迟时间（毫秒）
 * @returns 防抖后的事件处理函数
 */
export function createDebouncedHandler(
  handler: EventListener,
  delay: number = 100
): EventListener {
  let timeoutId: number | null = null;
  
  return function(this: any, event: Event) {
    if (timeoutId !== null) {
      clearTimeout(timeoutId);
    }
    
    timeoutId = window.setTimeout(() => {
      handler.call(this, event);
      timeoutId = null;
    }, delay);
  };
}

/**
 * 创建节流的事件处理函数
 * @param handler 原始事件处理函数
 * @param delay 节流延迟时间（毫秒）
 * @returns 节流后的事件处理函数
 */
export function createThrottledHandler(
  handler: EventListener,
  delay: number = 100
): EventListener {
  let lastCallTime = 0;
  
  return function(this: any, event: Event) {
    const now = Date.now();
    
    if (now - lastCallTime >= delay) {
      handler.call(this, event);
      lastCallTime = now;
    }
  };
}

/**
 * 全局事件监听器优化初始化
 * 在应用启动时调用，优化全局事件处理
 */
export function initGlobalEventOptimization(): void {
  // 检查浏览器是否支持passive选项
  let supportsPassive = false;
  
  try {
    const opts = Object.defineProperty({}, 'passive', {
      get() {
        supportsPassive = true;
        return false;
      }
    });
    
    window.addEventListener('testPassive', () => {}, opts);
    window.removeEventListener('testPassive', () => {}, opts);
  } catch (e) {
    supportsPassive = false;
  }
  
  if (!supportsPassive) {
    console.warn('Browser does not support passive event listeners');
  }
  
  // 添加全局错误处理
  window.addEventListener('error', (event) => {
    if (event.message && event.message.includes('passive')) {
      console.warn('Passive event listener warning detected:', event);
    }
  }, { passive: true });
}

/**
 * 事件监听器性能监控
 */
export class EventListenerMonitor {
  private listeners: Map<string, number> = new Map();
  
  /**
   * 记录事件监听器添加
   * @param eventType 事件类型
   */
  recordAdd(eventType: string): void {
    const count = this.listeners.get(eventType) || 0;
    this.listeners.set(eventType, count + 1);
  }
  
  /**
   * 记录事件监听器移除
   * @param eventType 事件类型
   */
  recordRemove(eventType: string): void {
    const count = this.listeners.get(eventType) || 0;
    if (count > 0) {
      this.listeners.set(eventType, count - 1);
    }
  }
  
  /**
   * 获取监控报告
   * @returns 监控报告
   */
  getReport(): Record<string, number> {
    const report: Record<string, number> = {};
    this.listeners.forEach((count, eventType) => {
      if (count > 0) {
        report[eventType] = count;
      }
    });
    return report;
  }
  
  /**
   * 检查是否有内存泄漏风险
   * @returns 是否有风险
   */
  checkMemoryLeakRisk(): boolean {
    const totalListeners = Array.from(this.listeners.values()).reduce((sum, count) => sum + count, 0);
    return totalListeners > 100; // 阈值可调整
  }
}

// 导出全局监控实例
export const globalEventMonitor = new EventListenerMonitor();
