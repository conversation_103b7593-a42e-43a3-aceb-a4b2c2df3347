/**
 * 网络请求优化工具
 * 解决天地图瓦片请求影响视频播放性能的问题
 */

/**
 * 请求优先级枚举
 */
export enum RequestPriority {
  CRITICAL = 1,    // 关键请求（视频流）
  HIGH = 2,        // 高优先级（API请求）
  NORMAL = 3,      // 普通优先级（页面资源）
  LOW = 4,         // 低优先级（地图瓦片）
  BACKGROUND = 5   // 后台请求（统计、日志）
}

/**
 * 请求队列管理器
 */
class RequestQueueManager {
  private queues: Map<RequestPriority, Array<() => Promise<any>>> = new Map();
  private activeRequests: Map<RequestPriority, number> = new Map();
  private maxConcurrentRequests: Map<RequestPriority, number> = new Map();
  private isProcessing = false;

  constructor() {
    // 初始化队列和并发限制
    Object.values(RequestPriority).forEach(priority => {
      if (typeof priority === 'number') {
        this.queues.set(priority, []);
        this.activeRequests.set(priority, 0);
      }
    });

    // 设置各优先级的最大并发数
    this.maxConcurrentRequests.set(RequestPriority.CRITICAL, 10);
    this.maxConcurrentRequests.set(RequestPriority.HIGH, 6);
    this.maxConcurrentRequests.set(RequestPriority.NORMAL, 4);
    this.maxConcurrentRequests.set(RequestPriority.LOW, 2);
    this.maxConcurrentRequests.set(RequestPriority.BACKGROUND, 1);
  }

  /**
   * 添加请求到队列
   */
  enqueue(priority: RequestPriority, requestFn: () => Promise<any>): Promise<any> {
    return new Promise((resolve, reject) => {
      const wrappedRequest = async () => {
        try {
          const result = await requestFn();
          resolve(result);
        } catch (error) {
          reject(error);
        } finally {
          this.activeRequests.set(priority, (this.activeRequests.get(priority) || 0) - 1);
          this.processQueue();
        }
      };

      this.queues.get(priority)?.push(wrappedRequest);
      this.processQueue();
    });
  }

  /**
   * 处理队列
   */
  private processQueue() {
    if (this.isProcessing) return;
    this.isProcessing = true;

    // 按优先级处理请求
    for (const priority of [
      RequestPriority.CRITICAL,
      RequestPriority.HIGH,
      RequestPriority.NORMAL,
      RequestPriority.LOW,
      RequestPriority.BACKGROUND
    ]) {
      const queue = this.queues.get(priority);
      const activeCount = this.activeRequests.get(priority) || 0;
      const maxConcurrent = this.maxConcurrentRequests.get(priority) || 1;

      if (queue && queue.length > 0 && activeCount < maxConcurrent) {
        const requestsToProcess = Math.min(queue.length, maxConcurrent - activeCount);

        for (let i = 0; i < requestsToProcess; i++) {
          const request = queue.shift();
          if (request) {
            this.activeRequests.set(priority, activeCount + i + 1);
            request();
          }
        }
      }
    }

    this.isProcessing = false;
  }

  /**
   * 获取队列状态
   */
  getStatus() {
    const status: any = {};
    this.queues.forEach((queue, priority) => {
      status[RequestPriority[priority]] = {
        queued: queue.length,
        active: this.activeRequests.get(priority) || 0,
        maxConcurrent: this.maxConcurrentRequests.get(priority) || 0
      };
    });
    return status;
  }
}

/**
 * 网络状态监控器
 */
class NetworkMonitor {
  private bandwidth = 0;
  private latency = 0;
  private isSlowNetwork = false;

  /**
   * 检测网络状态
   */
  async detectNetworkStatus(): Promise<void> {
    try {
      // 使用小文件测试网络速度
      const startTime = performance.now();
      const response = await fetch('/favicon.ico?' + Date.now(), {
        cache: 'no-cache',
        method: 'HEAD'
      });
      const endTime = performance.now();

      this.latency = endTime - startTime;
      this.isSlowNetwork = this.latency > 1000; // 超过1秒认为是慢网络

    } catch (error) {
      console.warn('Network detection failed:', error);
      this.isSlowNetwork = true;
    }
  }

  /**
   * 获取网络状态
   */
  getNetworkStatus() {
    return {
      bandwidth: this.bandwidth,
      latency: this.latency,
      isSlowNetwork: this.isSlowNetwork
    };
  }
}

/**
 * 全局网络优化器实例
 */
export const requestQueueManager = new RequestQueueManager();
export const networkMonitor = new NetworkMonitor();

/**
 * 请求优先级检测器
 */
export function detectRequestPriority(url: string): RequestPriority {
  // 视频流请求 - 最高优先级
  if (url.includes('rtmp://') || url.includes('rtsp://') ||
      url.includes('ws://') || url.includes('wss://') ||
      url.includes('/video/') || url.includes('/stream/')) {
    return RequestPriority.CRITICAL;
  }

  // API请求 - 高优先级
  if (url.includes('/api/') || url.includes('/dev-api/')) {
    return RequestPriority.HIGH;
  }

  // 天地图瓦片请求 - 低优先级
  if (url.includes('tianditu.com') || url.includes('DataServer')) {
    return RequestPriority.LOW;
  }

  // 静态资源 - 普通优先级
  if (url.includes('.js') || url.includes('.css') ||
      url.includes('.png') || url.includes('.jpg') ||
      url.includes('.svg') || url.includes('.woff')) {
    return RequestPriority.NORMAL;
  }

  // 默认普通优先级
  return RequestPriority.NORMAL;
}

/**
 * 优化的fetch函数
 */
export function optimizedFetch(url: string, options?: RequestInit): Promise<Response> {
  const priority = detectRequestPriority(url);

  return requestQueueManager.enqueue(priority, () => {
    return fetch(url, options);
  });
}

/**
 * 瓦片缓存项接口 - 重构版
 */
interface TileCacheItem {
  blob: Blob;
  headers: Record<string, string>;
  status: number;
  statusText: string;
  timestamp: number;
  accessCount: number;
  lastAccessed: number;
  size: number; // 实际Blob大小（字节）
}

/**
 * 待处理请求接口
 */
interface PendingRequest {
  promise: Promise<Response>;
  timestamp: number;
}

/**
 * 增强的地图瓦片请求节流器 - 重构版，支持正确的瓦片缓存和内存管理
 */
class MapTileThrottler {
  private pendingRequests = new Map<string, PendingRequest>();
  private tileCache = new Map<string, TileCacheItem>();
  private accessOrder = new Map<string, number>(); // LRU访问顺序
  private maxCacheSize = 100; // 最大缓存项数
  private maxMemorySize = 100 * 1024 * 1024; // 最大内存使用100MB
  private currentMemoryUsage = 0;
  private cacheExpireTime = 24 * 60 * 60 * 1000; // 24小时过期
  private accessCounter = 0;
  private cleanupInterval: NodeJS.Timeout | null = null;
  private hitCount = 0; // 缓存命中次数
  private totalRequests = 0; // 总请求次数

  constructor() {
    this.startCleanupTimer();
  }

  /**
   * 启动定期清理定时器
   */
  private startCleanupTimer() {
    this.cleanupInterval = setInterval(() => {
      this.performMaintenance();
    }, 5 * 60 * 1000); // 每5分钟执行一次维护
  }

  /**
   * 执行缓存维护
   */
  private performMaintenance() {
    this.cleanExpiredItems();
    this.checkMemoryPressure();
    this.logCacheStats();
  }

  /**
   * 清理过期项
   */
  private cleanExpiredItems() {
    const now = Date.now();
    const expiredKeys: string[] = [];

    // 清理过期的瓦片缓存
    for (const [key, item] of this.tileCache) {
      if (now - item.timestamp > this.cacheExpireTime) {
        expiredKeys.push(key);
      }
    }

    expiredKeys.forEach(key => {
      this.removeCacheItem(key);
    });

    // 清理超时的待处理请求
    this.cleanupPendingRequests();

    if (expiredKeys.length > 0) {
    }
  }

  /**
   * 检查内存压力并清理
   */
  private checkMemoryPressure() {
    if (this.currentMemoryUsage > this.maxMemorySize * 0.8) {
      this.performLRUCleanup(Math.floor(this.tileCache.size * 0.3));
    }
  }

  /**
   * 执行LRU清理
   */
  private performLRUCleanup(itemsToRemove: number) {
    const sortedItems = Array.from(this.tileCache.entries())
      .sort((a, b) => a[1].lastAccessed - b[1].lastAccessed);

    const removedCount = Math.min(itemsToRemove, sortedItems.length);
    for (let i = 0; i < removedCount; i++) {
      this.removeCacheItem(sortedItems[i][0]);
    }

    if (removedCount > 0) {
    }
  }

  /**
   * 移除缓存项
   */
  private removeCacheItem(key: string) {
    const item = this.tileCache.get(key);
    if (item) {
      this.currentMemoryUsage -= item.size;
      this.tileCache.delete(key);
      this.accessOrder.delete(key);
    }
  }

  /**
   * 清理超时的待处理请求
   */
  private cleanupPendingRequests() {
    const now = Date.now();
    const timeoutThreshold = 30000; // 30秒超时

    for (const [key, request] of this.pendingRequests) {
      if (now - request.timestamp > timeoutThreshold) {
        this.pendingRequests.delete(key);
      }
    }
  }

  /**
   * 估算响应大小
   */
  private estimateResponseSize(url: string): number {
    // 基于URL估算瓦片大小，一般瓦片在10-50KB之间
    const baseSize = 25 * 1024; // 25KB基础大小
    const urlLength = url.length;
    return baseSize + urlLength * 10; // URL越长，可能包含更多参数，估算稍大
  }

  /**
   * 生成缓存键
   */
  private generateCacheKey(url: string): string {
    // 移除时间戳等动态参数，保留核心瓦片标识
    const urlObj = new URL(url);
    const params = new URLSearchParams(urlObj.search);

    // 保留核心参数
    const coreParams = ['x', 'y', 'z', 'l', 'T', 'tk'];
    const filteredParams = new URLSearchParams();

    coreParams.forEach(param => {
      if (params.has(param)) {
        filteredParams.set(param, params.get(param)!);
      }
    });

    return `${urlObj.origin}${urlObj.pathname}?${filteredParams.toString()}`;
  }

  /**
   * 节流的瓦片请求 - 重构版，正确处理瓦片缓存
   */
  throttledTileRequest(url: string, options?: RequestInit): Promise<Response> {
    const cacheKey = this.generateCacheKey(url);
    const now = Date.now();
    this.totalRequests++;

    // 检查瓦片缓存
    const cachedTile = this.tileCache.get(cacheKey);
    if (cachedTile) {
      // 缓存命中，更新访问信息
      cachedTile.lastAccessed = now;
      cachedTile.accessCount++;
      this.accessOrder.set(cacheKey, ++this.accessCounter);
      this.hitCount++;

      // 从缓存的瓦片数据重新构造Response对象
      return Promise.resolve(this.createResponseFromCache(cachedTile));
    }

    // 检查是否已有相同请求在进行
    const pendingRequest = this.pendingRequests.get(cacheKey);
    if (pendingRequest) {
      // 返回已存在的请求Promise
      return pendingRequest.promise;
    }

    // 创建新的请求Promise
    const requestPromise = this.createTileRequest(url, cacheKey, options);

    // 添加到待处理请求
    this.pendingRequests.set(cacheKey, {
      promise: requestPromise,
      timestamp: now
    });

    return requestPromise;
  }

  /**
   * 创建瓦片请求
   */
  private async createTileRequest(url: string, cacheKey: string, options?: RequestInit): Promise<Response> {
    try {
      const response = await optimizedFetch(url, options);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      // 克隆响应以便缓存
      const responseClone = response.clone();

      // 异步缓存瓦片数据
      this.cacheTileData(cacheKey, responseClone).catch(error => {
        console.warn('[MapTileThrottler] Failed to cache tile:', error);
      });

      return response;
    } catch (error) {
      console.warn('[MapTileThrottler] Tile request failed:', error);
      throw error;
    } finally {
      // 清理待处理请求
      this.pendingRequests.delete(cacheKey);
    }
  }

  /**
   * 缓存瓦片数据
   */
  private async cacheTileData(cacheKey: string, response: Response): Promise<void> {
    try {
      const blob = await response.blob();
      const now = Date.now();

      // 检查内存限制
      if (this.currentMemoryUsage + blob.size > this.maxMemorySize) {
        this.performLRUCleanup(Math.ceil(this.tileCache.size * 0.2));
      }

      // 创建缓存项
      const cacheItem: TileCacheItem = {
        blob: blob,
        headers: this.extractHeaders(response),
        status: response.status,
        statusText: response.statusText,
        timestamp: now,
        accessCount: 1,
        lastAccessed: now,
        size: blob.size
      };

      // 添加到缓存
      this.tileCache.set(cacheKey, cacheItem);
      this.accessOrder.set(cacheKey, ++this.accessCounter);
      this.currentMemoryUsage += blob.size;

      // 检查缓存大小限制
      if (this.tileCache.size > this.maxCacheSize) {
        this.performLRUCleanup(this.tileCache.size - this.maxCacheSize);
      }

    } catch (error) {
      console.warn('[MapTileThrottler] Failed to cache tile data:', error);
    }
  }

  /**
   * 从缓存数据重新构造Response对象
   */
  private createResponseFromCache(cacheItem: TileCacheItem): Response {
    const headers = new Headers(cacheItem.headers);

    // 克隆Blob以确保每次返回的Response都是独立的
    const clonedBlob = cacheItem.blob.slice();

    return new Response(clonedBlob, {
      status: cacheItem.status,
      statusText: cacheItem.statusText,
      headers: headers
    });
  }

  /**
   * 提取响应头信息
   */
  private extractHeaders(response: Response): Record<string, string> {
    const headers: Record<string, string> = {};
    response.headers.forEach((value, key) => {
      headers[key] = value;
    });
    return headers;
  }

  /**
   * 记录缓存统计
   */
  private logCacheStats() {
    const stats = this.getCacheStats();
  }

  /**
   * 获取缓存统计信息
   */
  getCacheStats() {
    const totalItems = this.tileCache.size;
    const memoryUsage = this.currentMemoryUsage;
    const totalAccess = Array.from(this.tileCache.values())
      .reduce((sum, item) => sum + item.accessCount, 0);
    const averageAccessCount = totalItems > 0 ? totalAccess / totalItems : 0;

    // 准确的命中率计算
    const hitRate = this.totalRequests > 0 ? this.hitCount / this.totalRequests : 0;

    return {
      totalItems,
      memoryUsage,
      maxMemorySize: this.maxMemorySize,
      memoryUsagePercent: (memoryUsage / this.maxMemorySize) * 100,
      averageAccessCount,
      hitRate,
      pendingRequests: this.pendingRequests.size,
      totalRequests: this.totalRequests,
      hitCount: this.hitCount
    };
  }

  /**
   * 清理缓存
   */
  clearCache() {
    this.tileCache.clear();
    this.pendingRequests.clear();
    this.accessOrder.clear();
    this.currentMemoryUsage = 0;
    this.accessCounter = 0;
    this.hitCount = 0;
    this.totalRequests = 0;
  }

  /**
   * 销毁实例
   */
  destroy() {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
    this.clearCache();
  }

  /**
   * 设置缓存配置
   */
  configure(options: {
    maxCacheSize?: number;
    maxMemorySize?: number;
    cacheExpireTime?: number;
  }) {
    if (options.maxCacheSize !== undefined) {
      this.maxCacheSize = options.maxCacheSize;
    }
    if (options.maxMemorySize !== undefined) {
      this.maxMemorySize = options.maxMemorySize;
    }
    if (options.cacheExpireTime !== undefined) {
      this.cacheExpireTime = options.cacheExpireTime;
    }
  }
}

export const mapTileThrottler = new MapTileThrottler();

/**
 * 初始化网络优化
 */
export async function initNetworkOptimization() {
  // 检测网络状态
  await networkMonitor.detectNetworkStatus();

  // 定期检测网络状态
  setInterval(() => {
    networkMonitor.detectNetworkStatus();
  }, 30000); // 每30秒检测一次

  // 监控请求队列状态
  setInterval(() => {
    const status = requestQueueManager.getStatus();
  }, 10000); // 每10秒输出一次状态

}

/**
 * 获取网络优化统计信息
 */
export function getNetworkOptimizationStats() {
  return {
    queueStatus: requestQueueManager.getStatus(),
    networkStatus: networkMonitor.getNetworkStatus(),
    tileCache: mapTileThrottler.getCacheStats()
  };
}
