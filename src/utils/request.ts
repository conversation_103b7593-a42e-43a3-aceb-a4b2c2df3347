import axios, { AxiosResponse, InternalAxiosRequestConfig } from 'axios';
import { useUserStore } from '@/store/modules/user';
import { getToken } from '@/utils/auth';
import { tansParams, blobValidate } from '@/utils/ruoyi';
import cache from '@/plugins/cache';
import { HttpStatus } from '@/enums/RespEnum';
import { errorCode } from '@/utils/errorCode';
import { LoadingInstance } from 'element-plus/es/components/loading/src/loading';
import FileSaver from 'file-saver';
import { getLanguage } from '@/lang';
import { encryptBase64, encryptWithAes, generateAesKey, decryptWithAes, decryptBase64 } from '@/utils/crypto';
import { encrypt, decrypt } from '@/utils/jsencrypt';
import router from '@/router';
import {
  requestQueueManager,
  detectRequestPriority,
  networkMonitor,
  RequestPriority
} from '@/utils/networkOptimizer';

export const Place = 0

const encryptHeader = 'encrypt-key';
let downloadLoadingInstance: LoadingInstance;
// 是否显示重新登录
export const isRelogin = { show: false };
// 是否正在处理登录跳转
let isRedirecting = false;

export const globalHeaders = () => {
  return {
    Authorization: 'Bearer ' + getToken(),
    clientid: import.meta.env.VITE_APP_CLIENT_ID
  };
};

axios.defaults.headers['Content-Type'] = 'application/json;charset=utf-8';
axios.defaults.headers['clientid'] = import.meta.env.VITE_APP_CLIENT_ID;
// 创建 axios 实例
const service = axios.create({
  baseURL: import.meta.env.VITE_APP_BASE_API,
  timeout: 500000
});

// 请求拦截器
service.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    // 网络优化：检测请求优先级并添加到队列管理
    const fullUrl = config.baseURL + config.url;
    const priority = detectRequestPriority(fullUrl);

    // 添加优先级标记到请求头（用于调试和监控）
    config.headers['X-Request-Priority'] = RequestPriority[priority];

    // 如果是慢网络环境，对低优先级请求进行额外延迟
    const networkStatus = networkMonitor.getNetworkStatus();
    if (networkStatus.isSlowNetwork && priority >= RequestPriority.LOW) {
      // 慢网络下延迟低优先级请求
      config.timeout = Math.min(config.timeout || 500000, 30000);
    }

    // 对应国际化资源文件后缀
    config.headers['Content-Language'] = getLanguage();
    // 添加安全相关的HTTP头部
     config.headers['Content-Security-Policy'] = "default-src 'self'"; // 限制资源只能从同源加载
     config.headers['X-Content-Type-Options'] = 'nosniff'; // 禁止MIME类型嗅探
     config.headers['X-XSS-Protection'] = '1; mode=block'; // 启用XSS过滤
     config.headers['X-Download-Options'] = 'noopen'; // 禁止IE下载文件自动打开
     config.headers['X-Permitted-Cross-Domain-Policies'] = 'none'; // 禁止所有跨域策略
     config.headers['Referrer-Policy'] = 'strict-origin-when-cross-origin'; // 控制HTTP请求中的Referer头
     config.headers['X-Frame-Options'] = 'SAMEORIGIN'; // 防止网站被嵌入到非同源的iframe中，避免点击劫持攻击
    const isToken = config.headers?.isToken === false;
    // 是否需要防止数据重复提交
    const isRepeatSubmit = config.headers?.repeatSubmit === false;
    // 是否需要加密
    const isEncrypt = config.headers?.isEncrypt === 'true';

    if (getToken() && !isToken) {
      config.headers['Authorization'] = 'Bearer ' + getToken(); // 让每个请求携带自定义token 请根据实际情况自行修改
    }
    // get请求映射params参数
    if (config.method === 'get' && config.params) {
      let url = config.url + '?' + tansParams(config.params);
      url = url.slice(0, -1);
      config.params = {};
      config.url = url;
    }

    if (!isRepeatSubmit && (config.method === 'post' || config.method === 'put')) {
      const requestObj = {
        url: config.url,
        data: typeof config.data === 'object' ? JSON.stringify(config.data) : config.data,
        time: new Date().getTime()
      };
      const sessionObj = cache.session.getJSON('sessionObj');
      if (sessionObj === undefined || sessionObj === null || sessionObj === '') {
        cache.session.setJSON('sessionObj', requestObj);
      } else {
        const s_url = sessionObj.url; // 请求地址
        const s_data = sessionObj.data; // 请求数据
        const s_time = sessionObj.time; // 请求时间
        const interval = 0; // 间隔时间(ms)，小于此时间视为重复提交
        if (s_data === requestObj.data && requestObj.time - s_time < interval && s_url === requestObj.url) {
          const message = '数据正在处理，请勿重复提交';
          console.warn(`[${s_url}]: ` + message);
          return Promise.reject(new Error(message));
        } else {
          cache.session.setJSON('sessionObj', requestObj);
        }
      }
    }
    if (import.meta.env.VITE_APP_ENCRYPT === 'true') {
      // 当开启参数加密
      if (isEncrypt && (config.method === 'post' || config.method === 'put')) {
        // 生成一个 AES 密钥
        const aesKey = generateAesKey();
        config.headers[encryptHeader] = encrypt(encryptBase64(aesKey));
        config.data = typeof config.data === 'object' ? encryptWithAes(JSON.stringify(config.data), aesKey) : encryptWithAes(config.data, aesKey);
      }
    }
    // FormData数据去请求头Content-Type
    if (config.data instanceof FormData) {
      delete config.headers['Content-Type'];
    }
    return config;
  },
  (error: any) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
service.interceptors.response.use(
  (res: AxiosResponse) => {
    if (import.meta.env.VITE_APP_ENCRYPT === 'true') {
      // 加密后的 AES 秘钥
      const keyStr = res.headers[encryptHeader];

      // 加密
      if (keyStr != null && keyStr != '') {
        const data = res.data;
        // 请求体 AES 解密
        const base64Str = decrypt(keyStr);
        // base64 解码 得到请求头的 AES 秘钥
        const aesKey = decryptBase64(base64Str.toString());
        // aesKey 解码 data
        const decryptData = decryptWithAes(data, aesKey);
        // 将结果 (得到的是 JSON 字符串) 转为 JSON
        res.data = JSON.parse(decryptData);
      }
    }
    // 未设置状态码则默认成功状态
    const code = res.data.code || HttpStatus.SUCCESS;
    // 获取错误信息
    const msg = errorCode[code] || res.data.msg || errorCode['default'];
    // 二进制数据则直接返回
    if (res.request.responseType === 'blob' || res.request.responseType === 'arraybuffer') {
      return res.data;
    }
    if (code === 401) {
      if (!isRedirecting) {
        isRedirecting = true;
        // 清除 token
        useUserStore().logout();
        // 直接跳转到登录页
        router.push({
          path: '/login',
          query: {
            redirect: encodeURIComponent(router.currentRoute.value.fullPath || '/')
          }
        });
        // 重置跳转状态
        setTimeout(() => {
          isRedirecting = false;
        }, 1000);
      }
      return Promise.reject({ code: 401, message: '登录已过期，请重新登录' });
    } else if (code === HttpStatus.SERVER_ERROR) {
      const errorMsg = msg || '服务器错误';
      ElMessage({ message: errorMsg, type: 'error' });
      return Promise.reject({ code, message: errorMsg });
    } else if (code === HttpStatus.WARN) {
      const errorMsg = msg || '警告信息';
      ElMessage({ message: errorMsg, type: 'warning' });
      return Promise.reject({ code, message: errorMsg });
    } else if (code !== HttpStatus.SUCCESS) {
      const errorMsg = msg || '操作失败';
      ElNotification.error({ title: errorMsg });
      return Promise.reject({ code, message: errorMsg });
    } else {
      return res.data;
    }
  },
  (error: any) => {
    // 如果是 401 错误，直接返回
    if (error.response?.status === 401) {
      return Promise.reject({ code: 401, message: '登录已过期，请重新登录' });
    }

    // 处理错误消息
    let errorMsg = '未知错误';

    if (error?.message) {
      if (error.message === 'Network Error') {
        errorMsg = '后端接口连接异常';
      } else if (error.message.includes('timeout')) {
        errorMsg = '系统接口请求超时';
      } else if (error.message.includes('Request failed with status code')) {
        const statusCode = error.message.substr(error.message.length - 3);
        errorMsg = `系统接口${statusCode}异常`;
      } else {
        errorMsg = error.message;
      }
    }

    // 显示错误消息
    ElMessage({
      message: errorMsg,
      type: 'error',
      duration: 5 * 1000
    });

    return Promise.reject({
      code: error.response?.status || 500,
      message: errorMsg
    });
  }
);
// 通用下载方法
export function download(url: string, params: any, fileName: string) {
  downloadLoadingInstance = ElLoading.service({ text: '正在下载数据，请稍候', background: 'rgba(0, 0, 0, 0.7)' });
  // prettier-ignore
  return service.post(url, params, {
      transformRequest: [
        (params: any) => {
          return tansParams(params);
        }
      ],
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      responseType: 'blob'
    }).then(async (resp: any) => {
      const isLogin = blobValidate(resp);
      if (isLogin) {
        const blob = new Blob([resp]);
        FileSaver.saveAs(blob, fileName);
      } else {
        const resText = await resp.data.text();
        const rspObj = JSON.parse(resText);
        const errMsg = errorCode[rspObj.code] || rspObj.msg || errorCode['default'];
        ElMessage.error(errMsg);
      }
      downloadLoadingInstance.close();
    }).catch((r: any) => {
      console.error(r);
      ElMessage.error('下载文件出现错误，请联系管理员！');
      downloadLoadingInstance.close();
    });
}
// 导出 axios 实例
export default service;
