import { computed, onMounted, onUnmounted, ref } from 'vue';
import { useWindowSize } from '@vueuse/core';

/**
 * 屏幕类型枚举
 */
export enum ScreenType {
  PRIMARY_DISPLAY = 'primary-display',
  EXTENDED_DISPLAY = 'extended-display'
}

/**
 * 屏幕适配配置
 */
export interface ScreenAdapterConfig {
  // 扩展屏幕的最小分辨率阈值
  extendedDisplayThreshold: {
    width: number;
    height: number;
  };
  // 扩展屏幕的宽高比阈值（用于检测电视墙等超宽屏）
  extendedDisplayAspectRatio: number;
  // CSS类名配置
  cssClasses: {
    primaryDisplay: string;
    extendedDisplay: string;
  };
  // 是否启用调试模式
  debug: boolean;
}

/**
 * 默认配置
 */
const DEFAULT_CONFIG: ScreenAdapterConfig = {
  extendedDisplayThreshold: {
    width: 4800,
    height: 1620
  },
  // 宽高比大于2.5:1认为是超宽屏（如3:1的电视墙）
  extendedDisplayAspectRatio: 2.5,
  cssClasses: {
    primaryDisplay: 'primary-display',
    extendedDisplay: 'extended-display'
  },
  debug: false
};

/**
 * 屏幕适配检测工具
 * 基于现有的useWindowSize工具扩展，保持架构一致性
 */
export function useScreenAdapter(config: Partial<ScreenAdapterConfig> = {}) {
  // 合并配置
  const adapterConfig = { ...DEFAULT_CONFIG, ...config };

  // 使用现有的useWindowSize工具
  const { width, height } = useWindowSize();

  // 上一次的屏幕类型，用于检测变化
  let previousScreenType: ScreenType | null = null;

  // 全屏状态响应式变量
  const isFullscreen = ref(false);

  /**
   * 检测当前是否处于全屏模式
   * 兼容不同浏览器的fullscreen API
   */
  const detectFullscreenMode = (): boolean => {
    if (typeof document === 'undefined') return false;

    return !!(
      document.fullscreenElement ||
      (document as any).webkitFullscreenElement ||
      (document as any).mozFullScreenElement ||
      (document as any).msFullscreenElement
    );
  };

  /**
   * 更新全屏状态
   */
  const updateFullscreenState = () => {
    const newFullscreenState = detectFullscreenMode();
    const previousState = isFullscreen.value;
    isFullscreen.value = newFullscreenState;

    if (adapterConfig.debug && previousState !== newFullscreenState) {
      console.log(`[ScreenAdapter] 全屏状态变化: ${previousState} → ${newFullscreenState}`);
    }
  };

  /**
   * 初始化全屏事件监听器
   */
  const initFullscreenListeners = () => {
    if (typeof document === 'undefined') return;

    // 兼容不同浏览器的fullscreen事件
    const fullscreenEvents = [
      'fullscreenchange',
      'webkitfullscreenchange',
      'mozfullscreenchange',
      'MSFullscreenChange'
    ];

    fullscreenEvents.forEach(eventName => {
      document.addEventListener(eventName, updateFullscreenState, { passive: true });
    });

    // 初始化全屏状态
    updateFullscreenState();

    if (adapterConfig.debug) {
      console.log('[ScreenAdapter] 全屏事件监听器已初始化');
    }
  };

  /**
   * 清理全屏事件监听器
   */
  const cleanupFullscreenListeners = () => {
    if (typeof document === 'undefined') return;

    const fullscreenEvents = [
      'fullscreenchange',
      'webkitfullscreenchange',
      'mozfullscreenchange',
      'MSFullscreenChange'
    ];

    fullscreenEvents.forEach(eventName => {
      document.removeEventListener(eventName, updateFullscreenState);
    });

    if (adapterConfig.debug) {
      console.log('[ScreenAdapter] 全屏事件监听器已清理');
    }
  };

  /**
   * 检测屏幕类型 - 简化版本，主要基于全屏状态
   */
  const screenType = computed(() => {
    const aspectRatio = width.value / height.value;

    // 简化检测逻辑：主要基于全屏状态
    // 只有在全屏模式下才使用扩展显示模式
    const fullscreenMatch = isFullscreen.value;

    // 可选：保留超宽屏检测（用于电视墙等特殊显示器）
    const aspectRatioMatch = aspectRatio >= adapterConfig.extendedDisplayAspectRatio;

    // 优先使用全屏状态，其次是宽高比
    const isExtendedDisplay = fullscreenMatch || aspectRatioMatch;

    const currentType = isExtendedDisplay ? ScreenType.EXTENDED_DISPLAY : ScreenType.PRIMARY_DISPLAY;

    if (adapterConfig.debug) {
      console.group(`[ScreenAdapter] 屏幕检测详情`);
      console.log(`分辨率: ${width.value}x${height.value}`);
      console.log(`宽高比: ${aspectRatio.toFixed(2)}`);
      console.log(`全屏状态: ${fullscreenMatch}`);
      console.log(`宽高比匹配: ${aspectRatioMatch} (阈值: ${adapterConfig.extendedDisplayAspectRatio})`);
      console.log(`最终类型: ${currentType === ScreenType.EXTENDED_DISPLAY ? 'Extended' : 'Primary'}`);

      // 检测屏幕类型变化
      if (previousScreenType !== null && previousScreenType !== currentType) {
        console.warn(`[ScreenAdapter] 屏幕类型变化: ${previousScreenType} → ${currentType}`);
      }
      console.groupEnd();
    }

    // 更新上一次的屏幕类型
    previousScreenType = currentType;

    return currentType;
  });

  /**
   * 获取当前应该应用的CSS类名
   */
  const currentCssClass = computed(() => {
    return screenType.value === ScreenType.EXTENDED_DISPLAY
      ? adapterConfig.cssClasses.extendedDisplay
      : adapterConfig.cssClasses.primaryDisplay;
  });

  /**
   * 应用CSS类到body元素
   */
  const applyCssClass = () => {
    if (typeof document === 'undefined') {
      if (adapterConfig.debug) {
        console.warn('[ScreenAdapter] document未定义，跳过CSS类应用');
      }
      return;
    }

    const body = document.body;
    const startTime = performance.now();

    // 记录应用前的状态
    const beforeClasses = Array.from(body.classList);

    // 移除所有屏幕类型相关的CSS类
    body.classList.remove(
      adapterConfig.cssClasses.primaryDisplay,
      adapterConfig.cssClasses.extendedDisplay
    );

    // 添加当前屏幕类型对应的CSS类
    body.classList.add(currentCssClass.value);

    const endTime = performance.now();
    const duration = endTime - startTime;

    if (adapterConfig.debug) {
      console.group(`[ScreenAdapter] CSS类应用详情`);
      console.log(`应用前类名: [${beforeClasses.join(', ')}]`);
      console.log(`移除类名: [${adapterConfig.cssClasses.primaryDisplay}, ${adapterConfig.cssClasses.extendedDisplay}]`);
      console.log(`添加类名: ${currentCssClass.value}`);
      console.log(`应用后类名: [${Array.from(body.classList).join(', ')}]`);
      console.log(`执行耗时: ${duration.toFixed(2)}ms`);
      console.groupEnd();
    }
  };

  /**
   * 检查DOM是否准备就绪
   */
  const isDOMReady = () => {
    return typeof document !== 'undefined' &&
           (document.readyState === 'complete' || document.readyState === 'interactive');
  };

  /**
   * 等待DOM准备就绪
   */
  const waitForDOM = (): Promise<void> => {
    return new Promise((resolve) => {
      if (isDOMReady()) {
        resolve();
        return;
      }

      const handleDOMReady = () => {
        document.removeEventListener('DOMContentLoaded', handleDOMReady);
        resolve();
      };

      document.addEventListener('DOMContentLoaded', handleDOMReady);
    });
  };

  /**
   * 初始化屏幕适配
   */
  const initScreenAdapter = async () => {
    const startTime = performance.now();

    if (adapterConfig.debug) {
      console.group('[ScreenAdapter] 初始化开始');
      console.log(`DOM状态: ${typeof document !== 'undefined' ? document.readyState : 'undefined'}`);
    }

    // 等待DOM准备就绪
    await waitForDOM();

    // 初始化全屏事件监听器
    initFullscreenListeners();

    // 立即应用CSS类
    applyCssClass();

    const endTime = performance.now();
    const duration = endTime - startTime;

    if (adapterConfig.debug) {
      console.log(`初始化耗时: ${duration.toFixed(2)}ms`);
      console.log('[ScreenAdapter] 屏幕适配初始化完成');
      console.groupEnd();
    }
  };

  /**
   * 清理屏幕适配
   */
  const cleanupScreenAdapter = () => {
    if (typeof document === 'undefined') return;

    // 清理全屏事件监听器
    cleanupFullscreenListeners();

    const body = document.body;
    body.classList.remove(
      adapterConfig.cssClasses.primaryDisplay,
      adapterConfig.cssClasses.extendedDisplay
    );

    if (adapterConfig.debug) {
      console.log('[ScreenAdapter] 屏幕适配清理完成');
    }
  };

  /**
   * 监听屏幕类型变化并自动应用CSS类
   */
  const watchScreenTypeChange = () => {
    // 监听screenType变化，自动应用CSS类
    return computed(() => {
      const currentType = screenType.value;

      if (adapterConfig.debug) {
        console.log(`[ScreenAdapter] 屏幕类型监听触发: ${currentType}`);
      }

      applyCssClass();
      return currentType;
    });
  };

  /**
   * 获取屏幕适配状态信息
   */
  const getAdapterStatus = () => {
    return {
      screenType: screenType.value,
      resolution: `${width.value}x${height.value}`,
      aspectRatio: (width.value / height.value).toFixed(2),
      cssClass: currentCssClass.value,
      isExtended: screenType.value === ScreenType.EXTENDED_DISPLAY,
      isFullscreen: isFullscreen.value,
      fullscreenDetected: detectFullscreenMode(),
      thresholds: {
        resolution: `${adapterConfig.extendedDisplayThreshold.width}x${adapterConfig.extendedDisplayThreshold.height}`,
        aspectRatio: adapterConfig.extendedDisplayAspectRatio
      },
      timestamp: new Date().toISOString()
    };
  };

  /**
   * 打印屏幕适配状态
   */
  const printAdapterStatus = () => {
    const status = getAdapterStatus();
    console.table(status);
  };

  return {
    // 响应式数据
    screenType,
    currentCssClass,
    width,
    height,
    isFullscreen,

    // 方法
    initScreenAdapter,
    cleanupScreenAdapter,
    applyCssClass,
    watchScreenTypeChange,
    getAdapterStatus,
    printAdapterStatus,
    detectFullscreenMode,
    updateFullscreenState,

    // 配置
    config: adapterConfig
  };
}

/**
 * 全局屏幕适配管理器
 * 用于在应用级别管理屏幕适配
 */
export class ScreenAdapterManager {
  private static instance: ScreenAdapterManager | null = null;
  private adapter: ReturnType<typeof useScreenAdapter> | null = null;
  private isInitialized = false;

  private constructor() {}

  /**
   * 获取单例实例
   */
  static getInstance(): ScreenAdapterManager {
    if (!ScreenAdapterManager.instance) {
      ScreenAdapterManager.instance = new ScreenAdapterManager();
    }
    return ScreenAdapterManager.instance;
  }

  /**
   * 初始化全局屏幕适配
   */
  init(config?: Partial<ScreenAdapterConfig>) {
    if (this.isInitialized) {
      console.warn('[ScreenAdapterManager] 屏幕适配已经初始化');
      return;
    }

    this.adapter = useScreenAdapter(config);
    this.adapter.initScreenAdapter();

    // 监听屏幕类型变化
    this.adapter.watchScreenTypeChange();

    this.isInitialized = true;
  }

  /**
   * 清理全局屏幕适配
   */
  cleanup() {
    if (this.adapter) {
      this.adapter.cleanupScreenAdapter();
      this.adapter = null;
    }
    this.isInitialized = false;
  }

  /**
   * 获取当前屏幕类型
   */
  getCurrentScreenType(): ScreenType | null {
    return this.adapter?.screenType.value || null;
  }

  /**
   * 获取当前CSS类名
   */
  getCurrentCssClass(): string | null {
    return this.adapter?.currentCssClass.value || null;
  }
}

/**
 * 导出默认实例
 */
export const screenAdapterManager = ScreenAdapterManager.getInstance();
