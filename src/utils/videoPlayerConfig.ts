/**
 * EasyPlayerPro 视频播放器配置工具
 * 用于优化视频流延迟和播放性能
 */

/**
 * 获取低延迟实时播放配置 - 平衡延迟和稳定性
 */
export function getLowLatencyConfig() {
  return {
    isLive: true,
    bufferTime: 0.8, // 适度缓冲时间，避免网络波动影响
    videoBuffer: 200, // 适中的视频缓冲区
    videoBufferDelay: 500, // 适中的缓冲延迟
    networkDelay: 8000, // 增加网络延迟容忍度，减少频繁重连
    timeout: 15, // 增加连接超时时间，减少频繁重试
    heartTimeout: 30, // 增加心跳超时时间，减少心跳频率
    loadingTimeout: 15, // 增加加载超时时间
    // 性能优化 - 保守设置
    useWasm: true,
    useMSE: true,
    useWCS: true,
    forceNoOffscreen: false,
    autoWasm: true,
    checkFirstIFrame: true, // 启用首帧检查，提高稳定性
    // 音视频同步 - 放宽同步要求
    syncAudioAndVideo: true,
    syncAudioAndVideoDiff: 300,
    // 错误处理 - 减少自动重连频率
    playFailedAndReplay: false, // 禁用播放失败自动重播，减少请求
    mseDecodeErrorReplay: false, // 禁用MSE解码错误重播
    wcsDecodeErrorReplay: false, // 禁用WCS解码错误重播
    wasmDecodeErrorReplay: false, // 禁用WASM解码错误重播
    // 其他优化
    isResize: true,
    debug: false,
    stretch: false,
    hasAudio: true,
    MSE: true,
    WCS: true
  };
}

/**
 * 获取回放模式配置 - 稳定性优先
 */
export function getPlaybackConfig() {
  return {
    isLive: false,
    bufferTime: 1.5, // 回放模式更高缓冲，确保流畅播放
    videoBuffer: 400,
    videoBufferDelay: 800,
    networkDelay: 12000, // 回放模式增加网络延迟容忍度
    timeout: 20, // 回放模式增加连接超时时间
    heartTimeout: 60, // 回放模式大幅增加心跳超时时间
    loadingTimeout: 20, // 增加加载超时时间
    // 性能优化
    useWasm: true,
    useMSE: true,
    useWCS: true,
    forceNoOffscreen: false,
    autoWasm: true,
    checkFirstIFrame: true, // 回放模式检查首帧
    // 音视频同步
    syncAudioAndVideo: true,
    syncAudioAndVideoDiff: 400,
    // 错误处理 - 回放模式减少重连频率
    playFailedAndReplay: false, // 回放模式禁用自动重播
    mseDecodeErrorReplay: false, // 禁用MSE解码错误重播
    wcsDecodeErrorReplay: false, // 禁用WCS解码错误重播
    wasmDecodeErrorReplay: false, // 禁用WASM解码错误重播
    // 其他优化
    isResize: true,
    debug: false,
    stretch: false,
    hasAudio: true,
    MSE: true,
    WCS: true
  };
}

/**
 * 获取基础配置（通用设置）
 */
export function getBaseConfig() {
  return {
    // 基础设置
    isResize: true,
    isFullResize: false,
    debug: false,
    stretch: false,
    hasAudio: true,
    hasVideo: true,

    // 性能设置
    MSE: true,
    WCS: true,
    useWasm: true,
    useMSE: true,
    useWCS: true,
    useSIMD: false,
    useMThreading: false,

    // 渲染设置
    wcsUseVideoRender: true,
    wcsUseWebgl2Render: true,
    wasmUseVideoRender: true,
    mseUseCanvasRender: false,
    hlsUseCanvasRender: false,
    webrtcUseCanvasRender: false,
    useOffscreen: false,
    useWebGPU: false,

    // 错误处理
    mseDecodeErrorReplay: true,
    wcsDecodeErrorReplay: true,
    wasmDecodeErrorReplay: true,
    simdDecodeErrorReplay: true,
    autoWasm: true,
    decoderErrorAutoWasm: true,
    hardDecodingNotSupportAutoWasm: true,
    webglAlignmentErrorReplay: true,
    webglContextLostErrorReplay: true,

    // 控制设置
    operateBtns: {
      fullscreen: true,
      screenshot: false,
      stretch: false,
      play: false,
      audio: false,
      record: false,
      ptz: false,
      quality: false,
      zoom: false,
      close: false,
      scale: false,
      logSave: false
    },

    // 其他设置
    controlAutoHide: false,
    hasControl: true,
    loadingIcon: true,
    backgroundLoadingShow: true,
    hiddenAutoPause: false,
    keepScreenOn: true,
    muted: false,
    isNotMute: true
  };
}

/**
 * 合并配置
 * @param isPlaybackMode 是否为回放模式
 * @param customConfig 自定义配置
 */
export function mergeConfig(isPlaybackMode: boolean = false, customConfig: any = {}) {
  const baseConfig = getBaseConfig();
  const modeConfig = isPlaybackMode ? getPlaybackConfig() : getLowLatencyConfig();

  return {
    ...baseConfig,
    ...modeConfig,
    ...customConfig
  };
}

/**
 * 视频播放器性能监控配置
 */
export const performanceConfig = {
  // 延迟警告阈值（毫秒）
  delayWarningThreshold: 1000,
  // 帧率警告阈值
  fpsWarningThreshold: 20,
  // 缓冲警告阈值（毫秒）
  bufferWarningThreshold: 2000,
  // 性能监控间隔（毫秒）
  monitorInterval: 5000
};

/**
 * 双击全屏配置
 */
export const fullscreenConfig = {
  // 双击延迟（毫秒）
  doubleClickDelay: 300,
  // 全屏退出键
  exitKeys: ['Escape', 'F11'],
  // 全屏样式
  fullscreenClass: 'video-fullscreen'
};

/**
 * 事件监听器配置
 * 解决Chrome非被动事件监听器警告
 */
export const eventListenerConfig = {
  // 被动事件选项
  passiveOptions: { passive: true },
  // 非被动事件选项（需要preventDefault的事件）
  nonPassiveOptions: { passive: false },
  // 需要被动处理的事件类型
  passiveEvents: [
    'wheel',
    'touchstart',
    'touchmove',
    'touchend',
    'scroll',
    'mousewheel'
  ],
  // 需要非被动处理的事件类型
  nonPassiveEvents: [
    'keydown',
    'keyup',
    'contextmenu'
  ]
};

/**
 * 获取网络优化配置 - 专门用于减少频繁请求
 */
export function getNetworkOptimizedConfig(isPlaybackMode: boolean = false) {
  const baseConfig = isPlaybackMode ? getPlaybackConfig() : getLowLatencyConfig();

  return {
    ...baseConfig,
    // 进一步优化网络请求频率
    heartTimeout: isPlaybackMode ? 120 : 60, // 大幅增加心跳超时时间
    timeout: isPlaybackMode ? 30 : 20, // 增加连接超时时间
    networkDelay: isPlaybackMode ? 15000 : 12000, // 增加网络延迟容忍度
    loadingTimeout: isPlaybackMode ? 30 : 20, // 增加加载超时时间

    // 完全禁用所有自动重连功能
    playFailedAndReplay: false,
    mseDecodeErrorReplay: false,
    wcsDecodeErrorReplay: false,
    wasmDecodeErrorReplay: false,
    simdDecodeErrorReplay: false,
    decoderErrorAutoWasm: false,
    hardDecodingNotSupportAutoWasm: false,
    webglAlignmentErrorReplay: false,
    webglContextLostErrorReplay: false,

    // 减少缓冲检查频率
    bufferTime: isPlaybackMode ? 2.0 : 1.2,
    videoBuffer: isPlaybackMode ? 600 : 300,
    videoBufferDelay: isPlaybackMode ? 1200 : 800,
  };
}

/**
 * 获取优化的EasyPlayerPro配置，包含事件处理优化
 */
export function getOptimizedConfig(isPlaybackMode: boolean = false) {
  const baseConfig = getNetworkOptimizedConfig(isPlaybackMode);

  return {
    ...baseConfig,
    // 禁用一些可能导致非被动事件的功能
    supportDblclickFullscreen: false, // 禁用内置双击全屏，使用自定义实现
    hotKey: false, // 禁用热键，避免键盘事件冲突
    controlAutoHide: true, // 启用控制栏自动隐藏
    // 优化触摸和滚轮事件处理
    supportWheel: false, // 禁用内置滚轮支持
    // 其他优化
    disableContextmenu: true, // 禁用右键菜单，减少事件冲突
  };
}
