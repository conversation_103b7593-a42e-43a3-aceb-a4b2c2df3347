<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->
<template>
    <div class="relative w-full h-screen">
        <!-- 地图背景 -->
        <div class="w-full h-full bg-cover bg-center" :style="{ backgroundImage: `url('${mapBackground}')` }"></div>
        <!-- 右侧功能按钮组 -->
        <div class="absolute right-4 top-1/4 flex flex-col gap-4 z-10">
            <button
                class="bg-gray-800 bg-opacity-70 p-3 rounded-lg text-white hover:bg-opacity-90 !rounded-button whitespace-nowrap">
                <el-icon class="text-xl">
                    <Location />
                </el-icon>
            </button>
            <button
                class="bg-gray-800 bg-opacity-70 p-3 rounded-lg text-white hover:bg-opacity-90 !rounded-button whitespace-nowrap">
                <el-icon class="text-xl">
                    <Aim />
                </el-icon>
            </button>
            <button
                class="bg-gray-800 bg-opacity-70 p-3 rounded-lg text-white hover:bg-opacity-90 !rounded-button whitespace-nowrap">
                <el-icon class="text-xl">
                    <VideoPlay />
                </el-icon>
            </button>
            <button
                class="bg-gray-800 bg-opacity-70 p-3 rounded-lg text-white hover:bg-opacity-90 !rounded-button whitespace-nowrap">
                <el-icon class="text-xl">
                    <RefreshRight />
                </el-icon>
            </button>
        </div>
        <!-- 鼠标悬浮信息窗 -->
        <div v-if="showHoverInfo"
            class="absolute left-1/3 top-1/2 bg-gray-900 bg-opacity-70 p-4 rounded-lg text-white min-w-[300px]">
            <h3 class="text-lg mb-4 border-b border-gray-600 pb-2">远洋1</h3>
            <div class="space-y-2">
                <div class="flex justify-between">
                    <span>MMSI:</span>
                    <span>45215866200</span>
                </div>
                <div class="flex justify-between">
                    <span>经度:</span>
                    <span>30.210899</span>
                </div>
                <div class="flex justify-between">
                    <span>纬度:</span>
                    <span>121.816811</span>
                </div>
                <div class="flex justify-between">
                    <span>航速:</span>
                    <span>0节</span>
                </div>
                <div class="flex justify-between">
                    <span>类型:</span>
                    <span>其他</span>
                </div>
            </div>
        </div>
        <!-- 右侧详细信息窗 -->
        <div class="absolute right-20 top-10 bg-gray-900 bg-opacity-70 p-6 rounded-lg text-white min-w-[400px]">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-xl font-bold">远洋1</h2>
                <el-icon class="text-xl cursor-pointer">
                    <Close />
                </el-icon>
            </div>
            <div class="space-y-4">
                <div class="flex justify-between">
                    <span>英文船名:</span>
                    <span>YUANYANG1</span>
                </div>
                <div class="flex justify-between">
                    <span>MMSI:</span>
                    <span>45215866200</span>
                </div>
                <div class="flex justify-between">
                    <span>船长:</span>
                    <span>-</span>
                </div>
                <div class="flex justify-between">
                    <span>船宽:</span>
                    <span>-</span>
                </div>
                <div class="flex justify-between">
                    <span>经度:</span>
                    <span>30.210899</span>
                </div>
                <div class="flex justify-between">
                    <span>纬度:</span>
                    <span>121.816811</span>
                </div>
                <div class="flex justify-between">
                    <span>航速:</span>
                    <span>0节</span>
                </div>
                <div class="flex justify-between">
                    <span>航向:</span>
                    <span>360</span>
                </div>
                <div class="flex justify-between">
                    <span>预抵港:</span>
                    <span>NINGBO</span>
                </div>
                <div class="flex justify-between">
                    <span>状态:</span>
                    <span>停泊</span>
                </div>
            </div>
            <!-- 时间轴 -->
            <div class="mt-8">
                <div class="flex justify-between text-sm mb-2">
                    <span>船舶轨迹:</span>
                    <div class="flex gap-4">
                        <span>2h</span>
                        <span>4h</span>
                        <span>8h</span>
                        <span>12h</span>
                        <span>24h</span>
                        <span>48h</span>
                    </div>
                </div>
                <div class="w-full bg-gray-700 h-1 rounded-full">
                    <div class="bg-blue-500 w-1/3 h-full rounded-full"></div>
                </div>
            </div>
        </div>
    </div>
</template>
<script lang="ts" setup>
import { ref } from 'vue';
import { Location, Aim, VideoPlay, RefreshRight, Close } from '@element-plus/icons-vue';
const showHoverInfo = ref(true);
const mapBackground = 'https://ai-public.mastergo.com/ai/img_res/c76a240fba5677382076e06748ad03ec.jpg';
// 移除不需要的数组定义
</script>
<style scoped>
.el-icon {
    display: inline-flex;
    justify-content: center;
    align-items: center;
}

.bg-cover {
    background-size: cover;
    background-repeat: no-repeat;
}

.bg-center {
    background-position: center;
}

.min-w-\[300px\] {
    min-width: 300px;
}

.min-w-\[400px\] {
    min-width: 400px;
}

.space-y-2>*+* {
    margin-top: 0.5rem;
}

.space-y-4>*+* {
    margin-top: 1rem;
}

button:hover {
    transition: all 0.3s ease;
}

.rounded-lg {
    border-radius: 0.5rem;
}

.bg-opacity-70 {
    background-color: rgba(31, 41, 55, 0.7);
}

.h-1 {
    height: 0.25rem;
}

.rounded-full {
    border-radius: 9999px;
}

.bg-blue-500 {
    background-color: #3b82f6;
}

.cursor-pointer {
    cursor: pointer;
}

.text-xl {
    font-size: 1.25rem;
    line-height: 1.75rem;
}

.text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
}

.text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
}

.font-bold {
    font-weight: 700;
}

.text-white {
    color: #ffffff;
}
</style>