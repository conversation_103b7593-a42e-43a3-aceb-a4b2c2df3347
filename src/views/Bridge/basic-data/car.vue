<template>
  <div class="bridge-monitor">
    <!-- 顶部导航栏 -->
    <div class="header">
      <div class="logo">
        <!-- <img src="@/assets/logo.png" alt="logo"> -->
        <span>舟山跨海大桥综合监管指挥平台</span>
      </div>
      <div class="weather">
        <span>13℃</span>
        <span>多云转晴</span>
      </div>
    </div>

    <!-- 左侧菜单 -->
    <div class="left-menu">
      <div class="menu-item">
        <i class="icon-bridge"></i>
        <span>大桥概况</span>
      </div>
      <div class="menu-item">
        <i class="icon-notice"></i>
        <span>必势通知</span>
      </div>
      <!-- 其他菜单项... -->
    </div>

    <!-- 主要地图区域 -->
    <div class="main-map">
      <!-- 这里需要接入地图组件 -->
      <!-- 注释: 需要接入GIS地图，显示跨海大桥及周边区域 -->
    </div>

    <!-- 右侧信息面板 -->
    <div class="right-panel">
      <!-- 预警信息 -->
      <div class="warning-info">
        <div class="panel-title">预警信息</div>
        <div class="warning-list">
          <!-- 预警信息列表 -->
          <div class="warning-item" v-for="(item, index) in 3" :key="index">
            <div class="warning-header">
              <span class="warning-type">XXXXXXXX</span>
              <span class="warning-status">⭐</span>
            </div>
            <div class="warning-content">
              <div>事件类型: XXXX</div>
              <div>发生时间: 2024-xx-xx xx:xx:xx</div>
              <div>事件位置: 金塘大桥K14+145</div>
            </div>
            <div class="warning-footer">
              <button class="btn-detail">详细信息</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 组件逻辑
</script>

<style lang="scss" scoped>
.bridge-monitor {
  width: 100vw;
  height: 100vh;
  background: #0f1624;
  color: #fff;
  position: relative;
  overflow: hidden;

  .header {
    height: 60px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    background: rgba(15, 22, 36, 0.8);

    .logo {
      display: flex;
      align-items: center;
      img {
        height: 40px;
        margin-right: 10px;
      }
    }

    .weather {
      font-size: 14px;
      span {
        margin-left: 10px;
      }
    }
  }

  .left-menu {
    position: absolute;
    left: 0;
    top: 60px;
    width: 200px;
    background: rgba(15, 22, 36, 0.8);
    
    .menu-item {
      height: 50px;
      display: flex;
      align-items: center;
      padding: 0 20px;
      cursor: pointer;
      
      &:hover {
        background: rgba(255, 255, 255, 0.1);
      }

      i {
        margin-right: 10px;
      }
    }
  }

  .right-panel {
    position: absolute;
    right: 0;
    top: 60px;
    width: 350px;
    height: calc(100vh - 60px);
    background: rgba(15, 22, 36, 0.8);
    
    .warning-info {
      padding: 15px;

      .panel-title {
        font-size: 16px;
        margin-bottom: 15px;
      }

      .warning-item {
        background: rgba(255, 255, 255, 0.05);
        border-radius: 4px;
        padding: 15px;
        margin-bottom: 10px;

        .warning-header {
          display: flex;
          justify-content: space-between;
          margin-bottom: 10px;
        }

        .warning-content {
          font-size: 14px;
          line-height: 1.5;
        }

        .warning-footer {
          margin-top: 10px;
          text-align: right;

          .btn-detail {
            background: #1890ff;
            border: none;
            color: white;
            padding: 5px 15px;
            border-radius: 4px;
            cursor: pointer;
          }
        }
      }
    }
  }

  .main-map {
    position: absolute;
    left: 200px;
    top: 60px;
    right: 350px;
    bottom: 0;
    background: #1a1a1a;
    // 注释: 这里需要接入实际的地图组件
  }
}
</style>
