<template>
  <div class="bridge-form">
    <div class="form-header">
      <div class="title">{{ isEdit ? '编辑桥梁' : '新增桥梁' }}</div>
    </div>

    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      class="form-content"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="桥梁名称" prop="bridgeName">
            <el-input v-model="form.bridgeName" placeholder="请输入桥梁名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="桥梁类型" prop="bridgeType">
            <el-input v-model="form.bridgeType" placeholder="请输入桥梁类型" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="所属区域" prop="region">
            <el-input v-model="form.region" placeholder="请输入所属区域" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="建成日期" prop="buildDate">
            <el-date-picker
              v-model="form.buildDate"
              type="date"
              placeholder="选择日期时间"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              :default-time="new Date(2000, 1, 1, 0, 0, 0)"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="桥梁长度(米)" prop="bridgeLength">
            <el-input-number
              v-model="form.bridgeLength"
              :min="0"
              :precision="2"
              style="width: 100%"
              placeholder="请输入桥梁长度(米)"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="桥梁宽度(米)" prop="bridgeWidth">
            <el-input-number
              v-model="form.bridgeWidth"
              :min="0"
              :precision="2"
              style="width: 100%"
              placeholder="请输入桥梁宽度(米)"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="桥梁高度(米)" prop="bridgeHeight">
            <el-input-number
              v-model="form.bridgeHeight"
              :min="0"
              :precision="2"
              style="width: 100%"
              placeholder="请输入桥梁高度(米)"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="设计荷载(吨)" prop="designLoad">
            <el-input-number
              v-model="form.designLoad"
              :min="0"
              :precision="2"
              style="width: 100%"
              placeholder="请输入设计荷载(吨)"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="备注" prop="remarks">
            <el-input
              v-model="form.remarks"
              type="textarea"
              :rows="3"
              placeholder="请输入备注信息"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <div class="form-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="handleSubmit">确 定</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, defineProps, defineEmits } from 'vue'
import { ElMessage } from 'element-plus'
import { Close } from '@element-plus/icons-vue'
import { addBridge, updateBridge } from '@/api/bridge/bisc/bridge'
import dayjs from 'dayjs'

const props = defineProps({
  id: {
    type: String,
    default: ''
  },
  isEdit: {
    type: Boolean,
    default: false
  },
  editData: {
    type: Object,
    default: () => ({})
  }
})
function initForm() {
  form.value.bridgeHeight = props.editData.bridgeHeight
  form.value.bridgeWidth = props.editData.bridgeWidth
  form.value.bridgeLength = props.editData.bridgeLength
  form.value.bridgeName = props.editData.bridgeName
  form.value.bridgeType = props.editData.bridgeType
  form.value.buildDate = props.editData.buildDate
  form.value.region = props.editData.region
  form.value.remarks = props.editData.remarks
  form.value.designLoad = props.editData.designLoad
}

const emit = defineEmits(['success', 'close'])

const formRef = ref()
const form = ref({
  bridgeName: '',
  bridgeType: '',
  bridgeLength: 0,
  bridgeWidth: 0,
  bridgeHeight: 0,
  designLoad: 0,
  buildDate: dayjs().format('YYYY-MM-DD HH:mm:ss'),
  region: '',
  remarks: ''
})

const rules = {
  bridgeName: [{ required: true, message: '请输入桥梁名称', trigger: 'blur' }],
  bridgeType: [{ required: true, message: '请选择桥梁类型', trigger: 'change' }],
  bridgeLength: [{ required: true, message: '请输入桥梁长度', trigger: 'blur' }],
  bridgeWidth: [{ required: true, message: '请输入桥梁宽度', trigger: 'blur' }],
  region: [{ required: true, message: '请输入所属区域', trigger: 'blur' }],
  buildDate: [{ required: true, message: '请选择建成日期', trigger: 'change' }]
}

const handleClose = () => {
  emit('close')
}

const handleSubmit = async () => {
  if (!formRef.value) return
  await formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      try {
        const submitData = {
          ...form.value,
          id: props.id,
          buildDate: form.value.buildDate ? dayjs(form.value.buildDate).format('YYYY-MM-DD HH:mm:ss') : undefined
        }
        
        if (props.isEdit) {
          await updateBridge(submitData)
        } else {
          await addBridge(submitData)
        }
        ElMessage.success(props.isEdit ? '修改成功' : '新增成功')
        emit('success')
      } catch (error) {
        console.error(props.isEdit ? '修改失败' : '新增失败', error)
      }
    }
  })
}
initForm();
</script>

<style scoped lang="scss">

.bridge-form {
  background: transparent;
  border-radius: 0;
  box-shadow: none;
  
  .form-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 0;
    background: transparent;
    border-radius: 0;
    border-bottom: none;
    padding: 16px 24px;
    background: var(--el-bg-color-page);
    border-radius: 8px 8px 0 0;
    border-bottom: 1px solid var(--el-border-color-light);
    

    
    .title {
      font-size: 18px;
      color: #303133;
      font-weight: 600;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .close-btn {
      transition: all 0.3s;
      padding: 10px;
      border-radius: 6px;
      
      &:hover {
        background-color: #f4f4f5;
        color: #909399;
      }
    }
  }
  
  .form-content {
    padding: 24px 0;
    background: transparent;

    :deep(.el-form-item) {
      margin-bottom: 24px;
      
      .el-form-item__label {
        color: #606266;
        font-weight: 500;
      }
    }

    :deep(.el-input),
    :deep(.el-select),
    :deep(.el-date-editor),
    :deep(.el-input-number) {
      width: 100%;
    }

    :deep(.el-input-number) {
      .el-input__inner {
        text-align: left;
      }
    }
  }
  
  .form-footer {
    padding: 16px 0;
    text-align: right;
    border-top: none;
    background: transparent;
    border-radius: 0;

    .el-button {
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      padding: 10px 24px;
      border-radius: 6px;
      
      &:hover {
        transform: translateY(-1px);
      }

      &.el-button--primary:hover {
        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
      }
    }
  }
}
</style> 