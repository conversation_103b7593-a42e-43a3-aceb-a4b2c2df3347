<template>
  <div class="list-page">
    <!-- 分类选择和搜索区域 -->
    <div class="filter-area">
      <el-radio-group v-model="filterType" class="filter-type" style="width: 30%;">
        <el-radio label="全部">全部</el-radio>
        <el-radio label="待处理">未处理</el-radio>
        <el-radio label="已处理">已处理</el-radio>
      </el-radio-group>
      <div style="width: 70%; display: flex; justify-content: flex-end; gap: 10px;">
        <el-button type="primary" class="add-button" @click="handleAdd">新增</el-button>
      </div>
    </div>
    <!-- 列表区域 -->
    <el-table :data="tableData" border style="width: 100%">
      <el-table-column type="selection" width="55"></el-table-column>
      <el-table-column label="序号" prop="index" width="80" align="center">
        <template #default="scope">
          {{ scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column
        label="发生原因"
        prop="trafficAccidentWarning.blockageCause"
        min-width="180"
      >
        <template #default="scope">
          {{
            getDictLabel(
              "blockage_cause",
              scope.row.trafficAccidentWarning?.blockageCause
            )
          }}
        </template>
      </el-table-column>
      <el-table-column
        label="发生时间"
        prop="trafficAccidentWarning.occurrenceTime"
        width="200"
        align="center"
      ></el-table-column>
      <el-table-column
        label="发生地点"
        prop="trafficAccidentWarning.location"
        width="250"
      ></el-table-column>
      <el-table-column
        label="预计恢复时间"
        prop="trafficAccidentWarning.recoveryTime"
        width="180"
        align="center"
      >
        <template #default="scope">
          {{ scope.row.trafficAccidentWarning?.recoveryTime + "小时" }}
        </template>
      </el-table-column>
      <el-table-column
        label="事故类型"
        prop="trafficAccidentWarning.accidentType"
        width="180"
        align="center"
      >
        <template #default="scope">
          {{
            getDictLabel("accident_type", scope.row.trafficAccidentWarning?.accidentType)
          }}
        </template>
      </el-table-column>
      <el-table-column
        label="预警级别"
        prop="earlyWarningInfo.warnLevel"
        width="200"
        align="center"
      >
        <template #default="scope">
          <el-tooltip
            :content="`${scope.row.earlyWarningInfo?.warnLevel}级预警`"
            placement="top"
          >
            <img
              :src="getAlertImage(scope.row.earlyWarningInfo?.warnLevel)"
              style="width: 32px; height: 32px"
            />
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column
        label="处理状态"
        prop="trafficAccidentWarning.status"
        width="120"
        align="center"
      >
        <template #default="scope">
          <el-tag
            :type="scope.row.earlyWarningInfo?.status ? 'success' : 'warning'"
          >
            {{ scope.row.earlyWarningInfo?.status ? "已处理" : "未处理" }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="180" fixed="right" align="center">
        <template #default="scope">
          <el-button size="small" type="primary" link @click="viewDetails(scope.row)">详情</el-button>
          <!-- <el-button 
            v-if="scope.row.earlyWarningInfo?.status"
            size="small" 
            type="info" 
            link 
            @click="viewDetails(scope.row)"
          >
            结果
          </el-button> -->
          <el-button 
            size="small" 
            type="danger" 
            link 
            @click="handleDelete(scope.row)"
            v-perms="['bridge:traffic:remove']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[5, 10, 15, 20]"
        :background="true"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @current-change="handlePageChange"
        @size-change="handleSizeChange"
      />
    </div>

    <!-- 详情弹窗 -->
    <el-dialog
      v-model="detailVisible"
      title="交通事故预警详情"
      width="1000px"
      center
      destroy-on-close
    >
      <template #header>
        <div class="dialog-header">
          <span class="dialog-title">事故影响预警详情</span>
          <div class="action-buttons">
            <el-button 
              v-if="!isEditing"
              type="primary" 
              @click="enableEditing"
              class="edit-btn"
            >
              <i class="el-icon-edit" /> 编辑
            </el-button>
            <el-button 
              v-else
              type="info"
              @click="cancelEditing"
              class="cancel-btn"
            >
              <i class="el-icon-close" /> 取消
            </el-button>
          </div>
        </div>
      </template>
      <el-form :model="editForm" :rules="editRules" ref="editFormRef" label-width="180px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="事故类型：" prop="trafficAccidentWarning.accidentType">
              <el-select 
                v-model="editForm.trafficAccidentWarning.accidentType"
                placeholder="请选择事故类型"
                style="width:100%"
                :disabled="!isEditing"
              >
                <el-option
                  v-for="item in accidentTypeDict"
                  :key="item.dictValue"
                  :label="item.dictLabel"
                  :value="item.dictValue"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="发生地点：" prop="trafficAccidentWarning.location">
              <el-input 
                v-model="editForm.trafficAccidentWarning.location"
                :disabled="!isEditing"
              />
            </el-form-item>

            <el-form-item label="发生时间：">
              <el-input 
                v-model="editForm.trafficAccidentWarning.occurrenceTime" 
                disabled
              />
            </el-form-item>

            <el-form-item label="大桥损毁：" prop="trafficAccidentWarning.bridgeDamage">
              <el-input 
                v-model="editForm.trafficAccidentWarning.bridgeDamage"
                type="textarea"
                :rows="2"
                :disabled="!isEditing"
              />
            </el-form-item>

            <el-form-item label="起止位置：" prop="trafficAccidentWarning.blockageRange">
              <el-input v-model="editForm.trafficAccidentWarning.blockageRange" :disabled="!isEditing"/>
            </el-form-item>

            <el-form-item label="中断原因：" prop="trafficAccidentWarning.blockageCause">
              <el-select
                v-model="editForm.trafficAccidentWarning.blockageCause"
                style="width:100%"
                :disabled="!isEditing"
              >
                <el-option
                  v-for="item in blockageCauseDict"
                  :key="item.dictValue"
                  :label="item.dictLabel"
                  :value="item.dictValue"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="预警级别：" prop="earlyWarningInfo.warnLevel">
              <el-select
                v-model="editForm.earlyWarningInfo.warnLevel"
                style="width:100%"
                :disabled="!isEditing"
              >
                <el-option
                  v-for="item in warnLevelList"
                  :key="item.dictValue"
                  :label="item.dictLabel"
                  :value="item.dictValue"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="禁航需求：">
              <el-radio-group 
                v-model="editForm.earlyWarningInfo.navigationClosure" 
                :disabled="!isEditing"
              >
                <el-radio :label="1">是</el-radio>
                <el-radio :label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="基础设施损失（万元）：" prop="trafficAccidentWarning.directLoss">
              <el-input-number 
                v-model="editForm.trafficAccidentWarning.directLoss"
                :min="0"
                controls-position="right"
                style="width:100%"
                :disabled="!isEditing"
              />
            </el-form-item>

            <el-form-item label="预计恢复时间（小时）：" prop="trafficAccidentWarning.recoveryTime">
              <el-input 
                v-model="editForm.trafficAccidentWarning.recoveryTime"
                :min="1" 
                controls-position="right"
                style="width:100%"
                :disabled="!isEditing"
              />
            </el-form-item>

            <el-form-item label="滞留车辆数：" prop="trafficAccidentWarning.strandedVehicles">
              <el-input-number 
                v-model="editForm.trafficAccidentWarning.strandedVehicles"
                :min="0"
                controls-position="right"
                style="width:100%"
                :disabled="!isEditing"
              />
            </el-form-item>

            <el-form-item label="排队长度（公里）：" prop="trafficAccidentWarning.queueLength">
              <el-input-number
                v-model="editForm.trafficAccidentWarning.queueLength"
                :min="0"
                :precision="1"
                controls-position="right"
                style="width:100%"
                :disabled="!isEditing"
              />
            </el-form-item>
            <el-form-item label="应急措施：" prop="trafficAccidentWarning.emergencyMeasures">
              <el-input
                v-model="editForm.trafficAccidentWarning.emergencyMeasures"
                type="textarea"
                :rows="3"
                :disabled="!isEditing"
              />
            </el-form-item>

            <el-form-item label="预警状态：" prop="earlyWarningInfo.status">
              <el-radio-group 
                v-model="editForm.earlyWarningInfo.status" 
                :disabled="!isEditing"
              >
                <el-radio :label="1">已处理</el-radio>
                <el-radio :label="0">未处理</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <template #footer>
        <el-button v-if="isEditing" type="primary" @click="handleSubmit" class="save-btn">
          <i class="el-icon-check" /> 保存修改
        </el-button>
        <el-button @click="detailVisible = false">关闭</el-button>
      </template>
    </el-dialog>

    <!-- 新增弹窗 -->
    <el-dialog
      v-model="addDialogVisible"
      width="80%"
      top="5vh"
      destroy-on-close
      class="add-dialog"
      :show-header="false"
    >
      <AddComponent @success="handleAddSuccess" />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, defineAsyncComponent } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { listData } from "@/api/system/dict/data";
import { DictDataQuery } from "@/api/system/dict/data/types";
import { getTrafficAccidentWarningList,
    getTrafficAccidentWarningById,updateTrafficAccidentWarning,
deleteTrafficAccidentWarning } from "@/api/bridge/traffic/traffic";
import dayjs from "dayjs";
import { useRouter } from 'vue-router';

const queryParams = ref<DictDataQuery>({
  dictType: "",
  pageNum: 1,
  pageSize: 10,
  dictName: "",
  dictLabel: "",
});

const tableData = ref([]);
const getList = async () => {
  try {
    queryParams.value.dictType = "";
    const params = {
      ...queryParams.value,
      status: filterType.value === '全部' ? null : 
             filterType.value === '已处理' ? 1 : 0,
      keyword: searchValue.value
    };
    const res = await getTrafficAccidentWarningList(params);
    tableData.value = res.rows;
    total.value = res.total;
  } catch (error) {
    console.error('获取数据失败:', error);
  }
};

// 分类选择
const filterType = ref("全部");
// 搜索值
const searchValue = ref("");
// 当前页码
const currentPage = ref(1);
// 每页数量
const pageSize = ref(10);
// 总数量
const total = ref(0);

const detailVisible = ref(false);
const currentDetail = ref<any>({});

// 在script部分新增表单数据和校验规则
const editForm = ref({
  earlyWarningInfo: {
    title: currentDetail.value.earlyWarningInfo?.title,
    warnLevel: currentDetail.value.earlyWarningInfo?.warnLevel,
    navigationClosure: currentDetail.value.earlyWarningInfo?.navigationClosure,
    status: currentDetail.value.earlyWarningInfo?.status
  },
  trafficAccidentWarning: {
    accidentType: '',
    location: '',
    occurrenceTime: '',
    bridgeDamage: '',
    blockageRange: '',
    blockageCause: '',
    directLoss: null,
    recoveryTime: '',
    strandedVehicles: null,
    queueLength: null,
    emergencyMeasures: '',
  }
});

const editRules = ref<any>({
  'trafficAccidentWarning.accidentType': [{ required: true, message: '请选择事故类型', trigger: 'change' }],
  'trafficAccidentWarning.location': [{ required: true, message: '请输入发生地点', trigger: 'blur' }],
  'trafficAccidentWarning.bridgeDamage': [{ required: true, message: '请输入大桥损毁描述', trigger: 'blur' }],
  'trafficAccidentWarning.blockageRange': [{ required: true, message: '请输入起止位置', trigger: 'blur' }],
  'trafficAccidentWarning.blockageCause': [{ required: true, message: '请选择中断原因', trigger: 'change' }],
  'trafficAccidentWarning.directLoss': [{ required: true,  message: '请输入基础设施损失', trigger: 'blur' }],
  'trafficAccidentWarning.recoveryTime': [{ required: true,  message: '请输入预计恢复时间', trigger: 'blur' }],
  'trafficAccidentWarning.strandedVehicles': [{ required: true, message: '请输入滞留车辆数', trigger: 'blur' }],
  'trafficAccidentWarning.queueLength': [{ required: true,  message: '请输入排队长度', trigger: 'blur' }],
  'trafficAccidentWarning.emergencyMeasures': [{ required: true, message: '请输入应急措施', trigger: 'blur' }],
  'earlyWarningInfo.warnLevel': [{ required: true, message: '请选择预警级别', trigger: 'change' }],
  'earlyWarningInfo.navigationClosure': [{ required: true, message: '请选择禁航需求', trigger: 'change' }],
  'earlyWarningInfo.status': [{ required: true, message: '请选择预警状态', trigger: 'change' }]
});

// 查看详情方法
const viewDetails = async (row: any) => {
    isEditing.value = false;
  try {
    const res = await getTrafficAccidentWarningById(row.trafficAccidentWarning.id);
    currentDetail.value = res.data; // 确保正确赋值
    editForm.value = {
      earlyWarningInfo: res.data.earlyWarningInfo,
      trafficAccidentWarning: res.data.trafficAccidentWarning
    };
    detailVisible.value = true;
  } catch (error) {
    ElMessage.error('获取详情失败');
  }
};

// 处理事件方法
const handleEvent = (row: any) => {
  console.log("处理事件", row);
};

// 查看结果方法
const viewResult = (row: any) => {
  console.log("查看结果", row);
};

// 修改分页处理方法
const handlePageChange = (val: number) => {
  currentPage.value = val;
  queryParams.value.pageNum = val; // 同步到查询参数
  getList();
};

const handleSizeChange = (val: number) => {
  pageSize.value = val;
  queryParams.value.pageSize = val; // 同步到查询参数
  getList();
};

// 添加搜索和筛选的监听
watch([filterType, searchValue], () => {
  currentPage.value = 1; // 重置为第一页
  queryParams.value.pageNum = 1;
  getList();
});

// 删除处理
const handleDelete = (row: any) => {
  ElMessageBox.confirm("确定要删除该条预警吗？", "警告", {
    type: "warning",
  })
    .then(async () => {
      await deleteTrafficAccidentWarning(row.trafficAccidentWarning.id);
      ElMessage.success("删除成功");
      getList();
    })
    .catch(() => {});
};

// 添加新增处理方法
const router = useRouter();

const AddComponent = defineAsyncComponent(() => import('@/views/Bridge/traffic-accident/accident-effect.vue'));
const addDialogVisible = ref(false);

const handleAdd = () => {
  addDialogVisible.value = true;
};

const handleAddSuccess = () => {
  addDialogVisible.value = false;
  getList();
  ElMessage.success('新增成功');
};

const accidentTypeDict = ref<any[]>([]); // 事故类型字典
const blockageCauseDict = ref<any[]>([]); // 发生原因字典
const warnLevelList = ref<any[]>([]);

// 获取事故类型字典
const getAccidentTypeDict = async () => {
  queryParams.value.dictType = "accident_type";
  const res = await listData(queryParams.value);
  accidentTypeDict.value = res.rows;
};

// 获取发生原因字典
const getBlockageCauseDict = async () => {
  queryParams.value.dictType = "blockage_range";
  const res = await listData(queryParams.value);
  blockageCauseDict.value = res.rows;
};

// 获取预警级别字典
const getWarnLevelDict = async () => {
  queryParams.value.dictType = "warn_level";
  const res = await listData(queryParams.value);
  warnLevelList.value = res.rows;
};

// 添加字典转换方法
const getDictLabel = (dictType: string, value: string) => {
  const dict =
    dictType === "accident_type" ? accidentTypeDict.value : blockageCauseDict.value;
  const item = dict.find((d: any) => d.dictValue === value);
  return item?.dictLabel || "N/A";
};

// 添加预警图片获取方法
const getAlertImage = (level: string) => {
  return new URL(`/src/assets/svg/alert${level}.svg`, import.meta.url).href;
};

// 新增提交方法
const handleSubmit = async () => {
  try {
    const submitData = {
      earlyWarningInfo: {
        ...editForm.value.earlyWarningInfo,
        navigationClosure: editForm.value.earlyWarningInfo.navigationClosure,
        status: editForm.value.earlyWarningInfo.status
      },
      trafficAccidentWarning: editForm.value.trafficAccidentWarning
    };
    
    await updateTrafficAccidentWarning(submitData);
    ElMessage.success('更新成功');
    getList();
    detailVisible.value = false;
  } catch (error) {
    ElMessage.error('更新失败');
  }
};

// 添加字典查询方法调用
getAccidentTypeDict();
getBlockageCauseDict();
getWarnLevelDict();
getList();

// 在script部分添加状态控制
const isEditing = ref(false);
const originalData = ref({});

const enableEditing = () => {
  isEditing.value = true;
  originalData.value = JSON.parse(JSON.stringify(editForm.value));
};

const cancelEditing = () => {
  isEditing.value = false;
  editForm.value = JSON.parse(JSON.stringify(originalData.value));
};
</script>

<style scoped lang="scss">
.list-page {
  padding: 20px;
  background: var(--el-bg-color-page);
  border-radius: 4px;
  box-shadow: var(--el-box-shadow-light);
}

.filter-area {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
  background: var(--el-bg-color-overlay);
  padding: 16px;
  border-radius: 4px;

  .search-input {
    width: 300px;
    margin-left: auto;
  }
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.el-table {
  margin-top: 16px;
  background: var(--el-bg-color-overlay);
  
  :deep(th) {
    background: var(--el-bg-color-page) !important;
  }

  :deep(.warning-row) {
    --el-table-tr-bg-color: var(--el-color-warning-light-9);
  }
  :deep(.success-row) {
    --el-table-tr-bg-color: var(--el-color-success-light-9);
  }
}

.detail-container {
  display: flex;
  gap: 30px;
  padding: 20px 0;
  
  .detail-column {
    flex: 1;
    min-width: 300px;
    
    .detail-title {
      color: #409eff;
      font-size: 16px;
      margin-bottom: 15px;
      padding-bottom: 8px;
      border-bottom: 1px solid #ebeef5;
    }
    
    .detail-row {
      margin-bottom: 12px;
      display: flex;
      line-height: 1.6;
      
      .detail-label {
        width: 90px;
        color: #909399;
        flex-shrink: 0;
      }
      
      .detail-value {
        flex: 1;
        color: #606266;
        word-break: break-word;
      }
    }
  }

  .unit {
    margin-left: 8px;
    color: #909399;
    font-size: 12px;
  }
  
  .el-input-number {
    width: 100%;
    
    :deep(.el-input__inner) {
      text-align: left;
    }
  }
  
  .el-date-editor {
    width: 100%;
  }
  
  .el-radio-group {
    margin-top: 8px;
  }
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background: var(--el-bg-color-page);
  border-radius: 8px 8px 0 0;
  border-bottom: 1px solid var(--el-border-color-light);
  
  .dialog-title {
    font-size: 18px;
    color: #303133;
    font-weight: 600;
  }

  .action-buttons {
    display: flex;
    gap: 12px;
  }
}

.edit-btn {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  padding: 10px 20px;
  border-radius: 6px;
  
  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}

.cancel-btn {
  transition: all 0.3s;
  padding: 10px 20px;
  border-radius: 6px;
  
  &:hover {
    background-color: #f4f4f5;
    color: #909399;
  }
}

.save-btn {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  padding: 10px 24px;
  border-radius: 6px;
  
  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
  }
}

.disabled-field {
  :deep(.el-input__inner) {
    background-color: #f8f9fa;
    border-color: #e4e7ed;
    color: #909399;
    cursor: not-allowed;
  }
}

.el-dialog {
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  
  &__body {
    padding: 24px;
    background: var(--el-bg-color-overlay);
  }
}

.el-form-item {
  margin-bottom: 24px;
  margin-left: -20px;
  :deep(.el-form-item__label) {
    color: #606266;
    font-weight: 500;
  }
}

.unit {
  margin-left: 10px;
  color: #909399;
  font-size: 14px;
  font-style: italic;
}

.add-dialog {
  :deep(.el-dialog) {
    background: transparent;
    box-shadow: none;
    border: none;
    margin-top: 0 !important;
  }

  :deep(.el-dialog__header) {
    display: none;
  }

  :deep(.el-dialog__body) {
    padding: 0;
    background: transparent;
  }

  :deep(.el-dialog__footer) {
    position: absolute;
    right: 20px;
    top: 20px;
    padding: 0;
    background: transparent;
  }

  :deep(.el-dialog__close) {
    color: #fff;
    font-size: 24px;
    z-index: 1000;
    text-shadow: 0 2px 4px rgba(0,0,0,0.2);
    
    &:hover {
      color: var(--el-color-primary);
    }
  }
}
</style>
