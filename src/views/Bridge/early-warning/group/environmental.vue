<template>
  <div class="list-page">
    <!-- 分类选择和搜索区域 -->
    <div class="filter-area">
      <el-radio-group v-model="filterType" class="filter-type" style="width: 30%;">
        <el-radio label="全部">全部</el-radio>
        <el-radio label="待处理">未处理</el-radio>
        <el-radio label="已处理">已处理</el-radio>
      </el-radio-group>
      <div style="width: 70%; display: flex; justify-content: flex-end; gap: 10px;">
        <el-button type="primary" class="add-button" @click="handleAdd">新增</el-button>
      </div>
    </div>
    <!-- 列表区域 -->
    <el-table :data="tableData" border style="width: 100%">
      <el-table-column type="selection" width="55"></el-table-column>
      <el-table-column label="序号" prop="index" width="80" align="center">
        <template #default="scope">
          {{ scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column
        label="危险品类型"
        prop="environmentalPollutionWarning.hazardType"
        min-width="180"
      >
        <template #default="scope">
          {{ getDictLabel("danger_type", scope.row.environmentalPollutionWarning?.hazardType) }}
        </template>
      </el-table-column>
      <el-table-column
        label="泄漏原因"
        prop="environmentalPollutionWarning.leakCause"
        width="200"
        align="center"
      ></el-table-column>
      <el-table-column
        label="发生时间"
        prop="environmentalPollutionWarning.occurrenceTime"
        width="200"
        align="center"
      >
        <template #default="scope">
          {{ dayjs(scope.row.environmentalPollutionWarning?.occurrenceTime).format('YYYY-MM-DD HH:mm') }}
        </template>
      </el-table-column>
      <el-table-column
        label="扩散形式"
        prop="environmentalPollutionWarning.diffusionForm"
        width="180"
        align="center"
      >
        <template #default="scope">
          {{ getDictLabel("kuosan_form", scope.row.environmentalPollutionWarning?.diffusionForm) }}
        </template>
      </el-table-column>
      <el-table-column
        label="影响半径"
        prop="environmentalPollutionWarning.affectedRadius"
        width="120"
        align="center"
      >
        <template #default="scope">
          {{ scope.row.environmentalPollutionWarning?.affectedRadius + '公里' }}
        </template>
      </el-table-column>
      <el-table-column
        label="影响人口"
        prop="environmentalPollutionWarning.populationAffected"
        width="120"
        align="center"
      ></el-table-column>
      <el-table-column
        label="预计恢复时间"
        prop="environmentalPollutionWarning.recoveryTime"
        width="180"
        align="center"
      >
        <template #default="scope">
          {{ scope.row.environmentalPollutionWarning?.recoveryTime + '小时' }}
        </template>
      </el-table-column>
      <el-table-column
        label="预警级别"
        prop="earlyWarningInfo.warnLevel"
        width="200"
        align="center"
      >
        <template #default="scope">
          <el-tooltip
            :content="`${scope.row.earlyWarningInfo?.warnLevel}级预警`"
            placement="top"
          >
            <img
              :src="getAlertImage(scope.row.earlyWarningInfo?.warnLevel)"
              style="width: 32px; height: 32px"
            />
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column
        label="处理状态"
        prop="earlyWarningInfo.status"
        width="120"
        align="center"
      >
        <template #default="scope">
          <el-tag
            :type="scope.row.earlyWarningInfo?.status ? 'success' : 'warning'"
          >
            {{ scope.row.earlyWarningInfo?.status ? "已处理" : "未处理" }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="180" fixed="right" align="center">
        <template #default="scope">
          <el-button size="small" type="primary" link @click="viewDetails(scope.row)">详情</el-button>
          <el-button 
            size="small" 
            type="danger" 
            link 
            @click="handleDelete(scope.row)"
            v-perms="['bridge:traffic:remove']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[5, 10, 15, 20]"
        :background="true"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @current-change="handlePageChange"
        @size-change="handleSizeChange"
      />
    </div>

    <!-- 详情弹窗 -->
    <el-dialog
      v-model="detailVisible"
      title="环境污染预警详情"
      width="800px"
      center
      destroy-on-close
    >
      <template #header>
        <div class="dialog-header">
          <span class="dialog-title">环境污染预警详情</span>
          <div class="action-buttons">
            <el-button 
              v-if="!isEditing"
              type="primary" 
              @click="enableEditing"
              class="edit-btn"
            >
              <i class="el-icon-edit" /> 编辑
            </el-button>
            <el-button 
              v-else
              type="info"
              @click="cancelEditing"
              class="cancel-btn"
            >
              <i class="el-icon-close" /> 取消
            </el-button>
          </div>
        </div>
      </template>
      <el-form :model="editForm" :rules="editRules" ref="editFormRef" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="危险品类型：" prop="environmentalPollutionWarning.hazardType">
              <el-select 
                v-model="editForm.environmentalPollutionWarning.hazardType"
                placeholder="请选择危险品类型"
                style="width:100%"
                :disabled="!isEditing"
              >
                <el-option
                  v-for="item in hazardTypeDict"
                  :key="item.dictValue"
                  :label="item.dictLabel"
                  :value="item.dictValue"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="发生地点：" prop="environmentalPollutionWarning.location">
              <el-input 
                v-model="editForm.environmentalPollutionWarning.location"
                :disabled="!isEditing"
              />
            </el-form-item>

            <el-form-item label="泄漏原因：" prop="environmentalPollutionWarning.leakCause">
              <el-input 
                v-model="editForm.environmentalPollutionWarning.leakCause"
                :disabled="!isEditing"
              />
            </el-form-item>

            <el-form-item label="扩散形式：" prop="environmentalPollutionWarning.diffusionForm">
              <el-select
                v-model="editForm.environmentalPollutionWarning.diffusionForm"
                style="width:100%"
                :disabled="!isEditing"
              >
                <el-option
                  v-for="item in diffusionFormDict"
                  :key="item.dictValue"
                  :label="item.dictLabel"
                  :value="item.dictValue"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="发生时间：" prop="environmentalPollutionWarning.occurrenceTime">
              <el-date-picker
                v-model="editForm.environmentalPollutionWarning.occurrenceTime"
                type="datetime"
                style="width: 100%"
                :disabled="!isEditing"
              />
            </el-form-item>
                        <el-form-item label="预警级别：" prop="earlyWarningInfo.warnLevel">
              <el-select
                v-model="editForm.earlyWarningInfo.warnLevel"
                style="width:100%"
                :disabled="!isEditing"
              >
                <el-option
                  v-for="item in warnLevelList"
                  :key="item.dictValue"
                  :label="item.dictLabel"
                  :value="item.dictValue"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="桥梁名称及桩号：" prop="environmentalPollutionWarning.bridgePosition">
              <el-input 
                v-model="editForm.environmentalPollutionWarning.bridgePosition"
                :disabled="!isEditing"
              />
            </el-form-item>

            <el-form-item label="影响半径（公里）：" prop="environmentalPollutionWarning.affectedRadius">
              <el-input-number
                v-model="editForm.environmentalPollutionWarning.affectedRadius"
                :min="0"
                controls-position="right"
                style="width:100%"
                :disabled="!isEditing"
              />
            </el-form-item>

            <el-form-item label="影响人口数量：" prop="environmentalPollutionWarning.populationAffected">
              <el-input-number
                v-model="editForm.environmentalPollutionWarning.populationAffected"
                :min="0"
                controls-position="right"
                style="width:100%"
                :disabled="!isEditing"
              />
            </el-form-item>

            <el-form-item label="经济损失（万元）：" prop="environmentalPollutionWarning.economicLoss">
              <el-input-number
                v-model="editForm.environmentalPollutionWarning.economicLoss"
                :min="0"
                controls-position="right"
                style="width:100%"
                :disabled="!isEditing"
              />
            </el-form-item>

            <el-form-item label="预计恢复时间（小时）：" prop="environmentalPollutionWarning.recoveryTime">
              <el-input-number
                v-model="editForm.environmentalPollutionWarning.recoveryTime"
                :min="1" 
                controls-position="right"
                style="width:100%"
                :disabled="!isEditing"
              />
            </el-form-item>

            <el-form-item label="禁航需求：" prop="earlyWarningInfo.navigationClosure">
              <el-radio-group 
                v-model="editForm.earlyWarningInfo.navigationClosure" 
                :disabled="!isEditing"
              >
                <el-radio :label="1">是</el-radio>
                <el-radio :label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item label="预警状态：" prop="earlyWarningInfo.status">
              <el-radio-group 
                v-model="editForm.earlyWarningInfo.status" 
                :disabled="!isEditing"
              >
                <el-radio :label="1">已处理</el-radio>
                <el-radio :label="0">未处理</el-radio>
              </el-radio-group>
            </el-form-item>

          </el-col>
        </el-row>
      </el-form>

      <template #footer>
        <el-button v-if="isEditing" type="primary" @click="handleSubmit" class="save-btn">
          <i class="el-icon-check" /> 保存修改
        </el-button>
        <el-button @click="detailVisible = false">关闭</el-button>
      </template>
    </el-dialog>

    <!-- 新增弹窗 -->
    <el-dialog
      v-model="addDialogVisible"
      width="80%"
      top="5vh"
      destroy-on-close
      class="add-dialog"
      :show-header="false"
    >
      <AddComponent @success="handleAddSuccess" />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, defineAsyncComponent } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { listData } from "@/api/system/dict/data";
import { DictDataQuery } from "@/api/system/dict/data/types";
import { getEnvironmentalPollutionWarningList, 
  getEnvironmentalPollutionWarningById, 
  updateEnvironmentalPollutionWarning, 
  deleteEnvironmentalPollutionWarning } from "@/api/bridge/traffic/environmental";
import dayjs from "dayjs";
import { useRouter } from 'vue-router';

const queryParams = ref<DictDataQuery>({
  dictType: "",
  pageNum: 1,
  pageSize: 10,
  dictName: "",
  dictLabel: "",
});

const tableData = ref([]);
const getList = async () => {
  try {
    const params = {
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      status: filterType.value === '全部' ? null : 
             filterType.value === '已处理' ? 1 : 0,
      keyword: searchValue.value
    };
    const res = await getEnvironmentalPollutionWarningList(params);
    tableData.value = res.rows;
    total.value = res.total;
  } catch (error) {
    console.error('获取数据失败:', error);
  }
};

// 分类选择
const filterType = ref("全部");
// 搜索值
const searchValue = ref("");
// 当前页码
const currentPage = ref(1);
// 每页数量
const pageSize = ref(10);
// 总数量
const total = ref(0);

const detailVisible = ref(false);
const currentDetail = ref<any>({});

// 在script部分新增表单数据和校验规则
const editForm = ref({
  earlyWarningInfo: {
    title: currentDetail.value.earlyWarningInfo?.title,
    warnLevel: currentDetail.value.earlyWarningInfo?.warnLevel,
    navigationClosure: currentDetail.value.earlyWarningInfo?.navigationClosure,
    status: currentDetail.value.earlyWarningInfo?.status
  },
  environmentalPollutionWarning: {
    hazardType: '',
    location: '',
    occurrenceTime: '',
    bridgeDamage: '',
    blockageRange: '',
    leakCause: '',
    directLoss: null,
    recoveryTime: '',
    strandedVehicles: null,
    queueLength: null,
    emergencyMeasures: '',
  }
});

const editRules = ref<any>({
  'environmentalPollutionWarning.hazardType': [{ required: true, message: '请选择危险品类型', trigger: 'change' }],
  'environmentalPollutionWarning.location': [{ required: true, message: '请输入发生地点', trigger: 'blur' }],
  'environmentalPollutionWarning.leakCause': [{ required: true, message: '请输入泄漏原因', trigger: 'blur' }],
  'environmentalPollutionWarning.diffusionForm': [{ required: true, message: '请选择扩散形式', trigger: 'change' }],
  'environmentalPollutionWarning.occurrenceTime': [{ required: true, message: '请选择发生时间', trigger: 'change' }],
  'environmentalPollutionWarning.bridgePosition': [{ required: true, message: '请输入桥梁位置', trigger: 'blur' }],
  'environmentalPollutionWarning.affectedRadius': [{ required: true, type: 'number', message: '请输入影响半径', trigger: 'blur' }],
  'environmentalPollutionWarning.populationAffected': [{ required: true, type: 'number', message: '请输入影响人口', trigger: 'blur' }],
  'environmentalPollutionWarning.economicLoss': [{ required: true, type: 'number', message: '请输入经济损失', trigger: 'blur' }],
  'environmentalPollutionWarning.recoveryTime': [{ required: true, type: 'number', message: '请输入恢复时间', trigger: 'blur' }],
  'earlyWarningInfo.navigationClosure': [{ required: true, message: '请选择禁航需求', trigger: 'change' }],
  'earlyWarningInfo.status': [{ required: true, message: '请选择预警状态', trigger: 'change' }],
  'earlyWarningInfo.warnLevel': [{ required: true, message: '请选择预警级别', trigger: 'change' }]
});

// 查看详情方法
const viewDetails = async (row: any) => {
  try {
    const res = await getEnvironmentalPollutionWarningById(row.environmentalPollutionWarning.id);
    currentDetail.value = res.data;
    editForm.value = {
      earlyWarningInfo: res.data.earlyWarningInfo,
      environmentalPollutionWarning: res.data.environmentalPollutionWarning
    };
    detailVisible.value = true;
  } catch (error) {
    ElMessage.error('获取详情失败');
  }
};

// 处理事件方法
const handleEvent = (row: any) => {
  console.log("处理事件", row);
};

// 查看结果方法
const viewResult = (row: any) => {
  console.log("查看结果", row);
};

// 修改分页处理方法
const handlePageChange = (val: number) => {
  currentPage.value = val;
  queryParams.value.pageNum = val; // 同步到查询参数
  getList();
};

const handleSizeChange = (val: number) => {
  pageSize.value = val;
  queryParams.value.pageSize = val; // 同步到查询参数
  getList();
};

// 添加搜索和筛选的监听
watch([filterType, searchValue], () => {
  currentPage.value = 1; // 重置为第一页
  queryParams.value.pageNum = 1;
  getList();
});

// 删除处理
const handleDelete = (row: any) => {
  isEditing.value = true;
  ElMessageBox.confirm("确定要删除该条预警吗？", "警告", {
    type: "warning",
  })
    .then(async () => {
      await deleteEnvironmentalPollutionWarning(row.environmentalPollutionWarning.id);
      ElMessage.success("删除成功");
      getList();
    })
    .catch(() => {});
};

// 添加新增处理方法
const router = useRouter();

const AddComponent = defineAsyncComponent(() => import('@/views/Bridge/traffic-accident/environmental.vue'));
const addDialogVisible = ref(false);

const handleAdd = () => {
  addDialogVisible.value = true;
};

const handleAddSuccess = () => {
  addDialogVisible.value = false;
  getList();
  ElMessage.success('新增成功');
};

const hazardTypeDict = ref<any[]>([]); // 危险品类型字典
const leakCauseDict = ref<any[]>([]); // 泄漏原因字典
const warnLevelList = ref<any[]>([]);

// 获取危险品类型字典
const getHazardTypeDict = async () => {
  queryParams.value.dictType = "danger_type";
  const res = await listData(queryParams.value);
  hazardTypeDict.value = res.rows;
};

// 获取泄漏原因字典
const getLeakCauseDict = async () => {
  queryParams.value.dictType = "leak_cause";
  const res = await listData(queryParams.value);
  leakCauseDict.value = res.rows;
};

// 获取预警级别字典
const getWarnLevelDict = async () => {
  queryParams.value.dictType = "warn_level";
  const res = await listData(queryParams.value);
  warnLevelList.value = res.rows;
};

// 修改getDictLabel方法
const getDictLabel = (dictType: string, value: string) => {
  let dict: any[] = [];
  switch(dictType) {
    case "danger_type":
      dict = hazardTypeDict.value;
      break;
    case "kuosan_form":  // 新增扩散形式字典支持
      dict = diffusionFormDict.value;
      break;
    case "warn_level":   // 添加预警级别字典支持
      dict = warnLevelList.value;
      break;
  }
  const item = dict.find((d: any) => d.dictValue === value);
  return item?.dictLabel || "N/A";
};

// 添加预警图片获取方法
const getAlertImage = (level: string) => {
  return new URL(`/src/assets/svg/alert${level}.svg`, import.meta.url).href;
};

// 新增提交方法
const handleSubmit = async () => {
  try {
    const submitData = {
      earlyWarningInfo: editForm.value.earlyWarningInfo,
      environmentalPollutionWarning: editForm.value.environmentalPollutionWarning
    };
    await updateEnvironmentalPollutionWarning(submitData);
    ElMessage.success('更新成功');
    getList();
    detailVisible.value = false;
  } catch (error) {
    ElMessage.error('更新失败');
  }
};

// 在script部分添加状态控制
const isEditing = ref(false);
const originalData = ref({});

const enableEditing = () => {
  isEditing.value = true;
  originalData.value = JSON.parse(JSON.stringify(editForm.value));
};

const cancelEditing = () => {
  isEditing.value = false;
  editForm.value = JSON.parse(JSON.stringify(originalData.value));
};

// 新增扩散形式字典
const diffusionFormDict = ref<any[]>([]);

const getDiffusionFormDict = async () => {
  queryParams.value.dictType = "kuosan_form";
  const res = await listData(queryParams.value);
  diffusionFormDict.value = res.rows;
};

// 在初始化时调用
onMounted(() => {
  getHazardTypeDict();
  getDiffusionFormDict();
  getWarnLevelDict();
  getList();
});

// 在script部分添加时间格式化方法
const formatTime = (time: string) => {
  return dayjs(time).format('YYYY-MM-DD HH:mm');
};
</script>

<style scoped lang="scss">
.list-page {
  padding: 20px;
  background: var(--el-bg-color-page);
  border-radius: 4px;
  box-shadow: var(--el-box-shadow-light);
  
  // .el-table {
  //   @include ruoyi-table;
  // }
  
  // .dialog-header {
  //   @include ruoyi-dialog-header;
  // }
}

.filter-area {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
  background: var(--el-bg-color-overlay);
  padding: 16px;
  border-radius: 4px;

  .search-input {
    width: 300px;
    margin-left: auto;
  }
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.el-table {
  margin-top: 16px;
  background: var(--el-bg-color-overlay);
  
  :deep(th) {
    background: var(--el-bg-color-page) !important;
  }

  :deep(.warning-row) {
    --el-table-tr-bg-color: var(--el-color-warning-light-9);
  }
  :deep(.success-row) {
    --el-table-tr-bg-color: var(--el-color-success-light-9);
  }

  :deep(.el-table__body) {
    tr:hover > td {
      background-color: var(--el-fill-color-light) !important;
    }
  }
  
  :deep(.el-table__row) {
    background-color: var(--el-bg-color-overlay);
  }
  
  :deep(.el-tag) {
    background-color: var(--el-fill-color);
    border-color: var(--el-border-color-light);
  }
}

.detail-container {
  display: flex;
  gap: 30px;
  padding: 20px 0;
  
  .detail-column {
    flex: 1;
    min-width: 300px;
    
    .detail-title {
      color: #409eff;
      font-size: 16px;
      margin-bottom: 15px;
      padding-bottom: 8px;
      border-bottom: 1px solid #ebeef5;
    }
    
    .detail-row {
      margin-bottom: 12px;
      display: flex;
      line-height: 1.6;
      
      .detail-label {
        width: 90px;
        color: #909399;
        flex-shrink: 0;
      }
      
      .detail-value {
        flex: 1;
        color: #606266;
        word-break: break-word;
      }
    }
  }

  .unit {
    margin-left: 8px;
    color: #909399;
    font-size: 12px;
  }
  
  .el-input-number {
    width: 100%;
    
    :deep(.el-input__inner) {
      text-align: left;
    }
  }
  
  .el-date-editor {
    width: 100%;
  }
  
  .el-radio-group {
    margin-top: 8px;
  }
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background: var(--el-bg-color-page);
  border-radius: 8px 8px 0 0;
  border-bottom: 1px solid var(--el-border-color-light);
  
  .dialog-title {
    font-size: 18px;
    color: #303133;
    font-weight: 600;
  }

  .action-buttons {
    display: flex;
    gap: 12px;
  }
}

.edit-btn {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  padding: 10px 20px;
  border-radius: 6px;
  
  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}

.cancel-btn {
  transition: all 0.3s;
  padding: 10px 20px;
  border-radius: 6px;
  
  &:hover {
    background-color: #f4f4f5;
    color: #909399;
  }
}

.save-btn {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  padding: 10px 24px;
  border-radius: 6px;
  
  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
  }
}

.disabled-field {
  :deep(.el-input__inner) {
    background-color: #f8f9fa;
    border-color: #e4e7ed;
    color: #909399;
    cursor: not-allowed;
  }
}

.el-dialog {
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  
  &__body {
    padding: 24px;
    background: var(--el-bg-color-overlay);
  }
}

.el-form-item {
  margin-bottom: 24px;
  
  :deep(.el-form-item__label) {
    color: #606266;
    font-weight: 500;
  }
}

.unit {
  margin-left: 10px;
  color: #909399;
  font-size: 14px;
  font-style: italic;
}
</style>
