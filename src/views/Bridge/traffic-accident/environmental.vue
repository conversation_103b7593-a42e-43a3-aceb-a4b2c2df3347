<template>
  <div class="card-wrapper">
    <el-main class="form-card container-card">
      <!-- 优化标题样式 -->
      <div class="card-header">
        <span class="header-title">新增环境污染预警</span>
        <div class="header-line"></div>
      </div>

      <el-form 
        label-width="180px" 
        :model="form"
        :rules="rules" 
        ref="formRef"
        class="form-container"
      >
        <el-row :gutter="40">
          <el-col :xs="24" :sm="12" :md="10" :lg="11">
            <div class="left-form">
              <!-- 统一表单项样式 -->
              <el-form-item label="预警名称：" class="form-item" prop="earlyWarningInfo.title">
                <el-input v-model="form.earlyWarningInfo.title" placeholder="请输入预警名称" disabled />
              </el-form-item>

              <!-- 调整单选组样式 -->
              <el-form-item label="发生地点：" class="form-item" prop="location">
                <el-input 
                  v-model="form.location" 
                  placeholder="请输入地理坐标或道路桩号" 
                />
              </el-form-item>

              <!-- 统一选择器样式 -->
              <el-form-item label="预警级别：" class="form-item" prop="earlyWarningInfo.warnLevel">
                <el-select
                  v-model="form.earlyWarningInfo.warnLevel"
                  placeholder="请选择预警级别"
                  style="width: 100%"
                >
                  <el-option 
                    v-for="item in warnLevelList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                    :class="`option-level-${item.value}`"
                  />
                </el-select>
              </el-form-item>

              <el-form-item label="危险品类型：" class="form-item" prop="hazardType">
                <el-select
                  v-model="form.hazardType"
                  placeholder="请选择危险品类型"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in hazardTypeList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>

              <el-form-item label="泄漏原因：" class="form-item" prop="leakCause">
                <el-input 
                  v-model="form.leakCause" 
                  placeholder="请输入泄漏原因" 
                />
              </el-form-item>

              <el-form-item label="扩散形式：" class="form-item" prop="diffusionForm">
                <el-select
                  v-model="form.diffusionForm"
                  placeholder="请选择扩散形式"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in diffusionFormList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>

              <el-form-item label="发生时间：" class="form-item" prop="occurrenceTime">
                <el-date-picker
                  v-model="form.occurrenceTime"
                  type="datetime"
                  placeholder="选择日期和时间"
                  style="width: 100%"
                />
              </el-form-item>

              

              <el-form-item label="影响半径（公里）：" class="form-item" prop="affectedRadius">
                <el-input-number
                  v-model="form.affectedRadius"
                  :min="0"
                  :precision="1"
                  controls-position="right"
                  style="width: 100%"
                />
              </el-form-item>
            </div>
          </el-col>

          <el-col :xs="24" :sm="12" :md="14" :lg="12">
            <div class="right-section">
              <div class="right-form" >
                <el-form-item label="桥梁名称及桩号：" class="form-item" prop="bridgePosition">
                <el-input 
                  v-model="form.bridgePosition" 
                  placeholder="例：K12+300至K14+500" 
                />
              </el-form-item>
                <el-form-item label="影响人口数量：" class="form-item" prop="populationAffected">
                <el-input-number
                  v-model="form.populationAffected"
                  :min="0"
                  controls-position="right"
                  style="width: 100%"
                />
              </el-form-item>

              <el-form-item label="经济损失（万元）：" class="form-item" prop="economicLoss">
                <el-input-number
                  v-model="form.economicLoss"
                  :min="0"
                  controls-position="right"
                  style="width: 100%"
                />
              </el-form-item>
                <el-form-item label="预计恢复时间（小时）：" class="form-item" prop="recoveryTime">
                  <el-input-number 
                    v-model="form.recoveryTime"
                    :min="1" 
                    controls-position="right" 
                    style="width: 100%"
                  />
                </el-form-item>

                <!-- 新增禁航需求和预警状态单选 -->
                <el-form-item label="禁航需求：" class="form-item" prop="earlyWarningInfo.navigationClosure">
                  <el-radio-group v-model="form.earlyWarningInfo.navigationClosure">
                    <el-radio :label="1">是</el-radio>
                    <el-radio :label="0">否</el-radio>
                  </el-radio-group>
                </el-form-item>

                <el-form-item label="预警状态：" class="form-item" prop="earlyWarningInfo.status">
                  <el-radio-group v-model="form.earlyWarningInfo.status">
                    <el-radio :label="1">已处理</el-radio>
                    <el-radio :label="0">未处理</el-radio>
                  </el-radio-group>
                </el-form-item>

                <!-- 添加操作按钮 -->
                <el-form-item>
                <div style="width: 100%;">
                  <div class="form-actions">
                    <el-button @click="resetForm" class="cancel-btn">重置表单</el-button>
                    <el-button type="primary" @click="submitForm" class="submit-btn">提交预警</el-button>
                  </div>
                </div>
                </el-form-item>
              </div>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </el-main>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { listData } from "@/api/system/dict/data";
import { DictDataQuery } from "@/api/system/dict/data/types";
import { addEnvironmentalPollutionWarning } from "@/api/bridge/traffic/environmental";
import dayjs from 'dayjs';
import router from "@/router";

const queryParams = ref<DictDataQuery>({
  dictType: "",
  pageNum: 1,
  pageSize: 1000,
  dictName: '',
  dictLabel: ''
});

const formRef = ref();

const warnLevelList = ref([]);

const hazardTypeList = ref([]);
function getWarnLevelList() {
  queryParams.value.dictType = "warn_level";
  listData(queryParams.value).then((res) => {
    warnLevelList.value = res.rows.map(item => ({
      label: item.dictLabel,
      value: item.dictValue
    }));
  });
}

function getHazardTypeList() {
  queryParams.value.dictType = "danger_type";
  listData(queryParams.value).then((res) => {
    hazardTypeList.value = res.rows.map(item => ({
      label: item.dictLabel,
      value: item.dictValue
    }));
  });
}

const diffusionFormList = ref([]);
function getDiffusionFormList() {
  queryParams.value.dictType = "kuosan_form";
  listData(queryParams.value).then((res) => {
    diffusionFormList.value = res.rows.map(item => ({
      label: item.dictLabel,
      value: item.dictValue
    }));
  });
}

// 表单数据
const form = ref({
  earlyWarningInfo: {
    title: "危险品泄漏预警",
    warnLevel: "",
    navigationClosure: 0,
    status: 0
  },
  location: "",
  hazardType: "",
  leakCause: "",
  diffusionForm: "",
  occurrenceTime:dayjs().format('YYYY-MM-DD HH:mm:ss'),
  bridgePosition: "",
  affectedRadius: null,
  populationAffected: null,
  economicLoss: null,
  recoveryTime: null
});

// 在script部分更新校验规则
const rules = ref({
  'earlyWarningInfo.navigationClosure': [{ required: true, message: '请选择禁航需求', trigger: 'blur' }],
  'earlyWarningInfo.status': [{ required: true, message: '请选择预警状态', trigger: 'blur' }],
  location: [{ required: true, message: '发生地点不能为空', trigger: ['blur', 'change'] }],
  'earlyWarningInfo.warnLevel': [{ required: true, message: '请选择预警级别', trigger: ['blur', 'change'] }],
  hazardType: [{ required: true, message: '请选择危险品类型', trigger: ['blur', 'change'] }],
  diffusionForm: [{ required: true, message: '请选择扩散形式', trigger: ['blur', 'change'] }],
  occurrenceTime: [{ required: true, message: '请选择发生时间', trigger: ['blur', 'change'] }],
  bridgePosition: [{ required: true, message: '起止位置不能为空', trigger: ['blur', 'change'] }],
  affectedRadius: [{ required: true, message: '请输入影响半径', trigger: ['blur', 'change'] }],
  recoveryTime: [{ required: true, message: '请输入预计清理恢复时间', trigger: ['blur', 'change'] }],
  populationAffected: [{ required: true, message: '请输入影响人口数量', trigger: ['blur', 'change'] }],
  economicLoss: [{ required: true, message: '请输入经济损失', trigger: ['blur', 'change'] }]
});

// 更新提交表单函数，取消注释验证代码
const submitForm = async () => {
    await formRef.value.validate();
    const submitData = {
      earlyWarningInfo: {
        ...form.value.earlyWarningInfo,
        navigationClosure: form.value.earlyWarningInfo.navigationClosure,
        status: form.value.earlyWarningInfo.status
      },
      environmentalPollutionWarning: {
        ...form.value,
        bridgePosition: form.value.bridgePosition,
        hazardType: hazardTypeList.value.find(item => item.value === form.value.hazardType) 
          ? form.value.hazardType 
          : 'custom:' + form.value.hazardType,
        leakCause: form.value.leakCause,
        diffusionForm: diffusionFormList.value.find(item => item.value === form.value.diffusionForm)
          ? form.value.diffusionForm
          : 'custom:' + form.value.diffusionForm
      }
    };
    console.log("提交数据", submitData);
    // 实际提交逻辑
  const res = await addEnvironmentalPollutionWarning(submitData);
   if (res.code === 200) {
      ElMessage.success("提交成功");
        router.push("/early-warning/group/environmental");
    }
};

// 重置表单
const resetForm = () => {
  form.value = {
    earlyWarningInfo: {
      title: "危险品泄漏预警",
      warnLevel: "",
      navigationClosure: 0,
      status: 0
    },
    location: "",
    hazardType: "",
    leakCause: "",
    diffusionForm: "",
    occurrenceTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    bridgePosition: "",
    affectedRadius: null,
    populationAffected: null,
    economicLoss: null,
    recoveryTime: null
  };
};
getWarnLevelList();
getHazardTypeList();
getDiffusionFormList();
</script>

<style scoped lang="scss">
.card-wrapper {
  position: relative;
  height: 100%;
  background: var(--el-bg-color-page);
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    // box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    pointer-events: none;
  }
}

.form-card {
  height: calc(100vh - 120px);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  padding: 20px 30px;
  border-radius: 8px;
  background: var(--el-bg-color-overlay);
  // border: 1px solid var(--el-border-color-light);

  :deep(.el-card__body) {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: auto;
    padding: 16px 20px;
  }

  .card-header {
    margin-bottom: 25px;
    .header-title {
      font-size: 20px;
      color: var(--el-text-color-primary);
      font-weight: 600;
    }
    .header-line {
      height: 3px;
      background: linear-gradient(90deg, #409eff 30%, transparent 100%);
      margin-top: 12px;
    }
  }

  .form-container {
    flex: 1;
    min-height: 600px;
    padding: 0 15px;

    .el-col {
      height: 100%;
      display: flex;
      flex-direction: column;
    }

    .left-form {
      border-right: 1px dashed #e4e7ed;
      padding-right: 40px;
      height: 100%;
    }

    .right-section {
      padding-left: 40px;
      height: 100%;
      display: flex;
      flex-direction: column;

      .sub-title {
        font-size: 16px;
        margin-bottom: 25px;
        padding-left: 12px;
        border-left-width: 4px;
      }
    }
  }

  .form-item {
    margin-bottom: 28px;
    :deep(.el-form-item__label) {
      font-size: 14px;
      color: var(--el-text-color-primary);
    }

    .el-input, .el-select, .el-date-editor {
      :deep(.el-input__inner) {
        background-color: var(--el-input-bg-color);
        border-color: var(--el-input-border-color);
        color: var(--el-text-color-primary);
      }
    }
  }

  .el-input-number {
    :deep(.el-input-number__decrease),
    :deep(.el-input-number__increase) {
      background-color: var(--el-fill-color-light);
      border-color: var(--el-border-color-light);
    }
  }

  .el-radio-group {
    :deep(.el-radio__inner) {
      border-color: var(--el-border-color-light);
    }
  }

  .checkbox-group {
    width: 50%;
    display: flex;
    justify-content: space-between;
    gap: 20px;
    .el-checkbox {
      margin: 4px 0;
    }
  }

  .form-divider {
    margin: 28px 0;
    background-color: #e4e7ed;
  }

  .form-actions {
    margin-top: auto;
    padding-top: 20px;
    display: flex;
    gap: 20px;
    justify-content: flex-end;

    .el-button {
      padding: 12px 24px;
    }
  }
}

@media (max-width: 768px) {
  .form-card {
    height: auto;
    padding: 15px;
    
    .form-container {
      .left-form {
        border-right: none;
        padding-right: 0;
      }
      
      .right-section {
        padding-left: 0;
        margin-top: 20px;
      }
    }
    
    .form-actions {
      flex-direction: column;
      gap: 12px;
    }
  }
}

// 预警级别颜色标识
.option-level-1 {
  color: #f56c6c;
  font-weight: 500;
}
.option-level-2 {
  color: #e6a23c;
  font-weight: 500;
}
.option-level-3 {
  color: #409eff;
  font-weight: 500;
}
.option-level-4 {
  color: #67c23a;
  font-weight: 500;
}
</style>
