<template>
  <div class="card-wrapper">
    <el-main class="form-card container-card">
      <!-- 优化标题样式 -->
      <div class="card-header">
        <span class="header-title">新增交通事故预警</span>
        <div class="header-line"></div>
      </div>

      <el-form 
        label-width="180px" 
        :model="form"
        :rules="rules" 
        ref="formRef"
        class="form-container"
      >
        <el-row :gutter="40">
          <el-col :xs="24" :sm="12" :md="10" :lg="11">
            <div class="left-form">
              <!-- 统一表单项样式 -->
              <el-form-item label="预警名称：" class="form-item" prop="earlyWarningInfo.title">
                <el-input v-model="form.earlyWarningInfo.title" placeholder="请输入预警名称" disabled />
              </el-form-item>

              <!-- 调整单选组样式 -->
              <el-form-item label="发生地点：" class="form-item" prop="location">
                <el-input 
                  v-model="form.location" 
                  placeholder="请输入地理坐标或道路桩号" 
                />
              </el-form-item>

              <!-- 统一选择器样式 -->
              <el-form-item label="预警级别：" class="form-item" prop="earlyWarningInfo.warnLevel">
                <el-select
                  v-model="form.earlyWarningInfo.warnLevel"
                  placeholder="请选择预警级别"
                  style="width: 100%"
                >
                  <el-option 
                    v-for="item in warnLevelList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                    :class="`option-level-${item.value}`"
                  />
                </el-select>
              </el-form-item>

              <el-form-item label="事故类型：" class="form-item" prop="accidentType">
                <el-select
                  v-model="form.accidentType"
                  placeholder="请选择事故类型"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in typeList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>

              <el-form-item label="发生时间：" class="form-item" prop="occurrenceTime">
                <el-date-picker
                  v-model="form.occurrenceTime"
                  type="datetime"
                  placeholder="选择日期和时间"
                  style="width: 100%"
                  format="YYYY-MM-DD HH:mm:ss"
                  value-format="YYYY-MM-DD HH:mm:ss"
                />
              </el-form-item>

              <el-form-item label="大桥损毁描述：" class="form-item" prop="bridgeDamage">
                <el-input 
                  v-model="form.bridgeDamage" 
                  placeholder="请描述大桥受损情况" 
                />
              </el-form-item>

              <el-form-item label="交通中断原因：" class="form-item" prop="blockageCause">
                <el-select
                  v-model="form.blockageCause"
                  placeholder="请选择中断原因"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in blockageReasonList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>

              <el-form-item label="起止位置及桩号：" class="form-item" prop="blockageRange">
                <el-input 
                  v-model="form.blockageRange" 
                  placeholder="例：K12+300至K14+500" 
                />
              </el-form-item>

              <el-form-item label="禁航需求：" class="form-item" prop="earlyWarningInfo.navigationClosure">
                <el-radio-group v-model="form.earlyWarningInfo.navigationClosure">
                  <el-radio :label="1">是</el-radio>
                  <el-radio :label="0">否</el-radio>
                </el-radio-group>
              </el-form-item>
            </div>
          </el-col>

          <el-col :xs="24" :sm="12" :md="14" :lg="12">
            <div class="right-section">
              <div class="right-form" >
                <el-form-item label="预计恢复时间（小时）：" class="form-item" prop="recoveryTime">
                  <el-input-number 
                    v-model="form.recoveryTime"
                    :min="1" 
                    controls-position="right" 
                    style="width: 100%"
                  />
                </el-form-item>

                <el-form-item label="基础设施损失（万元）：" class="form-item">
                  <el-input-number
                    v-model="form.directLoss"
                    :min="0"
                    controls-position="right"
                    style="width: 100%"
                  />
                </el-form-item>

                <el-form-item label="滞留车辆数量：" class="form-item" prop="strandedVehicles">
                  <el-input-number
                    v-model="form.strandedVehicles"
                    :min="0"
                    controls-position="right"
                    style="width: 100%"
                  />
                </el-form-item>

                <el-form-item label="排队长度（公里）：" class="form-item" prop="queueLength">
                  <el-input-number
                    v-model="form.queueLength"
                    :min="0"
                    :precision="1"
                    controls-position="right"
                    style="width: 100%"
                  />
                </el-form-item>

                <el-form-item label="应急管理措施：" class="form-item" prop="emergencyMeasures">
                  <el-input
                    type="textarea"
                    v-model="form.emergencyMeasures"
                    :rows="4"
                    placeholder="请输入应急管理措施"
                  />
                </el-form-item>

                <el-form-item label="预警状态：" class="form-item" prop="earlyWarningInfo.status">
                  <el-radio-group v-model="form.earlyWarningInfo.status">
                    <el-radio :label="0">未处理</el-radio>
                    <el-radio :label="1">已处理</el-radio>
                  </el-radio-group>
                </el-form-item>

                <!-- <el-form-item label="绕行路线：" class="form-item">
                  <el-input
                    type="textarea"
                    v-model="form.detourRoute"
                    :rows="2"
                    placeholder="请输入建议绕行路线"
                  />
                </el-form-item> -->

                <!-- <el-form-item label="预计处理恢复时间（小时）：" class="form-item">
                  <el-input-number
                    v-model="form.estimatedRecoveryTime"
                    :min="1"
                    controls-position="right"
                    style="width: 100%"
                  />
                </el-form-item> -->
                <!-- 添加操作按钮 -->
                <el-form-item>
                <div style="width: 100%;">
                  <div class="form-actions">
                    <el-button @click="resetForm" class="cancel-btn">重置表单</el-button>
                    <el-button type="primary" @click="submitForm" class="submit-btn">提交预警</el-button>
                  </div>
                </div>
                </el-form-item>
              </div>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </el-main>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { listData } from "@/api/system/dict/data";
import { DictDataQuery } from "@/api/system/dict/data/types";
import { addTrafficAccidentWarning } from "@/api/bridge/traffic/traffic";
import dayjs from 'dayjs';
import router from "@/router";


const queryParams = ref<DictDataQuery>({
  dictType: "",
  pageNum: 1,
  pageSize: 1000,
  dictName: '',
  dictLabel: ''
});

const formRef = ref();

const warnLevelList = ref([]);

const typeList = ref([]);
function getWarnLevelList() {
  queryParams.value.dictType = "warn_level";
  listData(queryParams.value).then((res) => {
    warnLevelList.value = res.rows.map(item => ({
      label: item.dictLabel,
      value: item.dictValue
    }));
  });
}

function getTypeList() {
  queryParams.value.dictType = "accident_type";
  listData(queryParams.value).then((res) => {
    typeList.value = res.rows.map(item => ({
      label: item.dictLabel,
      value: item.dictValue
    }));
  });
}

const blockageReasonList = ref([]);
function getBlockageReasonList() {
  queryParams.value.dictType = "blockage_range";
  listData(queryParams.value).then((res) => {
    blockageReasonList.value = res.rows.map(item => ({
      label: item.dictLabel,
      value: item.dictValue
    }));
  });
}

// 表单数据
const form = ref({
  // earlyWarningInfo 部分
  earlyWarningInfo: {
    title: "交通事故预警",  // 原warningName改为title
    warnLevel: "" ,         // 原warningLevel改为warnLevel
    navigationClosure: 0,   // 新增禁航需求字段
    status: 0 // 新增预警状态字段
  },
  // trafficAccidentWarning 部分
  location: "",
  accidentType: "",
  occurrenceTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
  bridgeDamage: "",
  blockageCause: "",
  blockageRange: "",
  recoveryTime: null,
  directLoss: null,
  strandedVehicles: null,
  queueLength: null,
  emergencyMeasures: "",
  detourRoute: "",
  estimatedRecoveryTime: null,

});

// 在script部分更新校验规则
const rules = ref({
  'earlyWarningInfo.title': [{ required: true, message: '预警标题不能为空', trigger: 'blur' }],
  'earlyWarningInfo.warnLevel': [{ required: true, message: '请选择预警级别', trigger: 'blur' }],
  location: [{ required: true, message: '发生地点不能为空', trigger:'blur' }],
  accidentType: [{ required: true, message: '请选择事故类型', trigger:'blur' }],
  occurrenceTime: [{ required: true, message: '请选择发生时间', trigger: 'blur' }],
  bridgeDamage: [{ required: true, message: '大桥损毁描述不能为空', trigger: 'blur' }],
  blockageCause: [{ required: true, message: '请选择中断原因', trigger:'blur' }],
  blockageRange: [{ required: true, message: '起止位置不能为空', trigger: 'blur' }],
  recoveryTime: [{ required: true, message: '请输入预计恢复时间', trigger: 'blur' }],
  strandedVehicles: [{ required: true, message: '请输入滞留车辆数', trigger: 'blur' }],
  queueLength: [{ required: true, message: '请输入排队长度', trigger:'blur' }],
  emergencyMeasures: [{ required: true, message: '应急措施不能为空', trigger: 'blur' }],
  'earlyWarningInfo.navigationClosure': [{ required: true, message: '请选择禁航需求', trigger: 'blur' }],
  'earlyWarningInfo.status': [{ required: true, message: '请选择预警状态', trigger: 'blur' }]
});

// 更新提交表单函数，取消注释验证代码
const submitForm = async () => {
    await formRef.value.validate();
    const submitData = {
      earlyWarningInfo: {
        title: form.value.earlyWarningInfo.title,
        warnLevel: form.value.earlyWarningInfo.warnLevel,
        navigationClosure: form.value.earlyWarningInfo.navigationClosure,
        status: form.value.earlyWarningInfo.status
      },
      trafficAccidentWarning: {
        location: form.value.location,
        accidentType: typeList.value.find(item => item.value === form.value.accidentType) 
          ? form.value.accidentType 
          : 'custom:' + form.value.accidentType,
        occurrenceTime: form.value.occurrenceTime,
        bridgeDamage: form.value.bridgeDamage,
        blockageCause: blockageReasonList.value.find(item => item.value === form.value.blockageCause)
          ? form.value.blockageCause
          : 'custom:' + form.value.blockageCause,
        blockageRange: form.value.blockageRange,
        recoveryTime: form.value.recoveryTime,
        directLoss: form.value.directLoss,
        strandedVehicles: form.value.strandedVehicles,
        queueLength: form.value.queueLength,
        emergencyMeasures: form.value.emergencyMeasures,
      }
    };
    console.log("提交数据", submitData);
    const res = await addTrafficAccidentWarning(submitData);
    if (res.code === 200) {
      ElMessage.success("提交成功");
        router.push("/early-warning/group/accident-effct");
    }
};

// 重置表单
const resetForm = () => {
  form.value = {
    earlyWarningInfo: {
      title: "交通事故预警",
      warnLevel: "",
      navigationClosure: 0,
      status: 0
    },
    location: "",
    accidentType: "",
    occurrenceTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    bridgeDamage: "",
    blockageCause: "",
    blockageRange: "",
    recoveryTime: null,
    directLoss: null,
    strandedVehicles: null,
    queueLength: null,
    emergencyMeasures: "",
    detourRoute: "",
    estimatedRecoveryTime: null,
  };
};
getWarnLevelList();
getTypeList();
getBlockageReasonList();
</script>

<style scoped lang="scss">
.card-wrapper {
  position: relative;
  height: 100%;
  background: var(--el-bg-color-page);
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    // box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    pointer-events: none;
  }
}

.form-card {
  height: calc(100vh - 120px);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background: var(--el-bg-color-overlay);
  // border: 1px solid var(--el-border-color-light);
  padding: 20px 30px;
  border-radius: 8px;

  :deep(.el-card__body) {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: auto;
    padding: 16px 20px;
  }

  .card-header {
    margin-bottom: 25px;
    .header-title {
      font-size: 20px;
      color: var(--el-text-color-primary);
      font-weight: 600;
    }
    .header-line {
      height: 3px;
      background: linear-gradient(90deg, #409eff 30%, transparent 100%);
      margin-top: 12px;
    }
  }

  .form-container {
    flex: 1;
    min-height: 600px;
    padding: 0 15px;

    .el-col {
      height: 100%;
      display: flex;
      flex-direction: column;
    }

    .left-form {
      border-right: 1px dashed #e4e7ed;
      padding-right: 40px;
      height: 100%;
    }

    .right-section {
      padding-left: 40px;
      height: 100%;
      display: flex;
      flex-direction: column;

      .sub-title {
        font-size: 16px;
        margin-bottom: 25px;
        padding-left: 12px;
        border-left-width: 4px;
      }
    }
  }

  .form-item {
    margin-bottom: 28px;
    :deep(.el-form-item__label) {
      font-size: 14px;
      color: var(--el-text-color-primary);
    }

    .el-input, .el-select, .el-date-editor {
      :deep(.el-input__inner) {
        line-height: 1.5;
        background: var(--el-input-bg-color);
        border-color: var(--el-input-border-color);
        color: var(--el-text-color-primary);
      }
    }
  }

  .checkbox-group {
    width: 50%;
    display: flex;
    justify-content: space-between;
    gap: 20px;
    .el-checkbox {
      margin: 4px 0;
    }
  }

  .form-divider {
    margin: 28px 0;
    background-color: #e4e7ed;
  }

  .form-actions {
    margin-top: auto;
    padding-top: 20px;
    display: flex;
    gap: 20px;
    justify-content: flex-end;

    .el-button {
      padding: 12px 24px;
    }
  }
}

@media (max-width: 768px) {
  .form-card {
    height: auto;
    padding: 15px;
    
    .form-container {
      .left-form {
        border-right: none;
        padding-right: 0;
      }
      
      .right-section {
        padding-left: 0;
        margin-top: 20px;
      }
    }
    
    .form-actions {
      flex-direction: column;
      gap: 12px;
    }
  }
}

// 预警级别颜色标识
.option-level-1 {
  color: #f56c6c;
  font-weight: 500;
}
.option-level-2 {
  color: #e6a23c;
  font-weight: 500;
}
.option-level-3 {
  color: #409eff;
  font-weight: 500;
}
.option-level-4 {
  color: #67c23a;
  font-weight: 500;
}
</style>
