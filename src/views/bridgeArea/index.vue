<template>
  <div class="bridge-area">
    <div class="area-right">
      <div class="area-box1">
        <Box :title="'船舶通行流量'" class="Box1">
          <template #unit>
            <div class="unit">
              <!-- <el-select v-model="value1" placeholder="Select" size="large" style="width: 92px">
                <el-option v-for="item in options1" :key="item.value" :label="item.label" :value="item.value" />
              </el-select> -->
              <el-select v-model="value" placeholder="Select" @change="flowShipTime" size="large" style="width: 72px">
                <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </div>
          </template>
          <template v-slot:content>
            <div class="FlowEcharts" ref="FlowEchartsRef"></div>
          </template>
        </Box>
        <Box :title="'在桥船舶分析'" class="Box2">
          <template v-slot:content>
            <div class="line">
              <div class="box" v-for="item in customColors" :key="item.bridgeId">
                <span> {{ item.bridgeName }}</span>
                <el-progress :percentage="item.percentage" color="#1989fa" :show-text="false" />
                <span
                  ><i>{{ item.shipCount }}</i> 艘</span
                >
              </div>
              <noData v-if="customColors.length == 0"></noData>
            </div>
          </template>
        </Box>
        <Box :title="'警戒力量'" class="Box3" :onTitleClick="videoFc">
          <template #content>
            <div class="name">
              <span>警戒船名</span>
              <span>MMSI</span>
              <span>姓名</span>
              <span>手机号</span>
              <span>巡逻码头</span>
            </div>
            <div class="list">
              <div class="line" v-for="(item, index) in guardshipList" :key="index">
                <span class="line-span" :title="item.shipName">{{ item.shipName }}</span>
                <span class="line-span" :title="item.mmsi">{{ item.mmsi }}</span>
                <span class="line-span" :title="item.name">{{ item.name }}</span>
                <span class="line-span" :title="item.phone">{{ item.phone }}</span>
                <span class="line-span" :title="item.seaArea">{{ item.seaArea }}</span>
              </div>
              <NoData v-if="guardshipList?.length == 0"></NoData>
            </div>
          </template>
        </Box>
      </div>
      <div class="area-box2">
        <Box :title="'预警分析'" class="Box1">
          <template v-slot:content>
            <div class="content">
              <div class="head">
                <div class="head-lin">
                  <span class="img1"></span>
                  <span>全部</span>
                  <span>{{ incidentData.stats.total || 0 }}</span>
                </div>
                <div class="head-lin">
                  <span class="img4"></span>
                  <span>未处理</span>
                  <span>{{ incidentData.stats.status1Count || 0 }}</span>
                </div>
                <div class="head-lin">
                  <span class="img2"></span>
                  <span>处理中</span>
                  <span>{{ incidentData.stats.status2Count || 0 }}</span>
                </div>
                <div class="head-lin">
                  <span class="img3"></span>
                  <span>已处理</span>
                  <span>{{ incidentData.stats.status3Count || 0 }}</span>
                </div>
              </div>
              <div class="list">
                <div class="list-box" v-for="(item, index) in incidentData.list" :key="index">
                  <div class="img">
                    <img v-if="item.fileUrl[0]" :src="item.fileUrl[0]" alt="" />
                    <noData v-else text="暂无图片"></noData>
                  </div>
                  <div class="con">
                    <span>发生时间：{{ item.createTime }}</span>
                    <span>发生地点：{{ item.place }}</span>
                    <span>预警事件：{{ item.title }}</span>
                    <span
                      >状态：<i :class="warningState(item.status)">{{
                        item.status == '1' ? '未处理' : item.status == '3' ? '已处理' : item.status == '2' ? '处理中' : ''
                      }}</i></span
                    >
                  </div>
                  <div class="btn">
                    <div class="btn1"></div>
                    <div class="btn2" @click="addressClick(item)"></div>
                    <div class="btn3"></div>
                  </div>
                </div>
                <NoData v-if="incidentData.list?.length == 0"></NoData>
              </div>
            </div>
          </template>
        </Box>
        <!-- <Box :title="'处置记录'" class="Box2">
          <template v-slot:content>
            <div class="name">
              <span>发生地点</span>
              <span>发生时间</span>
              <span>事件</span>
              <span>处置状态</span>
            </div>
            <div class="list">
              <div class="line" v-for="(item, index) in analyzeData" :key="index">
                <span class="line-span" :title="item.eventLocation">{{ item.eventLocation }}</span>
                <span class="line-span" :title="item.create_time">{{ item.create_time }}</span>
                <span class="line-span" :title="item.eventTitle">{{ item.eventTitle }}</span>
                <span class="line-span"
                  ><i :class="FlowState(item.status)">{{ item.status == 1 ? '处置中' : '已完成' }}</i></span
                >
              </div>
            </div>
          </template>
        </Box> -->
      </div>
    </div>
    <!-- 执法船监控 -->
    <PopUp :visible="showModal1" title="执法船监控" :width="'100%'" @update:visible="showModal1 = $event" class="PopUp1">
      <div class="modal-content">
        <VideoList :monitor-list="monitorList" />
      </div>
    </PopUp>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue';
import * as echarts from 'echarts';
import Box from '@/components/Box/index.vue';
import txImg from '@/assets/forewarning/tx.png';
import { getWhitelist } from '@/api/bridge/dailyWork';
import { getWarningStatistics } from '@/api/bridge/perception';
import { getFlowList, getFlowShip, getWarningList, getBridgeShip } from '@/api/bridge/bridgeArea';
import PopUp from '@/components/PopUp/index.vue';
import VideoList from '@/components/VideoList/index.vue';
import { getUrlVideo } from '@/api/bridge/point';
import NoData from '@/components/noData/index.vue';
import { setViewToCoordinates } from '@/utils/mapMethods';

// 图表引用
const FlowEchartsRef = ref<HTMLElement | null>(null);

// 图表实例
let FlowEcharts: echarts.ECharts | null = null;
// 定时器实例
let bridgeShipTimer: number | null = null;

// 监听窗口大小变化，调整图表大小
const handleResize = () => {
  FlowEcharts?.resize();
};

const showModal1 = ref(false);
const monitorList: any = ref([]);
//执法船监控
const videoFc = () => {
  showModal1.value = true;
  fetchMonitorList();
};

// 获取监控列表
const fetchMonitorList = async () => {
  try {
    const res = await getUrlVideo();
    monitorList.value = res || [];
  } catch (error) {
    console.error('获取监控列表失败:', error);
  }
};

//船舶通行流量下拉框
const value1 = ref('全部');
const options1 = [
  {
    value: 'Option1',
    label: '全部'
  },
  {
    value: 'Option2',
    label: '2'
  },
  {
    value: 'Option3',
    label: '3'
  }
];
const flowShipTime = (val) => {
  initFlowEcharts({ time: val });
};
const value = ref('3');
const options = [
  {
    value: '1',
    label: '日'
  },
  {
    value: '2',
    label: '月'
  },
  {
    value: '3',
    label: '年'
  }
];

//船舶通行流量图表
const initFlowEcharts = async (data?: {}) => {
  const res = await getFlowShip(data);
  let xData = [];
  let inData = [];
  let outData = [];
  if (res.code == 200) {
    res.data?.forEach((item: any) => {
      xData.push(item.key);
      inData.push(item.value1);
      outData.push(item.value2);
    });
  }
  if (FlowEchartsRef.value) {
    FlowEcharts = echarts.init(FlowEchartsRef.value);
    const option = {
      xAxis: {
        type: 'category',
        data: xData ? xData : ['00:00', '03:00', '06:00', '09:00', '12:00', '15:00', '18:00', '21:00'],
        axisLabel: {
          color: '#86868d',
          fontSize: 20
        },
        axisLine: {
          lineStyle: {
            color: '#86868d'
          }
        },
        splitLine: {
          show: false
        }
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          color: '#86868d',
          fontSize: 20
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: '#86868d'
          }
        },
        splitLine: {
          show: false // 不显示 Y 轴的网格线
        }
      },
      series: [
        {
          name: '进',
          data: inData ? inData : [0, 0, 0, 0, 0, 0, 0, 0],
          type: 'line',
          smooth: true,
          lineStyle: {
            color: '#ffffff',
            width: 2
          },
          areaStyle: {
            color: 'rgba(255, 255, 255, 0.2)' // 设置面积颜色
          },
          itemStyle: {
            color: '#a6d8fc'
          }
        },
        {
          name: '出',
          data: outData ? outData : [0, 0, 0, 0, 0, 0, 0, 0],
          type: 'line',
          smooth: true,
          areaStyle: {
            color: 'rgba(133, 202, 252, 0.2)' // 设置面积颜色
          },
          lineStyle: {
            color: '#85cafc',
            width: 2
          },
          itemStyle: {
            color: '#35bfb0'
          }
        }
      ],
      legend: {
        right: 60,
        data: ['进', '出'],
        textStyle: {
          color: '#fff',
          fontSize: 20
        }
      },
      tooltip: {
        trigger: 'axis',
        backgroundColor: '#23334e', // 背景色
        textStyle: {
          color: '#fff' // 字体颜色
        }
      },
      grid: {
        top: 40,
        right: 0,
        bottom: 0,
        left: 0,
        containLabel: true,
        show: true,
        borderWidth: 0 // 不显示外边框
      }
    };
    FlowEcharts.setOption(option);
  }
};

//在桥船舶分析数据
const customColors = ref([]);
const getBridgeShipFc = async () => {
  const res = await getBridgeShip();
  if (res.code == 200) {
    let maxLength = res.data[0].shipCount; //根据最多的桥比
    customColors.value = res.data.map((item) => {
      return {
        percentage: ((item.shipCount / maxLength) * 100).toFixed(2),
        ...item
      };
    });
  }
};

//警戒力量数据
const guardshipList = ref([]);
const getWhitelistFc = async () => {
  const res = await getWhitelist({});
  if (res.code == 200) {
    guardshipList.value = res.data || [];
    // console.log(res);
  }
};

//预警分析数据
const incidentData = ref<any>({
  list: [],
  stats: {
    status1Count: 0,
    status2Count: 0,
    status3Count: 0,
    total: 0
  }
});

//预警地址点击
const addressClick = (item) => {
  if (item.lat && item.lon) {
    setViewToCoordinates([item.lon, item.lat]);
  }
};

//预警分析数据
const getWarningStatisticsFc = async () => {
  // const res = await getWarningStatistics({});
  const res = await getWarningList({});
  if (res.code == 200) {
    incidentData.value.list = res.data.list.map((item) => {
      return {
        ...item,
        fileUrl: JSON.parse(item.fileUrl)
      };
    });
    incidentData.value.stats = res.data.stats;
  }
};
//根据预警状态更改颜色
const warningState = (state) => {
  if (state == '1') {
    return 'current0';
  } else if (state == '3') {
    return 'current1';
  } else {
    return 'current2';
  }
};

//处置记录数据
const analyzeData = ref([]);
const getFlowListFc = async () => {
  const res = await getFlowList({});
  if (res.code == 200) {
    analyzeData.value = res.data || [];
  }
};
//用来判断处置状态
const FlowState = (item) => {
  if (item == 1) {
    return 'line5-dll';
  } else {
    return 'line5-lc';
  }
};

onMounted(() => {
  initFlowEcharts({ time: value.value });
  getWhitelistFc();
  getWarningStatisticsFc();
  window.addEventListener('resize', handleResize);

  // 启动定时器，每10分钟调用一次
  getBridgeShipFc(); // 立即执行一次
  bridgeShipTimer = window.setInterval(
    () => {
      getBridgeShipFc();
    },
    10 * 60 * 1000
  ); // 10分钟 = 10 * 60 * 1000毫秒
});

// 组件卸载前移除事件监听
onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize);
  // 销毁图表实例
  FlowEcharts?.dispose();
  FlowEcharts = null;
  // 清除定时器
  if (bridgeShipTimer) {
    clearInterval(bridgeShipTimer);
    bridgeShipTimer = null;
  }
});
</script>

<style scoped lang="scss">
.bridge-area {
  .area-right {
    height: 1377px;
    width: 1442px;
    position: absolute;
    bottom: 44px;
    right: 60px;
    display: flex;
    justify-content: space-between;
    z-index: 1;
    .area-box1 {
      height: 100%;
      width: 48%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      .Box1 {
        width: 100%;
        height: 554px;
        position: relative;
        .unit {
          margin-left: 20px;
          height: 42px;
          display: flex;
          align-items: center;
          .el-select {
            margin-right: 10px;
            background: #35373c;
            .el-select--large .el-select__wrapper {
              background: #35373c;
            }
            ::v-deep span {
              color: #fff;
              font-family: 'Microsoft YaHei';
              font-size: 17px;
              font-weight: bold;
            }
          }
        }
        .FlowEcharts {
          flex: 1;
          width: 100%;
        }
      }
      .Box2 {
        width: 100%;
        height: 330px;
        position: relative;
        .line {
          flex: 1;
          overflow-y: scroll;
          margin-top: 20px;
          padding-right: 20px;
          .box {
            height: 70px;
            width: 100%;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-around;
            align-items: center;
            background: #232531;
            border-radius: 4px;
            span:nth-child(1) {
              font-size: 21px;
              color: #9d9ea0;
              font-family: 'Microsoft YaHei';
            }
            span:last-child {
              font-size: 21px;
              font-family: 'Microsoft YaHei';
              color: #9d9ea0;
              i {
                font-style: normal;
                color: #fff;
                font-size: 25px;
                font-weight: bold;
              }
            }
            .el-progress {
              width: 335px;
              height: 12px;
            }
          }
        }
        /* 自定义滚动条宽度和高度 */
        ::-webkit-scrollbar {
          width: 0px;
          height: 0px;
        }
      }
      .Box3 {
        width: 100%;
        height: 425px;
        position: relative;
        .name {
          background: #262c3f;
          width: 100%;
          height: 62px;
          display: flex;
          color: #fff;
          margin-top: 20px;
          span {
            font-size: 19px;
            font-weight: bold;
            font-family: 'Microsoft YaHei';
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            width: 20%;
          }
        }
        .list {
          width: 100%;
          height: calc(100% - 123px);
          margin-top: 10px;
          overflow-y: scroll;
          .line {
            display: flex;
            width: 100%;
            height: 62px;

            .line-span {
              color: #fff;
              font-size: 17px;
              font-family: 'Microsoft YaHei';
              height: 100%;
              width: 20%;
              line-height: 62px;
              text-align: center;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }
        }
        /* 隐藏垂直滚动条 */
        .list::-webkit-scrollbar {
          width: 0;
        }
      }
    }
    .area-box2 {
      height: 100%;
      width: 48%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      .Box1 {
        width: 100%;
        height: 100%;
        position: relative;
        .content {
          flex: 1;
          overflow: hidden;
          .head {
            height: 118px;
            padding: 10px;
            width: 100%;
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            align-items: center;
            margin-top: 10px;
            background: #0e0f15;
            border-radius: 4px;
            .head-lin {
              color: #fff;
              display: flex;
              justify-content: space-around;
              align-items: center;
              width: 48%;
              background: #222730;
              border-radius: 4px;
              .img1 {
                width: 16px;
                height: 16px;
                background: url('@/assets/forewarning/qb.png') no-repeat center;
                background-size: 100% 100%;
              }
              .img2 {
                width: 16px;
                height: 16px;
                background: url('@/assets/forewarning/czz.png') no-repeat center;
                background-size: 100% 100%;
              }
              .img3 {
                width: 16px;
                height: 16px;
                background: url('@/assets/forewarning/ycl.png') no-repeat center;
                background-size: 100% 100%;
              }
              .img4 {
                width: 16px;
                height: 16px;
                background: url('@/assets/forewarning/wcl.png') no-repeat center;
                background-size: 100% 100%;
              }
              span:nth-child(1) {
                margin-left: 20px;
              }
              span:nth-child(2) {
                font-size: 22px;
                font-family: 'Microsoft YaHei';
                flex: 1;
                margin-left: 20px;
              }
              span:nth-child(3) {
                font-size: 17px;
                font-family: 'Microsoft YaHei';
                font-weight: bold;
                margin-right: 40px;
              }
            }
          }
          .list {
            overflow-y: scroll;
            width: 100%;
            height: calc(100% - 128px);
            .list-box {
              width: 100%;
              height: 150px;
              display: flex;
              align-items: center;
              background: #1a1e23;
              margin-bottom: 20px;
              .img {
                width: 211px;
                height: 125px;
                margin-left: 10px;
                img {
                  width: 100%;
                  height: 100%;
                }
              }
              .con {
                display: flex;
                height: 100%;
                flex: 1;
                flex-direction: column;
                justify-content: space-around;
                margin-left: 10px;
                span {
                  color: #fff;
                  display: inline-block;
                  font-size: 17px;
                  font-family: 'Microsoft YaHei';
                  i {
                    font-style: normal;
                    color: red;
                  }
                  .current0 {
                    color: red;
                  }
                  .current1 {
                    color: #00ff6d;
                  }
                  .current2 {
                    color: #efff00;
                  }
                }
              }
              .btn {
                width: 33px;
                height: 125px;
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                margin-right: 20px;
                > div {
                  width: 33px;
                  height: 33px;
                  border-radius: 4px;
                  cursor: pointer;
                }
                .btn1 {
                  background: #2a2b39 url('@/assets/forewarning/fxdj.png') no-repeat center;
                }
                .btn2 {
                  background: #2a2b39 url('@/assets/forewarning/dw.png') no-repeat center;
                }
                .btn3 {
                  background: #2a2b39 url('@/assets/forewarning/sxt.png') no-repeat center;
                }
              }
            }
          }
          /* 自定义滚动条宽度和高度 */
          ::-webkit-scrollbar {
            width: 0px;
            height: 0px;
          }
        }
      }
      .Box2 {
        width: 100%;
        height: 554px;
        position: relative;
        .name {
          background: #262c3f;
          width: 100%;
          height: 62px;
          display: flex;
          color: #fff;
          margin-top: 20px;
          span {
            font-size: 19px;
            font-weight: bold;
            font-family: 'Microsoft YaHei';
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            width: 25%;
          }
        }
        .list {
          width: 100%;
          height: calc(100% - 123px);
          margin-top: 10px;
          overflow-y: scroll;
          .line {
            display: flex;
            width: 100%;
            height: 62px;

            .line-span {
              color: #fff;
              font-size: 17px;
              font-family: 'Microsoft YaHei';
              height: 100%;
              width: 25%;
              line-height: 62px;
              text-align: center;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              min-width: 0;
              i {
                display: inline-block;
                height: 30px;
                width: 60px;
                font-style: normal;
                line-height: 30px;
                text-align: center;
              }
            }
          }
          .line:nth-child(2n) {
            background-color: #232531;
          }
          .line5-dll {
            background: #fe7c2b;
          }
          .line5-lc {
            background: #00ac28;
          }
          .line5-yd {
            background: #fe0020;
          }
        }
        /* 隐藏垂直滚动条 */
        .list::-webkit-scrollbar {
          width: 0;
        }
      }
    }
  }
  .PopUp1 {
    width: 100%;
    height: 100%;
    .modal-content {
      width: 100%;
      height: 100%;
      .modal-content {
        height: 100%;
        width: 100%;
        padding: 20px;
      }
    }
  }
}
</style>
