<template>
  <div class="conduct">
    <div class="conduct-right">
      <div class="conduct-box1">
        <!-- 预案匹配 -->
        <div class="collapse-panel">
          <div class="collapse-header interactive-button" @click="togglePanel('plan-match')">
            <div class="img"></div>
            <span>预案匹配</span>
          </div>
          <div class="collapse-content" :class="{ show: activePanel === 'plan-match' }">
            <div class="tit">
              <span>事件标签</span>
              <el-select v-model="value" placeholder="Select" @change="updateOptions" size="large" style="width: 170px">
                <el-option v-for="item in options" :key="item.value" :label="item.name" :value="item.value" />
              </el-select>
            </div>
            <div class="con1">
              <div
                class="box1 interactive-card"
                v-for="(item, index) in prePlanData"
                @click="planClick(item)"
                :class="currentPlanId == item.id ? 'active' : ''"
                :key="index"
              >
                <div class="box1-l">
                  <span>{{ item.planName }}</span>
                  <span>{{ item.publishUnit }}</span>
                </div>
                <div class="box1-r interactive-button" @click="updatePlanFc(item.id)">
                  <span>匹配度：{{ item.isQueried == 0 ? '100%' : '0%' }}</span>
                </div>
              </div>
              <noData v-if="prePlanData.length == 0"></noData>
            </div>
          </div>
        </div>

        <!-- 险情同步 -->
        <div class="collapse-panel">
          <div class="collapse-header interactive-button" @click="togglePanel('plan-danger')">
            <div class="img"></div>
            <span>险情同步</span>
          </div>
          <div class="collapse-content content2 interactive-card" @click="showModal2 = true" :class="{ show: activePanel === 'plan-danger' }">
            <div class="name">
              <span>协同单位</span>
              <span>负责人</span>
              <span>是否已读</span>
              <span>响应时间</span>
            </div>
            <div class="list">
              <div class="line" v-for="(item, index) in dangerList" :key="index">
                <span class="line-span" :title="item.deptName">{{ item.deptName }}</span>
                <span class="line-span" :title="item.nickName">{{ item.nickName }}</span>
                <span class="line-span"
                  ><i :class="item.status == 1 ? 'isTrue' : ''">{{ item.status == 1 ? '已读' : '未读' }}</i></span
                >
                <span class="line-span" :title="item.status == 1 ? item.update_time : ' '">{{ item.status == 1 ? item.update_time : '-' }}</span>
              </div>
              <noData v-if="dangerList.length == 0"></noData>
            </div>
          </div>
        </div>

        <!-- 现场处置 -->
        <div class="collapse-panel">
          <div class="collapse-header interactive-button" @click="togglePanel('plan-dispose')">
            <div class="img"></div>
            <span>现场处置</span>
          </div>
          <div class="collapse-content" :class="{ show: activePanel === 'plan-dispose' }">
            <div class="dispose">
              <div class="dispose-box" v-for="(item, index) in disposeList" :key="index">
                <div class="box-l">{{ item.time }}</div>
                <div class="bg"></div>
                <div class="time">{{ item.tm }}</div>
                <div class="box-r">
                  <div class="lin1">
                    <span>.........</span>
                    <span></span>
                    <span>{{ item.nodeName }}</span>
                  </div>
                  <div class="lin2">
                    <span>{{ item.note }}</span>
                    <span class="interactive-button" @click="detailsFc(item)"
                      ><el-icon><Document /></el-icon>详情</span
                    >
                  </div>
                </div>
              </div>
              <noData v-if="disposeList.length == 0"></noData>
            </div>
          </div>
        </div>

        <!-- 物资点验 -->
        <div class="collapse-panel">
          <div class="collapse-header" @click="togglePanel('plan-supplies')">
            <div class="img"></div>
            <span>物资点验</span>
          </div>
          <div class="collapse-content" :class="{ show: activePanel === 'plan-supplies' }">
            <div class="supplies">
              <div class="supplies-box">
                <div class="title">应急知识</div>
                <div class="tb">
                  <div class="th">
                    <span>编号</span>
                    <span>标题</span>
                    <span>分类</span>
                    <span>来源</span>
                  </div>
                  <div class="td">
                    <div class="line" v-for="(item, index) in suppliesList" :key="index">
                      <span>{{ item.id }}</span>
                      <span :title="item.title">{{ item.title }}</span>
                      <span>{{ item.category }}</span>
                      <span>{{ item.source }}</span>
                    </div>
                    <noData v-if="suppliesList.length == 0"></noData>
                  </div>
                </div>
              </div>
              <div class="supplies-box">
                <div class="title">应急物资</div>
                <div class="tb supplies2">
                  <div class="th">
                    <span>编号</span>
                    <span>标题</span>
                    <span>分类</span>
                    <span>数量</span>
                    <span>规格</span>
                  </div>
                  <div class="td">
                    <div class="line" v-for="(item, index) in suppliesList2" :key="index">
                      <span>{{ item.id }}</span>
                      <span :title="item.name">{{ item.name }}</span>
                      <span>{{ item.categoryName }}</span>
                      <span>{{ item.current_stock }}</span>
                      <span>{{ item.unit }}</span>
                    </div>
                    <noData v-if="suppliesList2.length == 0"></noData>
                  </div>
                </div>
              </div>
              <div class="supplies-box">
                <div class="title">救援队伍</div>
                <div class="tb">
                  <div class="th">
                    <span>编号</span>
                    <span>队伍名称</span>
                    <span>类型</span>
                    <span>人数</span>
                  </div>
                  <div class="td">
                    <div class="line" v-for="(item, index) in suppliesList3" :key="index">
                      <span>{{ item.id }}</span>
                      <span :title="item.teamName">{{ item.teamName }}</span>
                      <span>{{ item.teamType }}</span>
                      <span>{{ item.memberCount }}</span>
                    </div>
                    <noData v-if="suppliesList3.length == 0"></noData>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 处置评估 -->
        <div class="collapse-panel">
          <div class="collapse-header" @click="togglePanel('plan-evaluate')">
            <div class="img"></div>
            <span>处置评估</span>
          </div>
          <div class="collapse-content" :class="{ show: activePanel === 'plan-evaluate' }">
            <div class="evaluate" ref="evaluateRef"></div>
          </div>
        </div>
      </div>
      <div class="conduct-box2">
        <div class="box" v-for="(item, index) in lsitData" :key="index">
          <span :class="currentStatus == item.state ? 'zt1' : ''">{{ index + 1 }}</span>
          <span>{{ item.name }}</span>
          <span>
            <i></i>
            <i></i>
          </span>
        </div>
      </div>
      <div class="conduct-box3">
        <Box :title="'事件列表'" class="Box1">
          <template #content>
            <div class="content">
              <div class="btn">
                <span
                  v-for="(item, index) in activeName"
                  :key="index"
                  :class="index == currentIndex ? 'current' : ''"
                  @click="questList(index, item.value)"
                  :style="{ opacity: loading ? '0.6' : '1', pointerEvents: loading ? 'none' : 'auto' }"
                  >{{ item.name }}</span
                >
                <span @click="refreshEventsList" :style="{ opacity: loading ? '0.6' : '1', pointerEvents: loading ? 'none' : 'auto' }"
                  ><el-icon><RefreshRight /></el-icon>刷新</span
                >
              </div>
              <div class="con">
                <div v-if="loading" class="loading-container">
                  <el-icon class="is-loading"><Loading /></el-icon>
                  <span>加载中...</span>
                </div>
                <div class="con-box" v-for="(item, index) in projectData" :class="item.id == currentIndex3 ? 'active' : ''" :key="index" v-else>
                  <div class="tit">
                    <div class="bt">{{ item.eventName }}</div>
                    <div class="img">
                      <span></span>
                      <span></span>
                    </div>
                  </div>
                  <div class="list" @click="getEventsDetail(item, item.id)">
                    <span class="td">事件描述：{{ item.description }}</span>
                    <span class="td">发生时间：{{ item.happenTime }}</span>
                    <span class="td">事件地点：{{ item.location }}</span>
                    <span class="td">事件状态：{{ updateStatus(item.status) }}</span>
                  </div>
                  <div class="con-btn">
                    <span @click="setViewToCoordinates([item.lon, item.lat])">立即处理</span>
                    <span @click="emergencyFc()">应急处置</span>
                  </div>
                </div>
                <noData v-if="projectData.length == 0 && !loading"></noData>
              </div>
            </div>
          </template>
        </Box>
      </div>
      <div class="conduct-box4">
        <div class="btn">
          <div
            class="box"
            v-for="(item, index) in activeName2"
            @click="questList2(index, item.value)"
            :class="index == currentIndex2 ? 'current' : ''"
            :key="index"
            :style="{ opacity: loading ? '0.6' : '1', pointerEvents: loading ? 'none' : 'auto' }"
          >
            {{ item.name }}
          </div>
        </div>
      </div>
    </div>
    <PopUp :visible="showModal1" title="现场处置" @update:visible="showModal1 = $event" class="PopUp1">
      <div class="modal-content">
        <div class="list">
          <div class="line">
            <span
              >处置单位：<i>{{ detailsList?.nodeName }}</i></span
            >
          </div>
          <div class="line">
            <span
              >处置时间：<i>{{ detailsList?.processTime }}</i></span
            >
          </div>
          <div class="line">
            <span
              >处置内容：<i>{{ detailsList?.note }}</i></span
            >
          </div>
        </div>
        <div class="video">
          <img class="video-box" v-if="detailsList.fileUrl[0]" :src="detailsList.fileUrl[0]" alt="" />
          <noData class="video-box" v-else text="暂无图片"></noData>
        </div>
      </div>
    </PopUp>
    <PopUp :visible="showModal2" title="险情同步" @update:visible="showModal2 = $event" class="PopUp2">
      <div class="modal-content">
        <!-- 查询条件 -->
        <div class="btn">
          <span
            v-for="(item, index) in queryDanger"
            :key="index"
            :class="index == queryIndex ? 'current' : ''"
            @click="questDangerFc(index, item.value)"
            >{{ item.name }}</span
          >
          <span class="synchronously" @click="synchronouslyFc">一键同步</span>
        </div>
        <Table :columns="columns" :fetchData="dangerDetail" :searchParams="searchParams" :multiSelect="true" v-model:selectedRows="selectedRows">
          <template #status="scope">
            <span>
              <i class="read" :class="scope.row.status == 1 ? 'isTrue' : ''">{{ scope.row.status == 1 ? '已读' : '未读' }}</i></span
            >
          </template>
          <template #update_time="scope">
            <span> {{ scope.row.status == 1 ? scope.row.update_time : '-' }}</span>
          </template>
        </Table>
      </div>
    </PopUp>
    <PopUp :visible="showModal3" title="应急处置" @update:visible="showModal3 = $event" class="PopUp3">
      <div class="modal-content" style="min-height: 700px">
        <div class="con-left">
          <!-- 搜索框 -->
          <div class="search-bar">
            <el-input v-model="title" @change="updateTitle" placeholder="请输入" clearable style="width: 300px" />
            <el-button type="primary" @click="handleSearch" style="width: 100px; margin-left: 27px"
              ><el-icon><Search /></el-icon>搜索</el-button
            >
          </div>
          <div class="treeList">
            <el-tree
              style="max-width: 600px; background-color: #353638"
              @check="handleCheck"
              :data="treeData"
              :props="defaultProps"
              :default-expanded-keys="defaultChecked"
              :default-checked-keys="defaultChecked"
              show-checkbox
              class="custom-tree"
              ref="treeRef"
              node-key="id"
            />
          </div>
        </div>
        <div class="con-right">
          <div class="box-1">
            <span>已选择</span>
            <span @click="clearSelected">清空已选</span>
          </div>
          <div class="list">
            <div class="tit">人员（{{ selectedUserList.length }}）</div>
            <div class="line-con">
              <div class="line" v-for="(user, index) in selectedUserList" :key="index">
                {{ user.userName }}（{{ user.deptName }}）- {{ user.phone }}
              </div>
            </div>
          </div>
          <div class="con">
            <div class="tit">通知内容：</div>
            <div class="content">
              <el-input v-model="content" type="textarea" disabled :rows="4" placeholder="请输入通知内容" />
            </div>
          </div>
          <div class="btn" @click="notifyFc">确定</div>
        </div>
      </div>
    </PopUp>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, watch } from 'vue';
import Box from '@/components/Box/index.vue';
import * as echarts from 'echarts';
import PopUp from '@/components/PopUp/index.vue';
import Table from '@/components/Table/index.vue';
import noData from '@/components/noData/index.vue';
import { Loading } from '@element-plus/icons-vue';
import {
  getEventsList,
  getEventsByPlan,
  getNotifyList,
  getNotifyPage,
  getLogsId,
  getSupplies,
  getKnowledge,
  getTeams,
  getEvaluation,
  getNoticeTree,
  getEventPlan
} from '@/api/bridge/conduct';
import { addPoint, setViewToCoordinates, getPointById, updatePointCoordinates, clearTypeFeatures } from '@/utils/mapMethods';
import policeImg from '@/assets/conduct/police.png';
import { postNotify, getPersonnelNames } from '@/api/bridge/emergency';

// 图表引用
const evaluateRef = ref<HTMLElement | null>(null);

// 图表实例
let evaluateEcharts: echarts.ECharts | null = null;

// 监听窗口大小变化，调整图表大小
const handleResize = () => {
  evaluateEcharts?.resize();
};

//应急处置弹窗
const showModal3 = ref(false);
const defaultProps = {
  children: 'children',
  label: 'name'
};
const treeData = ref([]);

//应急处置
const emergencyFc = () => {
  if (!currentPlanId.value) return ElMessage.warning('请选择一个预案！');
  showModal3.value = true;
};

//获取部门组织数据
const getNoticeTreeFc = async (keyword?: any) => {
  const res = await getNoticeTree({ keyword });
  if (res.code == 200) {
    treeData.value = res.data || [];
  }
};
//拨打电话以及短信
const notifyFc = async () => {
  if (selectedUserList.value.length == 0) {
    ElMessage.info('请选择人员');
    return;
  }

  try {
    await ElMessageBox.confirm('确定通知?', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    });

    // 用户点击确定后执行通知
    const smsPromise = postNotify({
      type: 'sms',
      phone: selectedUserList.value.map((item) => item.phone),
      warnId: searchParams.value.eventId
    });

    const phonePromise = postNotify({
      type: 'phone',
      phone: selectedUserList.value.map((item) => item.phone),
      warnId: searchParams.value.eventId
    });

    const [smsRes, phoneRes] = await Promise.all([smsPromise, phonePromise]);

    if (smsRes.code === 200 && phoneRes.code === 200) {
      ElMessage.success('通知发送成功');
      refreshEventsList(); //刷新事件列表
      updateOptions(''); //预案匹配
      getNotifyListFc(searchParams.value.eventId); //险情同步
      initevaluateEcharts(searchParams.value.eventId); //处置评估
      getLogsIdFc({ eventId: searchParams.value.eventId }); //现场处置
    } else {
      ElMessage.error('通知发送失败');
    }
  } catch (error) {
    if (error === 'cancel') {
      // 用户取消操作，不需要显示错误信息
      return;
    }
    console.error('通知发送出错:', error);
    ElMessage.error('通知发送出错');
  }
};
//通知内容
const content = ref('【紧急预警/预警通知】%s：%s 在 %s 发生"%s"预警。请速登录系统"预警/事件"管理，了解详情。');
// 添加选中人员的数据存储
const selectedUserList = ref([]);

// 查找节点的父节点
const findParentNode = (nodeId, treeData) => {
  for (const node of treeData) {
    if (node.children) {
      // 检查当前节点的子节点
      const found = node.children.find((child) => child.id === nodeId);
      if (found) {
        return node;
      }
      // 递归检查子节点的子节点
      const parent = findParentNode(nodeId, node.children);
      if (parent) {
        return parent;
      }
    }
  }
  return null;
};

// 通过节点ID获取完整节点数据
const handleCheck = (currentNode, checkedStatus) => {
  // 获取选中的节点数据
  const checkedNodes = checkedStatus.checkedNodes;
  // 获取选中的人员数据
  selectedUserList.value = checkedNodes
    .filter((node) => !node.children)
    .map((node) => {
      // 获取父节点（部门）信息
      const parentNode = findParentNode(node.id, treeData.value);
      return {
        userName: node.name,
        id: node.id,
        deptName: parentNode ? parentNode.name : '未知部门',
        phone: node.phone
      };
    });
};
//应急处置搜索框
const title = ref('');
const updateTitle = (value) => {
  title.value = value;
};
//搜索
const handleSearch = () => {
  getNoticeTreeFc(title.value);
};

const value = ref(' ');
const options = [
  {
    name: '自然灾害',
    value: 1
  },
  {
    name: '事故灾难',
    value: 2
  },
  {
    name: '公共卫生事件',
    value: 3
  },
  {
    name: '社会安全事件',
    value: 4
  }
];

// 当前激活的面板
const activePanel = ref('plan-match');
// 切换面板的函数
const togglePanel = (panelId) => {
  if (activePanel.value === panelId) {
    // 如果点击的是当前面板，则关闭
    activePanel.value = null;
  } else {
    // 否则，展开点击的面板
    activePanel.value = panelId;
  }
};
//预案匹配数据
const prePlanData = ref<any>([]);
//默认的人员
const defaultChecked = ref([]);
//预案匹配点击
const planClick = async (item) => {
  if (!searchParams.value.eventId) return ElMessage.warning('请先选择一个事件');
  currentPlanId.value = item.id;
  await getPersonnelNamesFc(item.id);
  showModal3.value = true;
};
//根据事件id和预案id修改预案匹配
const updatePlanFc = async (planId) => {
  if (!searchParams.value.eventId) return ElMessage.warning('请先选择一个事件');
  ElMessageBox.confirm('是否确定关联当前预案？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      getEventPlan({ planId, eventId: searchParams.value.eventId }).then((res) => {
        if (res.code == 200) {
          ElMessage.success('操作成功');
          updateOptions('');
        }
      });
    })
    .catch(() => {
      // 用户点击取消按钮
    });
};
//根据预案id获取人员
const getPersonnelNamesFc = async (planId) => {
  const res = await getPersonnelNames({ planId });
  if (res.code == 200) {
    defaultChecked.value = res.data || [];
  }
};
//下拉选项方法
const updateOptions = async (value) => {
  const res = await getEventsByPlan({ planId: currentPlanId.value, eventCategory: value });
  if (res.code == 200) {
    prePlanData.value = res.data || [];
  }
};
//根据事件id查询预案匹配
const currentIndex3 = ref(-1);
//预案id
const currentPlanId = ref('');
//当前事件状态
const currentStatus = ref(-1);
//点击事件列表之后的操作
const getEventsDetail = async (item, index) => {
  currentIndex3.value = index; //事件列表的当前选中
  currentPlanId.value = item.planId; //预案id
  currentStatus.value = item.flowStatus; //事件处置的当前状态
  searchParams.value.eventId = item.id; //事件id
  //预案匹配数据
  const res = await getEventsByPlan({ planId: item.planId });
  if (res.code == 200) {
    prePlanData.value = res.data || [];
    value.value = res.data[0]?.eventCategory || ' '; //选中预案事件之后回显预案匹配下拉
    // console.log(res.data);
  }
  getNotifyListFc(item.id); //险情同步
  initevaluateEcharts(item.id); //处置评估
  getLogsIdFc({ eventId: item.id }); //现场处置
};
//险情同步数据
const dangerList = ref([]);
const getNotifyListFc = async (eventId) => {
  const res = await getNotifyList(eventId);
  if (res.code == 200) {
    dangerList.value = res.data || [];
  }
};

const showModal2 = ref(false);
const queryIndex = ref(0);
const queryDanger = ref([
  {
    name: '全部',
    value: 'All'
  },
  {
    name: '已读',
    value: '1'
  },
  {
    name: '未读',
    value: '0'
  }
]);
//一键同步
const synchronouslyFc = () => {
  console.log(selectedRows, '一键同步');
};

//表格选中之后的数据
const selectedRows = ref([]);

//条件选择
const questDangerFc = (index, value) => {
  queryIndex.value = index;
  searchParams.value.status = value;
};
//险情同步表格查询参数
const searchParams = ref({
  status: 'All',
  eventId: ''
});
//险情同步表格columns
const columns = ref([
  // {
  //   prop: 'eventName',
  //   label: '事件名称',
  //   align: 'center'
  // },
  {
    prop: 'deptName',
    label: '协同单位',
    align: 'center'
  },
  {
    prop: 'nickName',
    label: '负责人',
    align: 'center'
  },
  {
    prop: 'contact_method',
    label: '联系电话',
    align: 'center'
  },
  {
    prop: 'notify_method',
    label: '同步方式',
    align: 'center'
  },
  {
    prop: 'create_time',
    label: '同步时间',
    align: 'center'
  },
  {
    prop: 'status',
    label: '是否已读',
    align: 'center',
    slot: 'status'
  },
  {
    prop: 'update_time',
    label: '响应时间',
    align: 'center',
    slot: 'update_time'
  }
]);

//获取险情同步弹窗列表数据
const dangerDetail = async (params?: {}) => {
  try {
    const res = await getNotifyPage(params);
    if (res.code == 200) {
      return {
        data: res.data.records || [],
        total: res.data.total || 0
      };
    }
  } catch (error) {
    console.error('获取预警监督数据失败:', error);
    ElMessage.error('获取数据失败');
    return {
      data: [],
      total: 0
    };
  }
};

//现场处置
const disposeList = ref([]);
const getLogsIdFc = async (eventId) => {
  const res = await getLogsId(eventId);
  if (res.code == 200) {
    disposeList.value = res.data?.map((item) => {
      return {
        time: item.processTime?.split(' ')[0],
        tm: item.processTime?.split(' ')[1],
        ...item
      };
    });
  }
};
//现场处置详情
const detailsList = ref<any>([]);
const detailsFc = (data) => {
  showModal1.value = true;
  detailsList.value = {
    ...data,
    fileUrl: JSON.parse(data.fileUrl)
  };
};
const showModal1 = ref(false);

//物资点研数据
const suppliesList = ref([]);
const getKnowledgeFc = async () => {
  const res = await getKnowledge();
  if (res.code == 200) {
    suppliesList.value = res.data || [];
  }
};
const suppliesList2 = ref([]);
const getSuppliesFc = async () => {
  const res = await getSupplies();
  if (res.code == 200) {
    suppliesList2.value = res.data || [];
  }
};
const suppliesList3 = ref([]);
const getTeamsFc = async () => {
  const res = await getTeams();
  if (res.code == 200) {
    suppliesList3.value = res.data || [];
  }
};

//处置评估
const initevaluateEcharts = async (eventId) => {
  const res = await getEvaluation({ eventId });
  let arrData = [];
  let sum = 100;
  let scores = [100, 100, 100, 100, 100];
  if (res.code == 200) {
    scores = [];
    res.data[0]?.scoreDetails.forEach((item) => {
      arrData.push({
        name: item.dimension,
        sum: item.score,
        max: 100
      });
      scores.push(item.score);
    });
    sum = res.data[0]?.total_score;
  }
  if (evaluateRef.value) {
    evaluateEcharts = echarts.init(evaluateRef.value);
    // const stages = [
    //   { name: '社会影响', max: 100, content: '10条负面舆情', sum: 10 },
    //   { name: '应急人员伤亡', max: 100, content: '1人伤亡', sum: 5 },
    //   { name: '应急物资', max: 100, content: '0件应急物件超出', sum: 0 },
    //   { name: '处置时长', max: 100, content: '0条任务超时', sum: 0 },
    //   { name: '经济损失', max: 100, content: '0经济损失', sum: 0 }
    // ]; // 直接传入的数组数据

    function contains(arr, obj) {
      var i = arr.length;
      while (i--) {
        if (arr[i].name === obj) {
          return i;
        }
      }
      return false;
    }

    const option = {
      graphic: [
        {
          type: 'text',
          left: 'center', // 水平居中
          top: 'center', // 垂直居中
          z: 10,
          style: {
            text: sum,
            fontSize: 53,
            fontWeight: 'bold',
            fill: '#fff',
            textAlign: 'center' // 文本水平居中
          }
        }
      ],
      radar: {
        radius: '50%',
        shape: 'polygon', // 设置为多边形，形成五角形
        triggerEvent: true,
        name: {
          rich: {
            a: {
              width: 150, // 设置明确的宽度
              height: 25,
              lineHeight: 25, // 确保垂直居中
              textAlign: 'center', // 确保水平居中
              fontSize: 20,
              fontWeight: 'bold',
              color: '#85C2FF'
            },
            b: {
              width: 150, // 设置明确的宽度
              height: 25,
              lineHeight: 25, // 确保垂直居中
              textAlign: 'center', // 确保水平居中
              fontSize: 16,
              color: '#fff'
            },
            c: {
              width: 150, // 设置明确的宽度
              height: 25,
              lineHeight: 25, // 确保垂直居中
              textAlign: 'center', // 确保水平居中
              fontSize: 16,
              color: '#fff',
              fontWeight: 'bold'
            }
          },
          formatter: (a, item) => {
            let i = contains(arrData, a);
            // return `{c| -${item.sum}分}\n{a| ${a}}\n{b| (${item.content})}`;
            return `{c| ${item.sum}分}\n{a| ${a}}`;
          }
        },
        nameGap: 25,
        indicator: arrData,
        splitArea: {
          areaStyle: {
            color: [
              'rgba(39,67,143, 0.1)',
              'rgba(39,67,143, 0.2)',
              'rgba(39,67,143, 0.4)',
              'rgba(39,67,143, 0.6)',
              'rgba(39,67,143, 0.8)',
              'rgba(39,67,143, 1)'
            ].reverse()
          },
          z: 1 // 降低 splitArea 的层级
        },
        axisLine: {
          lineStyle: {
            color: 'rgba(0,0,0,0)'
          }
        },
        splitLine: {
          lineStyle: {
            width: 2,
            color: ['rgba(45,65,110, 0.6)'].reverse()
          }
        }
      },
      series: [
        {
          name: '进站',
          type: 'radar',
          areaStyle: {
            normal: {
              color: new echarts.graphic.LinearGradient(
                0,
                1,
                0,
                0,
                [
                  {
                    offset: 0,
                    color: '#37c5f4'
                  },
                  {
                    offset: 1,
                    color: '#19469a'
                  }
                ],
                false
              )
            }
          },
          symbolSize: 0,
          lineStyle: {
            normal: {
              color: '#32a9ea',
              width: 1
            }
          },
          data: [
            {
              value: scores, // 直接传入 scores 数组
              name: '进站'
            }
          ]
        }
      ]
    };
    evaluateEcharts.setOption(option);
  }
};

//步骤条
const lsitData = ref([
  {
    state: 1,
    name: '预案选择'
  },
  {
    state: 2,
    name: '险情同步'
  },
  {
    state: 3,
    name: '现场处置'
  },
  {
    state: 4,
    name: '应急终止'
  },
  {
    state: 5,
    name: '后期处置'
  },
  {
    state: 6,
    name: '应急总结'
  }
]);

//事件列表条件切换
const activeName = ref([
  {
    name: '未处理',
    value: '1'
  },
  {
    name: '处理中',
    value: '2'
  },
  {
    name: '已处理',
    value: '3'
  }
]);
const currentIndex = ref(0);
const eventParams = ref({
  status: '1',
  eventCategory: 'All'
});

// 添加 loading 状态
const loading = ref(false);

//事件列表
const getEventsListFc = async (params?: {}) => {
  const res = await getEventsList(params);
  if (res.code == 200) {
    projectData.value = res.data || [];
    if (res.data.length > 0) {
      res.data.forEach((item) => {
        const pointId = `point_event${item.id}`;
        const fileUrl = JSON.parse(item.fileUrl);
        // 弹窗内容
        const popupContent = `
          <div class="popup-title" style="min-width:700px;min-height:60px;display:flex;justify-content: space-between;align-items: center;background: linear-gradient(141deg, rgba(40, 40, 61, 0.85) 0%, rgba(0, 0, 0, 0.57) 44%, rgba(4, 3, 9, 0) 100%);padding:0px 25px;">
        <span style="color:#fff;font-size:25px;font-weight: bold;">${item.eventName}</span><button class="popup-close" style="font-size:20px">X</button>
      </div>
      <div class="popup-content" style="min-width:700px;min-height:200px;background: linear-gradient(141deg, rgba(40, 40, 61, 0.85) 0%, rgba(0, 0, 0, 0.57) 44%, rgba(4, 3, 9, 0) 100%);padding:25px;border-top: 1px solid #accbff; ">
        <div class="ship-popup" style="width:100%;height:auto;">
              <div class="ship-info" style="font-size: 20px;color: #fff;display:flex;justify-content: space-between;margin-bottom:20px;">
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">事件编号: ${item.id || ''}</span>
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">经纬度: ${item.lon},${item.lat}</span>
              </div>
              <div class="ship-info" style="font-size: 20px;color: #fff;display:flex;justify-content: space-between;margin-bottom:20px;">
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">发生时间: ${item.happenTime || ''}</span>
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">事件地点: ${item.location || ''}</span>
              </div>
              <div class="ship-info" style="font-size: 20px;color: #fff;display:flex;justify-content: space-between;margin-bottom:20px;">
                <span style="display:inline-block;width:100%;overflow: hidden;text-overflow: ellipsis;white-space: wrap;">事件描述: ${item.description || ''}</span>
              </div>
              <div class="ship-info" style="font-size: 20px;color: #fff;display:flex;margin-bottom:20px;">
                ${
                  fileUrl && fileUrl[0]
                    ? `<img style="width:300px;height:100px;object-fit:cover;" src="${fileUrl[0]}" alt="事件图片" />`
                    : `<div style="width:300px;height:100px;background:#2f3039;display:flex;justify-content:center;align-items:center;color:#fff;">暂无图片</div>`
                }
              </div>
            </div>
      </div>
          `;
        //判断有没有
        const existingPoint = getPointById(pointId);
        if (existingPoint) {
          // 更新点位坐标
          updatePointCoordinates(existingPoint, [item.lon, item.lat]);
        } else {
          // 添加新的点位
          if (item.lon && item.lat) addPoint([item.lon, item.lat], policeImg, 1, popupContent, pointId);
        }
      });
    }
  }
};

const questList = async (index, value) => {
  currentIndex.value = index;
  eventParams.value.status = value;
  loading.value = true;
  try {
    await getEventsListFc(eventParams.value);
  } finally {
    loading.value = false;
  }
};

const updateStatus = (status) => {
  if (status == '1') {
    return '未处理';
  } else if (status == '2') {
    return '处理中';
  } else if (status == '3') {
    return '已处理';
  }
};
const projectData = ref([]);
const currentIndex2 = ref(0);
const activeName2 = ref([
  {
    name: '全部',
    value: 'All'
  },
  {
    name: '自然灾害',
    value: '1'
  },
  {
    name: '事故灾难',
    value: '2'
  },
  {
    name: '公共卫生事件',
    value: '3'
  },
  {
    name: '社会安全事件',
    value: '4'
  }
]);
const questList2 = async (index, value) => {
  currentIndex2.value = index;
  eventParams.value.eventCategory = value;
  loading.value = true;
  try {
    await getEventsListFc(eventParams.value);
  } finally {
    loading.value = false;
  }
};

// 刷新事件列表
const refreshEventsList = async () => {
  loading.value = true;
  try {
    await getEventsListFc(eventParams.value);
  } finally {
    loading.value = false;
  }
};

// 添加树组件的ref
const treeRef = ref();

// 清空选中
const clearSelected = () => {
  selectedUserList.value = [];
  treeRef.value?.setCheckedKeys([]);
};

// 根据ID从树数据中查找节点
const findNodeById = (id, tree) => {
  for (const node of tree) {
    if (node.id === id) {
      return node;
    }
    if (node.children) {
      const found = findNodeById(id, node.children);
      if (found) return found;
    }
  }
  return null;
};

// 监听 defaultChecked 变化，同步更新 selectedUserList
watch(
  defaultChecked,
  (newVal) => {
    if (newVal && newVal.length > 0) {
      // 使用 setTimeout 确保树组件已经完成渲染和选中状态更新
      setTimeout(() => {
        const checkedNodes = treeRef.value?.getCheckedNodes() || [];
        selectedUserList.value = checkedNodes
          .filter((node) => !node.children)
          .map((node) => {
            const parentNode = findParentNode(node.id, treeData.value);
            return {
              userName: node.name,
              id: node.id,
              deptName: parentNode ? parentNode.name : '未知部门',
              phone: node.phone
            };
          });
      }, 100);
    } else {
      selectedUserList.value = [];
    }
  },
  { immediate: true }
);

onMounted(async () => {
  loading.value = true;
  try {
    await getEventsListFc(eventParams.value);
  } finally {
    loading.value = false;
  }
  getSuppliesFc();
  getKnowledgeFc();
  getTeamsFc();
  getNoticeTreeFc();
  window.addEventListener('resize', handleResize);
  //预案匹配数据
  updateOptions('');
  initevaluateEcharts(''); //处置评估
});

// 组件卸载前移除事件监听
onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize);
  clearTypeFeatures('point_event'); //清除事件点位
});
</script>

<style lang="scss" scoped>
.conduct {
  .conduct-right {
    height: 1377px;
    width: 1502px;
    position: absolute;
    bottom: 44px;
    right: 0px;
    display: flex;
    justify-content: space-between;
    z-index: 1;
    .conduct-box1 {
      height: 100%;
      width: 612px;
      display: flex;
      flex-direction: column;
      .collapse-panel {
        border-radius: 4px;
        overflow: hidden; /* 防止内容溢出 */
        background: #222233;
        padding: 25px 33px;
        margin-bottom: 35px;
        .collapse-header {
          display: flex;
          cursor: pointer;
          .img {
            width: 40px;
            height: 40px;
            background: url('@/components/Box/images/titlebg.png') no-repeat center;
            background-size: 100% 100%;
            margin-right: 14px;
          }
          span {
            font-weight: bold;
            color: #fff;
            font-size: 28px;
            font-family: 'Microsoft YaHei';
          }
        }
        .collapse-content {
          padding: 0 10px;
          max-height: 0; /* 初始高度为 0 */
          overflow: hidden; /* 隐藏超出内容 */
          transition: max-height 0.3s ease-out; /* 添加过渡动画 */
          .tit {
            width: 100%;
            height: 80px;
            margin-top: 20px;
            background: #3b3c48;
            padding-left: 22px;
            display: flex;
            align-items: center;
            span {
              color: #fff;
              font-size: 22;
              font-weight: bold;
              font-family: 'Microsoft YaHei';
              margin-right: 20px;
            }
            .el-select {
              background: #35373c;
              .el-select--large .el-select__wrapper {
                background: #35373c;
              }
              ::v-deep span {
                color: #fff;
                font-family: 'Microsoft YaHei';
                font-size: 17px;
                font-weight: bold;
              }
            }
          }
          .con1 {
            width: 100%;
            overflow-y: scroll;
            height: 671px;
            .box1 {
              height: 137px;
              width: 100%;
              background: #3b3c48;
              margin-top: 20px;
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding: 0px 27px;

              .box1-l {
                display: flex;
                flex-direction: column;
                align-items: center;
                width: 208px;
                overflow: hidden;
                span {
                  color: #fff;
                  font-size: 22px;
                  font-weight: bold;
                  font-family: 'Microsoft YaHei';
                }
                span:nth-child(2) {
                  display: inline-block;
                  width: 100%;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                }
              }
              .box1-r {
                height: 53px;
                width: 160px;
                background: #323438;
                border-radius: 10px;
                border: 1px solid #88909b;
                text-align: center;
                line-height: 53px;
                color: #fff;
                font-size: 20px;
                font-family: 'Microsoft YaHei';
                cursor: pointer;
              }
            }
            .active {
              background: #27354c;
            }
          }
          /* 隐藏垂直滚动条 */
          .con1::-webkit-scrollbar {
            width: 0;
          }
        }
        .content2 {
          .name {
            background: #404351;
            width: 100%;
            height: 72px;
            display: flex;
            color: #fff;
            margin-top: 40px;
            span {
              font-size: 20px;
              font-weight: bold;
              font-family: 'Microsoft YaHei';
              display: flex;
              flex-direction: column;
              justify-content: center;
              align-items: center;
              width: 25%;
            }
          }
          .list {
            width: 100%;
            height: calc(100% - 122px);
            margin-top: 10px;
            overflow-y: scroll;
            .line {
              display: flex;
              width: 100%;
              height: 72px;

              .line-span {
                color: #fff;
                font-size: 20px;
                font-family: 'Microsoft YaHei';
                height: 100%;
                width: 25%;
                line-height: 72px;
                text-align: center;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                i {
                  display: inline-block;
                  height: 30px;
                  width: 60px;
                  font-style: normal;
                  line-height: 30px;
                  text-align: center;
                  background: #d00018;
                }
                .isTrue {
                  background: #00c232;
                }
              }
            }
          }
          /* 隐藏垂直滚动条 */
          .list::-webkit-scrollbar {
            width: 0;
          }
        }

        .dispose {
          /*现场处置 */
          width: 100%;
          height: 100%;
          display: flex;
          flex-direction: column;
          overflow-y: scroll;
          padding-top: 90px;
          .dispose-box {
            width: 100%;
            display: flex;
            align-items: flex-start;
            .box-l {
              width: 56px;
              height: 52px;
              color: #fff;
              font-size: 20px;
              text-align: center;
              font-family: 'Microsoft YaHei';
            }
            .bg {
              width: 22px;
              height: 22px;
              background: url('@/assets/conduct/q.png') no-repeat center;
              background-size: 100%;
              margin: 0px 15px;
              position: relative;
            }
            .bg::before {
              content: ''; /* 必须设置 content */
              position: absolute; /* 绝对定位 */
              left: 50%; /* 垂直居中 */
              top: 20px; /* 向左偏移 15px */
              width: 1px; /* 线段的宽度 */
              height: 70px; /* 线段的高度 */
              border-left: 1px dashed #fff;
              transform: translateX(-50%); /* 垂直居中 */
            }

            .time {
              width: 94px;
              height: 26px;
              color: #fff;
              font-family: 'Microsoft YaHei';
              font-size: 20px;
            }
            .box-r {
              display: flex;
              flex-direction: column;
              width: calc(100% - 204px);
              .lin1 {
                display: flex;
                align-items: center;
                span:nth-child(1) {
                  display: inline-block;
                  width: 36px;
                  height: 36px;
                  color: #fff;
                  text-align: top;
                }
                span:nth-child(2) {
                  background: url('@/assets/conduct/ga.png') no-repeat center;
                  display: inline-block;
                  width: 36px;
                  height: 36px;
                  margin: 0 15px;
                  font-family: 'Microsoft YaHei';
                }
                span:nth-child(3) {
                  color: #fff;
                  font-family: 'Microsoft YaHei';
                  font-size: 20px;
                  display: inline-block;
                  height: 36px;
                  line-height: 36px;
                  font-family: 'Microsoft YaHei';
                }
              }
              .lin2 {
                display: flex;
                align-items: center;
                span:nth-child(1) {
                  color: #a2d4ff;
                  font-family: 'Microsoft YaHei';
                  font-size: 20px;
                  width: 192px;
                  height: 52px;
                  display: inline-block;
                  overflow: hidden;
                  text-overflow: ellipsis;
                }
                span:nth-child(2) {
                  display: inline-block;
                  width: 68px;
                  height: 28px;
                  color: #fff;
                  font-family: 'Microsoft YaHei';
                  font-size: 13px;
                  background: #33507e;
                  border-radius: 4px;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  margin-left: 53px;
                  cursor: pointer;
                }
              }
            }
          }
          .dispose-box:nth-child(1) .bg::after {
            content: ''; /* 必须设置 content */
            position: absolute; /* 绝对定位 */
            left: 50%; /* 垂直居中 */
            bottom: 20px; /* 向左偏移 15px */
            width: 1px; /* 线段的宽度 */
            height: 70px; /* 线段的高度 */
            border-left: 1px dashed #fff;
            transform: translateX(-50%); /* 垂直居中 */
          }
        }
        /* 隐藏垂直滚动条 */
        .dispose::-webkit-scrollbar {
          width: 0;
        }

        .supplies {
          /*物资点研*/
          width: 100%;
          height: 100%;
          display: flex;
          flex-direction: column;
          justify-content: space-around;
          .supplies-box {
            width: 100%;
            height: 30%;
            background: #2f3039;
            padding: 13px 15px;
            .title {
              color: #fff;
              font-size: 22px;
              font-weight: bold;
              font-family: 'Microsoft YaHei';
              margin-left: 13px;
            }
            .tb {
              width: 100%;
              height: 90%;
              margin-top: 13px;
              .th {
                width: 100%;
                height: 40px;
                background: #404652;
                span {
                  display: inline-block;
                  width: 25%;
                  height: 40px;
                  line-height: 40px;
                  text-align: center;
                  color: #fff;
                  font-size: 20px;
                  font-family: 'Microsoft YaHei';
                }
              }
              .td {
                width: 100%;
                height: calc(100% - 53px);
                overflow-y: scroll;
                .line {
                  width: 100%;
                  height: 40px;
                  span {
                    display: inline-block;
                    width: 25%;
                    height: 40px;
                    line-height: 40px;
                    text-align: center;
                    color: #fff;
                    font-size: 20px;
                    font-family: 'Microsoft YaHei';
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                  }
                }
              }
              /* 隐藏垂直滚动条 */
              .td::-webkit-scrollbar {
                width: 0;
              }
            }
            .supplies2 {
              .th span {
                width: 20% !important;
              }
              .td .line span {
                width: 20% !important;
              }
            }
          }
        }
        .evaluate {
          /*处置评估 */
          width: 100%;
          height: 764px;
        }
        .collapse-content.show {
          height: 764px;
          max-height: 764px; /* 设置一个足够大的高度 */
        }
      }
    }
    .conduct-box2 {
      height: 100%;
      width: 116px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      padding: 44px 18px;
      background: #222233;
      border-radius: 4px;
      .box {
        width: 80px;
        height: 16%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: space-between;
        span:nth-child(1) {
          display: inline-block;
          color: #fff;
          font-weight: bold;
          font-family: 'Microsoft YaHei';
          font-size: 24px;
          width: 80px;
          height: 80px;
          background: url('@/assets/conduct/zt2.png') no-repeat center;
          background-size: 100%;
          line-height: 80px;
          text-align: center;
        }
        span:nth-child(2) {
          display: inline-block;
          color: #fff;
          font-weight: bold;
          font-family: 'Microsoft YaHei';
          font-size: 28px;
          width: 60px;
          white-space: wrap;
        }
        span:nth-child(3) {
          display: inline-block;
          height: 28px;
          width: 28px;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          i {
            display: inline-block;
            height: 10px;
            width: 28px;
            font-style: normal;
            background: url('@/assets/conduct/jt.png') no-repeat center;
            background-size: 100%;
          }
        }
        .zt1 {
          background: url('@/assets/conduct/zt1.png') no-repeat center !important;
        }
      }
      .box:last-child {
        span:nth-child(3) {
          i {
            background: none;
          }
        }
      }
    }
    .conduct-box3 {
      height: 100%;
      width: 612px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      .Box1 {
        width: 100%;
        height: 100%;
        position: relative;
        .content {
          flex: 1;
          width: 100%;
          overflow: hidden;
          .btn {
            width: 100%;
            display: flex;
            flex-wrap: wrap;
            margin: 20px 0px;
            span {
              display: inline-block;
              width: 22%;
              height: 36px;
              border-radius: 6px;
              color: #b6b6bb;
              font-size: 20px;
              font-family: 'Microsoft YaHei';
              background: #50505a;
              cursor: pointer;
              display: flex;
              justify-content: center;
              align-items: center;
              margin-top: 10px;
              margin-right: 10px;
            }
            .current {
              background: #416194;
              color: #fff;
            }
          }
          .con {
            width: 100%;
            height: calc(100% - 120px);
            margin-top: 18px;
            overflow-y: scroll;
            .loading-container {
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
              height: 200px;
              color: #fff;
              font-size: 20px;
              font-family: 'Microsoft YaHei';
              .el-icon {
                font-size: 40px;
                margin-bottom: 20px;
                color: #416194;
              }
            }
            .con-box {
              height: 272px;
              width: 100%;
              background: #3c3c44;
              border-radius: 4px;
              margin-bottom: 24px;
              padding: 20px 42px 22px 47px;
              display: flex;
              flex-direction: column;
              justify-content: space-between;
              .tit {
                width: 100%;
                height: 35px;
                display: flex;
                justify-content: space-between;
                .bt {
                  color: #fff;
                  font-size: 22px;
                  font-weight: bold;
                  font-family: 'Microsoft YaHei';
                }
                .img {
                  width: 90px;
                  height: 100%;
                  display: flex;
                  justify-content: space-between;
                  align-items: center;
                  span:nth-child(1) {
                    display: inline-block;
                    width: 35px;
                    height: 35px;
                    background: url('@/assets/secialVehicle/bjdj1.png') no-repeat center;
                    background-size: 100%;
                    cursor: pointer;
                  }
                  span:nth-child(2) {
                    display: inline-block;
                    width: 30px;
                    height: 30px;
                    background: url('@/assets/secialVehicle/sc.png') no-repeat center;
                    background-size: 100%;
                    cursor: pointer;
                  }
                }
              }
              .list {
                height: 137px;
                width: 100%;
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                cursor: pointer;
                .td {
                  display: inline-block;
                  width: 100%;
                  color: #fff;
                  font-size: 20px;
                  font-family: 'Microsoft YaHei';
                  text-overflow: ellipsis;
                  white-space: nowrap;
                  overflow: hidden;
                }
              }
              .con-btn {
                width: 100%;
                height: 34px;
                display: flex;
                justify-content: center;
                span {
                  display: inline-block;
                  color: #fff;
                  font-size: 20px;
                  font-family: 'Microsoft YaHei';
                  height: 34px;
                  width: 108px;
                  line-height: 34px;
                  text-align: center;
                  border-radius: 6px;
                  cursor: pointer;
                }
                span:nth-child(1) {
                  margin-right: 10px;
                  background: #333539;
                }
                span:nth-child(2) {
                  background: #365484;
                }
              }
            }
            .active {
              background: #27354c;
            }
          }
          /* 隐藏垂直滚动条 */
          .con::-webkit-scrollbar {
            width: 0;
          }
        }
      }
    }
    .conduct-box4 {
      height: 100%;
      width: 64px;
      display: flex;
      flex-direction: column;
      .btn {
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: end;
        .box {
          width: 46px;
          height: 178px;
          writing-mode: vertical-rl;
          text-orientation: upright;
          color: #fff;
          font-size: 22px;
          font-family: 'Microsoft YaHei';
          display: flex;
          justify-content: center;
          align-items: center;
          cursor: pointer;
          opacity: 0.6;
          margin-bottom: 10px;
        }
        .current {
          opacity: 1;
          font-size: 26px;
          font-weight: bold;
          width: 64px;
        }
        .box:nth-child(1) {
          background: url('@/assets/conduct/qb.png') no-repeat center;
          background-size: 100% 100%;
        }
        .box:nth-child(2) {
          background: url('@/assets/conduct/qtjg.png') no-repeat center;
          background-size: 100% 100%;
        }
        .box:nth-child(3) {
          background: url('@/assets/conduct/dlyj.png') no-repeat center;
          background-size: 100% 100%;
        }
        .box:nth-child(4) {
          background: url('@/assets/conduct/hyfx.png') no-repeat center;
          background-size: 100% 100%;
        }
        .box:nth-child(5) {
          background: url('@/assets/conduct/whpfx.png') no-repeat center;
          background-size: 100% 100%;
        }
      }
    }
  }
  .PopUp1 {
    .modal-content {
      width: 100%;
      display: flex;
      flex-direction: column;
      padding: 20px;
      .list {
        width: 100%;
        height: 278px;
        display: flex;
        flex-direction: column;
        .line {
          width: 100%;
          // height: 40px;
          margin-bottom: 40px;
          span {
            display: inline-block;
            width: 100%;
            color: #fff;
            font-size: 30px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: wrap;
            font-family: 'Microsoft YaHei';
            i {
              font-style: normal;
              color: #accbff;
            }
          }
        }
      }
      .video {
        width: 100%;
        height: 285px;
        display: flex;
        justify-content: space-between;
        span {
          color: #fff;
          font-size: 30px;
          font-family: 'Microsoft YaHei';
        }
        .video-box {
          width: 40%;
          height: 100%;
          background: url('@/assets/secialVehicle/videoBg.png') no-repeat center;
          background-size: 100% 100%;
        }
      }
    }
  }
  .PopUp2 {
    .btn {
      height: 36px;
      width: 100%;
      display: flex;
      padding: 0 20px;
      position: relative;
      span {
        display: inline-block;
        width: 165px;
        height: 100%;
        border-radius: 6px;
        color: #b6b6bb;
        font-size: 20px;
        font-family: 'Microsoft YaHei';
        background: #50505a;
        cursor: pointer;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-right: 20px;
      }
      .synchronously {
        position: absolute;
        right: 0;
        background: #39598b;
        color: #fff;
      }
      .current {
        background: #416194;
        color: #fff;
      }
    }
    .read {
      display: inline-block;
      height: 30px;
      width: 60px;
      font-style: normal;
      line-height: 30px;
      text-align: center;
      background: #d00018;
    }
    .isTrue {
      background: #00c232;
    }
  }
}
.PopUp3 {
  .modal-content {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: space-between;
    .con-left {
      width: 600px;
      height: 700px;
      background: #353638;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      .search-bar {
        padding: 0 20px;
        width: 100%;
        display: flex;
        align-items: center;
        margin-bottom: 20px;
        span {
          color: #fff;
          font-family: 'Microsoft YaHei';
          font-size: 24px;
        }
        :deep(.el-input__wrapper) {
          background: #474750;
        }
        :deep(.el-input__inner) {
          color: #fff;
          font-size: 25px;
        }
      }
      .treeList {
        overflow-y: scroll;
        flex: 1;
        /* 自定义滚动条样式 */
        &::-webkit-scrollbar {
          width: 6px;
        }
        &::-webkit-scrollbar-thumb {
          background: #666;
          border-radius: 3px;
        }
        &::-webkit-scrollbar-track {
          background: #353638;
        }
      }
      :deep(.custom-tree) {
        .el-tree-node__content {
          padding: 20px 0;
          color: #fff;
          font-size: 25px;
          font-family: 'Microsoft YaHei';
        }

        .el-checkbox__label {
          color: #fff;
          font-size: 25px;
          font-family: 'Microsoft YaHei';
        }

        // 修改三角形大小
        .el-tree-node__expand-icon {
          font-size: 25px;
        }

        // 去掉选中背景色
        .el-tree-node.is-current > .el-tree-node__content {
          background-color: transparent !important;
        }

        // 去掉hover背景色
        .el-tree-node__content:hover {
          background-color: transparent !important;
        }
      }
    }
    .con-right {
      width: calc(95% - 600px);
      height: 700px;
      background: #353638;
      padding: 20px;
      position: relative;
      .box-1 {
        width: 100%;
        height: 34px;
        display: flex;
        justify-content: space-between;
        margin-bottom: 20px;
        span {
          color: #fff;
          font-size: 25px;
          font-family: 'Microsoft YaHei';
        }
        span:last-child {
          color: #54a5ff;
          cursor: pointer;
        }
      }
      .list {
        height: 300px;
        width: 100%;
        display: flex;
        flex-direction: column;
        .tit {
          color: #fff;
          font-size: 25px;
          font-family: 'Microsoft YaHei';
        }
        .line-con {
          width: 100%;
          height: 100%;
          overflow-y: auto;
          /* 自定义滚动条样式 */
          &::-webkit-scrollbar {
            width: 6px;
          }
          &::-webkit-scrollbar-thumb {
            background: #666;
            border-radius: 3px;
          }
          &::-webkit-scrollbar-track {
            background: #353638;
          }
          .line {
            color: #fff;
            font-size: 25px;
            font-family: 'Microsoft YaHei';
            margin-top: 20px;
          }
        }
      }
      .con {
        width: 100%;
        height: 200px;
        .tit {
          color: #fff;
          font-size: 25px;
          font-family: 'Microsoft YaHei';
          margin-bottom: 20px;
        }
        .content {
          width: 100%;
          height: 100%;
          background: #4c4e53;
          border-radius: 10px;
          padding: 20px;
          :deep(.el-textarea__inner) {
            background: #4c4e53;
            color: #fff;
            border: none;
            font-size: 25px;
            font-family: 'Microsoft YaHei';
          }
        }
      }
      .btn {
        width: 104px;
        height: 42px;
        position: absolute;
        right: 20px;
        bottom: 20px;
        background: #446397;
        color: #fff;
        font-size: 25px;
        font-family: 'Microsoft YaHei';
        cursor: pointer;
        line-height: 42px;
        text-align: center;
        border-radius: 6px;
      }
    }
  }
}
</style>
