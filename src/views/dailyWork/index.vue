<template>
  <div class="dailyWork">
    <div class="daily-right">
      <div class="daily-box1">
        <Box :title="'警戒力量'" class="Box1" :onTitleClick="videoFc">
          <template v-slot:content>
            <div class="name">
              <span>船名</span>
              <span>警戒海域</span>
              <span>运行状态</span>
              <span>操作</span>
            </div>
            <div class="list">
              <div class="line" v-for="(item, index) in analyzeData" :key="index">
                <span class="line-span" :title="item.shipName">{{ item.shipName }}</span>
                <span class="line-span" :title="item.seaArea">{{ item.seaArea }}</span>
                <span class="line-span">{{ item.sog != 0 ? '运行' : '停泊' }}</span>
                <span class="line-span"
                  ><i @click="viewFc(item)"
                    ><el-icon><View /></el-icon>查看</i
                  ></span
                >
              </div>
              <noData v-if="analyzeData.length == 0"></noData>
            </div>
          </template>
        </Box>
        <Box :title="'任务情况'" class="Box2">
          <template v-slot:content>
            <div class="con-1">
              <div class="img"></div>
              <div class="list list1">
                <span>任务总数</span>
                <span
                  ><i>{{ totalCount }}</i
                  >件</span
                >
              </div>
            </div>
            <div class="bg"></div>
            <div class="con-1 con-2">
              <div class="con">
                <div class="img1"></div>
                <div class="list">
                  <span>未启动</span>
                  <span
                    ><i>{{ statusCount[0]?.count || 0 }}</i
                    >件</span
                  >
                </div>
              </div>
              <div class="con">
                <div class="img2"></div>
                <div class="list">
                  <span>进行中</span>
                  <span
                    ><i>{{ statusCount[1]?.count || 0 }}</i
                    >件</span
                  >
                </div>
              </div>
              <div class="con">
                <div class="img4"></div>
                <div class="list">
                  <span>已关闭</span>
                  <span
                    ><i>{{ statusCount[2]?.count || 0 }}</i
                    >件</span
                  >
                </div>
              </div>
            </div>
            <div class="line">
              <div class="line-box">
                <div class="se">
                  <span>发生时间</span>
                  <el-select v-model="value1" placeholder="Select" @change="updateTime" size="large" style="width: 173px; height: 32px">
                    <el-option v-for="item in options1" :key="item.value" :label="item.label" :value="item.value" />
                  </el-select>
                </div>
                <div class="se">
                  <span>预警类型</span>
                  <el-select v-model="value2" placeholder="Select" @change="updateType" size="large" style="width: 173px; height: 32px">
                    <el-option v-for="item in options2" :key="item.value" :label="item.label" :value="item.value" />
                  </el-select>
                </div>
                <!-- <div class="se">
                  <span>预警等级</span>
                  <el-select v-model="value3" placeholder="Select" size="large" style="width: 173px; height: 32px">
                    <el-option v-for="item in options3" :key="item.value" :label="item.label" :value="item.value" />
                  </el-select>
                </div>
                <div class="se">
                  <span>处置时间</span>
                  <el-select v-model="value4" placeholder="Select" size="large" style="width: 173px; height: 32px">
                    <el-option v-for="item in options4" :key="item.value" :label="item.label" :value="item.value" />
                  </el-select>
                </div> -->
              </div>
              <div class="box-list">
                <div class="box" v-for="item in customColors" :key="item.name">
                  <span> {{ item.name }}：</span>
                  <el-progress :text-inside="true" :stroke-width="19" :percentage="item.percentage" />
                </div>
                <NoData v-if="customColors.length == 0"></NoData>
              </div>
            </div>
          </template>
        </Box>
      </div>
      <div class="daily-box2">
        <Box :title="'流量统计'" class="Box1">
          <template #unit>
            <div class="unit">
              <el-select v-model="value" placeholder="Select" @change="updateTime2" size="large" style="width: 72px">
                <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
              <div class="btn1" @click="onClickFc1"></div>
              <div class="btn2" @click="onClickFc2"></div>
            </div>
          </template>
          <template #content>
            <div class="FlowEcharts" ref="FlowEchartsRef"></div>
          </template>
        </Box>
        <Box :title="'预警分析'" class="Box2">
          <template v-slot:content>
            <div class="content">
              <div class="head">
                <div class="head-lin">
                  <span class="img1"></span>
                  <span>全部</span>
                  <span>{{ incidentData.totalCount || 0 }}</span>
                </div>
                <div class="head-lin">
                  <span class="img4"></span>
                  <span>未处理</span>
                  <span>{{ incidentData.statusStats[1] || 0 }}</span>
                </div>
                <div class="head-lin">
                  <span class="img2"></span>
                  <span>处理中</span>
                  <span>{{ incidentData.statusStats[2] || 0 }}</span>
                </div>
                <div class="head-lin">
                  <span class="img3"></span>
                  <span>已处理</span>
                  <span>{{ incidentData.statusStats[3] || 0 }}</span>
                </div>
              </div>
              <div class="list">
                <div class="list-box" v-for="(item, index) in incidentData.events" :key="index">
                  <div class="img">
                    <img v-if="item.fileUrl[0]" :src="item.fileUrl[0]" alt="" />
                    <noData v-else text="暂无图片"></noData>
                  </div>
                  <div class="con">
                    <span>发生时间：{{ item.publishTime }}</span>
                    <span>发生地点：{{ item.location }}</span>
                    <span>预警事件：{{ item.title }}</span>
                    <span
                      >状态：<i :class="warningState(item.status)">{{
                        item?.status == '1' ? '未处理' : item?.status == '2' ? '处理中' : '已处理'
                      }}</i></span
                    >
                  </div>
                  <div class="btn">
                    <div class="btn1"></div>
                    <div class="btn2" @click="addressClick(item)"></div>
                    <div class="btn3"></div>
                  </div>
                </div>
                <noData v-if="incidentData.events.length == 0"></noData>
              </div>
            </div>
          </template>
        </Box>
      </div>
    </div>
    <!-- 执法船监控 -->
    <PopUp :visible="showModal1" title="执法船监控" :width="'100%'" @update:visible="showModal1 = $event" class="PopUp1">
      <div class="modal-content">
        <VideoList :monitor-list="monitorList" />
      </div>
    </PopUp>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, computed } from 'vue';
import * as echarts from 'echarts';
import Box from '@/components/Box/index.vue';
import txImg from '@/assets/forewarning/tx.png';
import { getSpecialEvents } from '@/api/bridge/forewarning';
import { getWhitelist, getStatistics } from '@/api/bridge/dailyWork';
import { getTrafficFlow } from '@/api/bridge/home';
import { getTrafficFlowStats } from '@/api/bridge/trafficFlow';
import PopUp from '@/components/PopUp/index.vue';
import VideoList from '@/components/VideoList/index.vue';
import { getUrlVideo } from '@/api/bridge/point';
import NoData from '@/components/noData/index.vue';
import { setViewToCoordinates, getPointById, updatePointCoordinates, addPoint, clearTypeFeatures } from '@/utils/mapMethods';
import policeImg from '@/assets/conduct/police.png';

// 图表引用
const FlowEchartsRef = ref<HTMLElement | null>(null);

// 定时器
let timer: any | null = null;
let retryCount = 0;
const MAX_RETRIES = 5;
const TIMEOUT_DURATION = 30000; // 30秒

// 图表实例
let FlowEcharts: echarts.ECharts | null = null;

// 监听窗口大小变化，调整图表大小
const handleResize = () => {
  FlowEcharts?.resize();
};

//执法船监控
const videoFc = () => {
  showModal1.value = true;
  fetchMonitorList();
};

//警戒力量数量
const analyzeData = ref([]);
const getWhitelistFc = async () => {
  const res = await getWhitelist({});
  if (res.code == 200) {
    // console.log(res);
    analyzeData.value = res.data || [];
  }
};
const showModal1 = ref(false);

//调用五次接口还是失败就不用调用了
const getShipRealtimeFcWithRetry = async () => {
  try {
    await getWhitelistFc();
    // 如果成功，重置重试计数
    retryCount = 0;
    // 设置下一次调用
    timer = setTimeout(getShipRealtimeFcWithRetry, TIMEOUT_DURATION);
  } catch (error) {
    retryCount++;
    console.error(`获取数据失败，第 ${retryCount} 次重试`);

    if (retryCount >= MAX_RETRIES) {
      // 达到最大重试次数，清除定时器
      if (timer) {
        clearTimeout(timer);
        timer = null;
      }
      console.error(timer, '达到最大重试次数，停止获取数据');
    } else {
      // 如果还没达到最大重试次数，继续尝试
      timer = setTimeout(getShipRealtimeFcWithRetry, TIMEOUT_DURATION);
    }
  }
};
//根据经纬度点位视角
const viewFc = (data) => {
  if ((data.lat, data.lon)) {
    setViewToCoordinates([data.lon, data.lat]);
  }
};

//任务情况
const value1 = ref('3');
const options1 = [
  {
    value: '1',
    label: '一天'
  },
  {
    value: '2',
    label: '一个月'
  },
  {
    value: '3',
    label: '一年'
  }
];

const value2 = ref('All');
const options2 = [
  {
    value: 'All',
    label: '全部'
  },
  {
    value: '1',
    label: '自然灾害'
  },
  {
    value: '2',
    label: '事故灾难'
  },
  {
    value: '3',
    label: '公共卫生事件'
  },
  {
    value: '4',
    label: '社会安全事件'
  }
];

const value3 = ref('全部');
const options3 = [
  {
    value: 'Option1',
    label: '全部'
  },
  {
    value: 'Option2',
    label: 'Option2'
  }
];

const value4 = ref('全部');
const options4 = [
  {
    value: 'Option1',
    label: '全部'
  },
  {
    value: 'Option2',
    label: 'Option2'
  }
];
//时间筛选
const updateTime = (value) => {
  // console.log(value);
  value1.value = value;
  getStatisticsFc({
    eventCategory: value2.value,
    happenTime: value1.value
  });
};
//类型筛选
const updateType = (value) => {
  // console.log(value);
  value2.value = value;
  getStatisticsFc({
    eventCategory: value2.value,
    happenTime: value1.value
  });
};
//任务情况接口
const totalCount = ref('0');
const statusCount = ref([]);
const getStatisticsFc = async (params?: {}) => {
  const res = await getStatistics(params);
  if (res.code == 200) {
    // console.log(res);
    totalCount.value = res.data.totalCount || '0';
    statusCount.value = res.data.statusCount || [];
    customColors.value = res.data.departmentCount || [];
  }
};

const customColors = ref([
  { percentage: 56, count: 56, name: '养护管理处' },
  { percentage: 47, count: 47, name: '安全监督处' },
  { percentage: 62, count: 62, name: '舟山交通局' },
  { percentage: 25, count: 25, name: '舟山消防' }
]);

//流量统计图表
const initFlowEcharts = async () => {
  const xData = [];
  const inData = [];
  const outData = [];
  const historyData = [];

  try {
    // 使用新的流量统计接口
    const res: any = await getTrafficFlowStats(value.value);
    if (res.code === 200) {
      Object.keys(res.data).forEach((key) => {
        xData.push(key);
        inData.push(res.data[key].value1);
        outData.push(res.data[key].value2);
        historyData.push(res.data[key].value3);
      });
    } else {
      console.error('流量统计接口返回错误:', res.msg);
    }
  } catch (error) {
    console.error('获取流量趋势数据失败:', error);
  }

  if (FlowEchartsRef.value) {
    FlowEcharts = echarts.init(FlowEchartsRef.value);
    const option = {
      xAxis: {
        type: 'category',
        data: xData,
        axisLabel: {
          color: '#86868d',
          fontSize: 20
        },
        axisLine: {
          lineStyle: {
            color: '#86868d'
          }
        },
        splitLine: {
          show: false
        }
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          color: '#86868d',
          fontSize: 20
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: '#86868d'
          }
        },
        splitLine: {
          show: false // 不显示 Y 轴的网格线
        }
      },
      series: [
        {
          name: '进',
          data: inData,
          type: 'line',
          smooth: true,
          lineStyle: {
            color: '#a6d8fc',
            width: 2
          },
          areaStyle: {
            color: 'rgba(168, 214, 253, 0.2)' // 设置面积颜色
          },
          itemStyle: {
            color: '#a6d8fc'
          }
        },
        {
          name: '出',
          data: outData,
          type: 'line',
          smooth: true,
          areaStyle: {
            color: 'rgba(42, 191, 174, 0.2)' // 设置面积颜色
          },
          lineStyle: {
            color: '#35bfb0',
            width: 2
          },
          itemStyle: {
            color: '#35bfb0'
          }
        },
        {
          name: '历史',
          data: historyData,
          type: 'line',
          smooth: true,
          areaStyle: {
            color: 'rgba(96, 129, 237, 0.2)' // 设置面积颜色
          },
          lineStyle: {
            color: '#6081ed',
            width: 2
          },
          itemStyle: {
            color: '#6081ed'
          }
        }
      ],
      legend: {
        right: 60,
        data: ['进', '出', '历史'],
        textStyle: {
          color: '#fff',
          fontSize: 20
        }
      },
      tooltip: {
        trigger: 'axis',
        backgroundColor: '#23334e', // 背景色
        textStyle: {
          color: '#fff' // 字体颜色
        }
      },
      grid: {
        top: 40,
        right: 0,
        bottom: 0,
        left: 0,
        containLabel: true,
        show: true,
        borderWidth: 0 // 不显示外边框
      }
    };
    FlowEcharts.setOption(option);
  }
};
//船流量
const onClickFc1 = () => {
  type.value = 1;
  // initFlowEcharts({ type: 1, time: value.value });
};

const value = ref(1);
const type = ref(1);
const options = [
  {
    value: 1,
    label: '日'
  },
  {
    value: 2,
    label: '月'
  },
  {
    value: 3,
    label: '年'
  }
];
//更新时间
const updateTime2 = (value) => {
  // value.value = value;
  initFlowEcharts();
};
//车流量
const onClickFc2 = () => {
  type.value = 2;
  // initFlowEcharts({ type: 2, time: value.value });
};

//预警分析数据
const incidentData = ref<any>({
  events: [],
  statusStats: {},
  totalCount: 0
});
//预警地址点击
const addressClick = (item) => {
  if (item.lat && item.lon) {
    setViewToCoordinates([item.lon, item.lat]);
  }
};
const getSpecialEventsFc = async () => {
  const res = await getSpecialEvents({});
  if (res.code == 200) {
    // console.log(res);
    incidentData.value.events = res.data.events.map((item) => {
      const pointId = `point_warning${item.id}`;
      const fileUrl = JSON.parse(item.fileUrl);
      // 弹窗内容
      const popupContent = `
          <div class="popup-title" style="min-width:700px;min-height:60px;display:flex;justify-content: space-between;align-items: center;background: linear-gradient(141deg, rgba(40, 40, 61, 0.85) 0%, rgba(0, 0, 0, 0.57) 44%, rgba(4, 3, 9, 0) 100%);padding:0px 25px;">
        <span style="color:#fff;font-size:25px;font-weight: bold;">${item.title}</span><button class="popup-close" style="font-size:20px">X</button>
      </div>
      <div class="popup-content" style="min-width:700px;min-height:200px;background: linear-gradient(141deg, rgba(40, 40, 61, 0.85) 0%, rgba(0, 0, 0, 0.57) 44%, rgba(4, 3, 9, 0) 100%);padding:25px;border-top: 1px solid #accbff; ">
        <div class="ship-popup" style="width:100%;height:auto;">
              <div class="ship-info" style="width:100%;font-size: 20px;color: #fff;display:flex;justify-content: space-between;margin-bottom:20px;">
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">预警类型: ${item.title || ''}</span>
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">预警ID: ${item.id || ''}</span>
              </div>
              <div class="ship-info" style="width:100%;font-size: 20px;color: #fff;display:flex;justify-content: space-between;margin-bottom:20px;">
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">桥梁名称: ${item.bridgeName || ''}</span>
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">发布地点: ${item.location || ''}</span>
              </div>
              <div class="ship-info" style="width:100%;font-size: 20px;color: #fff;display:flex;justify-content: space-between;margin-bottom:20px;">
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">经纬度: ${item.lon},${item.lat}</span>
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">预警时间: ${item.publishTime || ''}</span>
              </div>
              <div class="ship-info" style="width:100%;font-size: 20px;color: #fff;display:flex;justify-content: space-between;margin-bottom:20px;">
                <span style="display:inline-block;width:100%;overflow: hidden;text-overflow: ellipsis;white-space: wrap;">预警内容: ${item.content || ''}</span>
              </div>
              <div class="ship-info" style="font-size: 20px;color: #fff;display:flex;margin-bottom:20px;">
                ${
                  fileUrl && fileUrl[0]
                    ? `<img style="width:300px;height:100px;object-fit:cover;" src="${fileUrl[0]}" alt="预警图片" />`
                    : `<div style="width:300px;height:100px;background:#2f3039;display:flex;justify-content:center;align-items:center;color:#fff;">暂无图片</div>`
                }
              </div>
            </div>
      </div>
          `;

      //判断有没有
      const existingPoint = getPointById(pointId);
      if (existingPoint) {
        // 更新点位坐标
        updatePointCoordinates(existingPoint, [item.lon, item.lat]);
      } else {
        // 添加新的点位
        addPoint([item.lon, item.lat], policeImg, 1, popupContent, pointId);
      }
      return {
        ...item,
        fileUrl
      };
    });
    incidentData.value.statusStats = res.data.statusStats;
    incidentData.value.totalCount = res.data.totalCount;
  }
};
//根据预警状态更改颜色
const warningState = (state) => {
  if (state == '1') {
    return 'current0';
  } else if (state == '3') {
    return 'current1';
  } else {
    return 'current2';
  }
};

const monitorList: any = ref([]);

// 获取监控列表
const fetchMonitorList = async () => {
  try {
    const res = await getUrlVideo();
    monitorList.value = res || [];
  } catch (error) {
    console.error('获取监控列表失败:', error);
  }
};

// 生命周期钩子
onMounted(() => {
  initFlowEcharts();
  getSpecialEventsFc();
  getWhitelistFc();
  timer = setTimeout(getShipRealtimeFcWithRetry, TIMEOUT_DURATION); // 每隔秒调用一次
  getStatisticsFc({
    eventCategory: value2.value,
    happenTime: value1.value
  });
  window.addEventListener('resize', handleResize);
});

// 组件卸载前移除事件监听
onBeforeUnmount(() => {
  if (timer) {
    clearTimeout(timer); // 清除定时器
    timer = null;
  }
  window.removeEventListener('resize', handleResize);
  // 销毁图表实例
  FlowEcharts?.dispose();
  FlowEcharts = null;
  clearTypeFeatures('point_warning'); //清除预警点位
});
</script>

<style scoped lang="scss">
.dailyWork {
  .daily-right {
    height: 1377px;
    width: 1442px;
    position: absolute;
    bottom: 44px;
    right: 60px;
    display: flex;
    justify-content: space-between;
    z-index: 1;
    .daily-box1 {
      height: 100%;
      width: 48%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      .Box1 {
        height: 476px;
        width: 100%;
        position: relative;
        .name {
          background: #262c3f;
          width: 100%;
          height: 62px;
          display: flex;
          color: #fff;
          margin-top: 20px;
          span {
            font-size: 19px;
            font-weight: bold;
            font-family: 'Microsoft YaHei';
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            width: 25%;
          }
        }
        .list {
          width: 100%;
          height: calc(100% - 123px);
          margin-top: 10px;
          overflow-y: scroll;
          .line {
            display: flex;
            width: 100%;
            height: 62px;

            .line-span {
              color: #fff;
              font-size: 17px;
              font-family: 'Microsoft YaHei';
              height: 100%;
              width: 25%;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              min-width: 0;
              display: flex;
              align-items: center;
              justify-content: center;
              i {
                display: inline-block;
                height: 30px;
                width: 60px;
                font-style: normal;
                display: flex;
                align-items: center;
                justify-content: center;
                background: #2e4a78;
                cursor: pointer;
              }
            }
          }
          .line:nth-child(2n) {
            background-color: #232531;
          }
          .line5-dll {
            background: #fe7c2b;
          }
          .line5-lc {
            background: #00ac28;
          }
          .line5-yd {
            background: #fe0020;
          }
        }
        /* 隐藏垂直滚动条 */
        .list::-webkit-scrollbar {
          width: 0;
        }
      }
      .Box2 {
        height: 866px;
        width: 100%;
        position: relative;
        .bg {
          position: absolute;
          width: 203px;
          height: 254px;
          top: 60px;
          right: 37px;
          background: url('@/assets/dailyWork/rwqkBg.png') no-repeat;
        }
        .con-1 {
          width: 100%;
          height: 140px;
          margin-top: 35px;
          padding-left: 20px;
          display: flex;
          align-items: center;
          background: #232531;
          .img {
            width: 100px;
            height: 100px;
            background: url('@/assets/dailyWork/rwzs.png') no-repeat;
            background-size: 100% 100%;
          }
          .img1 {
            width: 66px;
            height: 66px;
            background: url('@/assets/dailyWork/zcczz.png') no-repeat;
            background-size: 100% 100%;
          }
          .img2 {
            width: 66px;
            height: 66px;
            background: url('@/assets/dailyWork/yq.png') no-repeat;
            background-size: 100% 100%;
          }
          .img4 {
            width: 66px;
            height: 66px;
            background: url('@/assets/dailyWork/ygb.png') no-repeat;
            background-size: 100% 100%;
          }
          .list {
            color: #fff;
            display: flex;
            flex-direction: column;
            font-family: 'Microsoft YaHei';
            margin-left: 5px;
            span:nth-child(1) {
              font-size: 23px;
              margin-bottom: 10px;
            }
            span:nth-child(2) {
              font-size: 21px;
              i {
                font-style: normal;
                font-size: 35px;
                font-weight: bold;
                margin-right: 10px;
              }
            }
          }
          .list1 {
            display: flex;
            flex-direction: row;
            align-items: center;
            span:nth-child(1) {
              margin-bottom: 0px;
            }
            span:nth-child(2) {
              i {
                margin: 0px 17px;
              }
            }
          }
          .con {
            width: 25%;
            height: 100%;
            display: flex;
            align-items: center;
          }
        }
        .con-2 {
          justify-content: space-between;
        }
        .line {
          flex: 1;
          margin-top: 20px;
          padding: 20px;
          background: #232531;
          overflow: hidden;
          .line-box {
            width: 100%;
            // height: 84px;
            height: 54px;
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            margin-bottom: 30px;
            .se {
              width: 46%;
              height: 32px;
              display: flex;
              justify-content: space-between;
              align-items: center;
              span {
                color: #fff;
                font-size: 16px;
                font-family: 'Microsoft YaHei';
              }
              .el-select {
                margin-right: 10px;
                background: #35373c;
                .el-select--large .el-select__wrapper {
                  background: #35373c;
                }
                ::v-deep span {
                  color: #fff;
                  font-family: 'Microsoft YaHei';
                  font-size: 17px;
                  font-weight: bold;
                }
              }
            }
            .se:nth-child(3),
            .se:nth-child(4) {
              margin-top: 20px;
            }
          }
          .box-list {
            width: 100%;
            // height: calc(100% - 134px);
            height: calc(100% - 104px);
            overflow-y: scroll;
            .box {
              height: 60px;
              width: 100%;
              margin-bottom: 30px;
              display: flex;
              flex-direction: column;
              span:nth-child(1) {
                font-size: 18px;
                color: #fff;
                font-family: 'Microsoft YaHei';
                margin-bottom: 17px;
              }
              .el-progress {
                width: 100%;
                height: 19px;
                :deep(.el-progress-bar__outer) {
                  background-color: #2b3542; //这里是背景颜色
                }
              }
            }
          }
        }
        ::-webkit-scrollbar-track {
          background-color: #0d0d16;
          border-radius: 20px;
        }

        /* 自定义滚动条宽度和高度 */
        ::-webkit-scrollbar {
          width: 0px;
          height: 0px;
        }
      }
    }
    .daily-box2 {
      height: 100%;
      width: 48%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      .Box1 {
        height: 420px;
        width: 100%;
        position: relative;
        .unit {
          position: absolute;
          top: 24px;
          right: 0;
          width: 70%;
          height: 42px;
          display: flex;
          align-items: center;
          .el-select {
            background: #35373c;
            .el-select--large .el-select__wrapper {
              background: #35373c;
            }
            ::v-deep span {
              color: #fff;
              font-family: 'Microsoft YaHei';
              font-size: 17px;
              font-weight: bold;
            }
          }
          .btn1 {
            cursor: pointer;
            width: 42px;
            height: 42px;
            background: url('@/assets/home/<USER>') no-repeat;
            background-size: 100% 100%;
            margin-left: 20px;
          }
          .btn2 {
            cursor: pointer;
            width: 42px;
            height: 42px;
            background: url('@/assets/home/<USER>') no-repeat;
            background-size: 100% 100%;
            margin-left: 10px;
          }
        }
        .FlowEcharts {
          flex: 1;
          width: 100%;
        }
      }
      .Box2 {
        height: 923px;
        width: 100%;
        position: relative;
        .content {
          flex: 1;
          overflow: hidden;
          .head {
            height: 118px;
            padding: 10px;
            width: 100%;
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            align-items: center;
            margin-top: 10px;
            background: #0e0f15;
            border-radius: 4px;
            .head-lin {
              color: #fff;
              display: flex;
              justify-content: space-around;
              align-items: center;
              width: 48%;
              background: #222730;
              border-radius: 4px;
              .img1 {
                width: 16px;
                height: 16px;
                background: url('@/assets/forewarning/qb.png') no-repeat center;
                background-size: 100% 100%;
              }
              .img2 {
                width: 16px;
                height: 16px;
                background: url('@/assets/forewarning/czz.png') no-repeat center;
                background-size: 100% 100%;
              }
              .img3 {
                width: 16px;
                height: 16px;
                background: url('@/assets/forewarning/ycl.png') no-repeat center;
                background-size: 100% 100%;
              }
              .img4 {
                width: 16px;
                height: 16px;
                background: url('@/assets/forewarning/wcl.png') no-repeat center;
                background-size: 100% 100%;
              }
              span:nth-child(1) {
                margin-left: 20px;
              }
              span:nth-child(2) {
                font-size: 22px;
                font-family: 'Microsoft YaHei';
                flex: 1;
                margin-left: 20px;
              }
              span:nth-child(3) {
                font-size: 17px;
                font-family: 'Microsoft YaHei';
                font-weight: bold;
                margin-right: 40px;
              }
            }
          }
          .list {
            overflow-y: scroll;
            width: 100%;
            height: calc(100% - 128px);
            .list-box {
              width: 100%;
              height: 150px;
              display: flex;
              align-items: center;
              background: #1a1e23;
              margin-bottom: 20px;
              .img {
                width: 211px;
                height: 125px;
                margin-left: 10px;
                img {
                  width: 100%;
                  height: 100%;
                }
              }
              .con {
                display: flex;
                height: 100%;
                flex: 1;
                flex-direction: column;
                justify-content: space-around;
                margin-left: 10px;
                span {
                  color: #fff;
                  display: inline-block;
                  font-size: 17px;
                  font-family: 'Microsoft YaHei';
                  i {
                    font-style: normal;
                    color: red;
                  }
                  .current0 {
                    color: red;
                  }
                  .current1 {
                    color: #00ff6d;
                  }
                  .current2 {
                    color: #efff00;
                  }
                }
              }
              .btn {
                width: 33px;
                height: 125px;
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                margin-right: 20px;
                > div {
                  width: 33px;
                  height: 33px;
                  border-radius: 4px;
                  cursor: pointer;
                }
                .btn1 {
                  background: #2a2b39 url('@/assets/forewarning/fxdj.png') no-repeat center;
                }
                .btn2 {
                  background: #2a2b39 url('@/assets/forewarning/dw.png') no-repeat center;
                }
                .btn3 {
                  background: #2a2b39 url('@/assets/forewarning/sxt.png') no-repeat center;
                }
              }
            }
          }
          /* 自定义滚动条宽度和高度 */
          ::-webkit-scrollbar {
            width: 0px;
            height: 0px;
          }
        }
      }
    }
  }
  .PopUp1 {
    width: 100%;
    height: 100%;
    .modal-content {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
