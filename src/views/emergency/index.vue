<template>
  <div>
    <div class="emergency">
      <div class="emergency-right">
        <div class="emergency-box1">
          <Box :title="'事件列表'" class="Box1">
            <template #content>
              <div class="name">
                <span>事件名称</span>
                <span>发生时间</span>
                <span>发生地点</span>
                <span>评分</span>
                <span>操作</span>
              </div>
              <div class="list">
                <div class="line" v-for="(item, index) in incidentList" :key="index">
                  <span class="line-span" :title="item.eventName">{{ item.eventName }}</span>
                  <span class="line-span" :title="item.happenTime">{{ item.happenTime }}</span>
                  <span class="line-span" :title="item.location">{{ item.location }}</span>
                  <span class="line-span">{{ item.totalScore }}</span>
                  <span class="line-span" @click="viewDelist(item)"
                    ><i
                      ><el-icon><Document /></el-icon>详情</i
                    ></span
                  >
                </div>
                <noData v-if="incidentList.length == 0"></noData>
              </div>
            </template>
          </Box>
          <Box :title="'险情感知'" class="Box2">
            <template #content>
              <div class="video">
                <div class="video-box" v-for="(item, index) in videoList" :key="index">
                  <div class="tit">{{ item.title }}</div>
                  <div class="vid"></div>
                </div>
              </div>
            </template>
          </Box>
        </div>
        <div class="emergency-box2">
          <Box :title="'通知详情'" class="Box1">
            <template #content>
              <div class="notificationEcharts" ref="notificationRef"></div>
            </template>
          </Box>
          <Box :title="'处置列表'" class="Box2">
            <template #content>
              <div class="list">
                <div class="tit">
                  <span class="img"></span>
                  <span>紧急事件：请尽快处理</span>
                </div>
                <div class="con">
                  <div class="line" v-for="(item, index) in disposeList" :key="index">
                    <span :title="item?.note">{{ item?.note }}</span>
                    <span>{{ item?.process_time }}</span>
                  </div>
                  <noData v-if="disposeList.length == 0"></noData>
                </div>
              </div>
            </template>
          </Box>
          <Box :title="'通讯录'" class="Box3">
            <template #content>
              <div class="content">
                <el-input
                  v-model="input"
                  size="large"
                  style="width: 100%; height: 52px"
                  class="custom-suffix-icon"
                  placeholder="请输入关键字"
                  :suffix-icon="Search"
                />
                <div class="con">
                  <div class="con-left">
                    <div
                      class="left-box"
                      v-for="(item, index) in jyList"
                      :key="index"
                      :class="currentIndex == index ? '' : 'current'"
                      @click="tabFc(index)"
                    >
                      <span></span>
                      <span>{{ item.name }}</span>
                    </div>
                  </div>
                  <div class="con-right">
                    <el-checkbox-group v-model="checkList" class="checkList">
                      <el-checkbox v-for="(item, index) in jyList[currentIndex].list" :key="index" size="large" :value="item.phone" class="checkBox">
                        <div class="label">{{ item.name }}</div>
                        <div class="btn1" @click="voiceFc(item.phone)"></div>
                        <div class="btn2" @click="dingFc(item.phone)"></div>
                      </el-checkbox>
                    </el-checkbox-group>
                    <div class="bot-btn">
                      <div class="btn" @click="voiceFc(null)">
                        <span></span>
                        <span>语音</span>
                      </div>
                      <div class="btn" @click="dingFc(null)">
                        <span></span>
                        <span>叮消息</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </template>
          </Box>
        </div>
      </div>
    </div>
    <PopUp :visible="showModal1" title="详情" @update:visible="showModal1 = $event" class="PopUp1">
      <div class="modal-content">
        <div class="list">
          <div class="line">
            <span
              >事件ID：<i>{{ warningList.id }}</i></span
            >
            <span
              >事件名称：<i>{{ warningList.eventName }}</i></span
            >
          </div>
          <div class="line">
            <span
              >发生时间：<i>{{ warningList.happenTime }}</i></span
            ><span
              >发生地点：<i>{{ warningList.location }}</i></span
            >
          </div>
          <div class="line">
            <span
              >桥梁名称：<i>{{ warningList.bridgeName }}</i></span
            >
            <span
              >经纬度：<i>{{ warningList.lon }},{{ warningList.lat }}</i></span
            >
          </div>
          <div class="line">
            <span class="line-con"
              >事故描述：<i>{{ warningList.description }}</i></span
            >
          </div>
        </div>
        <div class="video">
          <img class="video-box" v-if="warningList.fileUrl[0]" :src="warningList.fileUrl[0]" alt="" />
          <noData class="video-box" v-else text="暂无图片"></noData>
        </div>
      </div>
    </PopUp>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue';
import { Search } from '@element-plus/icons-vue';
import Box from '@/components/Box/index.vue';
import * as echarts from 'echarts';
import { getWarningStatistics } from '@/api/bridge/perception';
import { postNotify, getFlowList } from '@/api/bridge/emergency';
import PopUp from '@/components/PopUp/index.vue';
import noData from '@/components/noData/index.vue';

// 图表引用
const notificationRef = ref<HTMLElement | null>(null);

// 图表实例
let notificationEcharts: echarts.ECharts | null = null;

// 监听窗口大小变化，调整图表大小
const handleResize = () => {
  notificationEcharts?.resize();
};

//事件列表数据
const incidentList = ref([]);
const getWarningStatisticsFc = async () => {
  const res = await getWarningStatistics({});
  if (res.code == 200) {
    // console.log(res);
    incidentList.value = res.data?.warningList || [];
  }
};
//详情
const showModal1 = ref(false);
const warningList: any = ref([]);
const viewDelist = (data) => {
  showModal1.value = true;
  warningList.value = {
    ...data,
    fileUrl: JSON.parse(data.fileUrl)
  };
  // console.log(data);
};

//险情感知
const videoList = ref([
  {
    title: 'k301',
    url: ''
  },
  {
    title: 'k302',
    url: ''
  },
  {
    title: 'k303',
    url: ''
  },
  {
    title: 'k304',
    url: ''
  },
  {
    title: 'k305',
    url: ''
  },
  {
    title: 'k306',
    url: ''
  }
]);

//通知详情图表
const initNotificationEcharts = () => {
  if (notificationRef.value) {
    notificationEcharts = echarts.init(notificationRef.value);
    let data = [
      {
        name: '总计',
        value: 300
      },
      {
        name: '已读',
        value: 250
      },
      {
        name: '未读',
        value: 50
      }
    ];
    let arrName = getArrayValue(data, 'name');
    let objData = array2obj(data, 'name');
    let optionData = getData(data);
    function getArrayValue(array, key) {
      var key = key || 'value';
      var res = [];
      if (array) {
        array.forEach(function (t) {
          res.push(t[key]);
        });
      }
      return res;
    }

    function array2obj(array, key) {
      var resObj = {};
      for (var i = 0; i < array.length; i++) {
        resObj[array[i][key]] = array[i];
      }
      return resObj;
    }

    function getData(data) {
      var res = {
        series: [],
        yAxis: []
      };
      for (let i = 0; i < data.length; i++) {
        res.series.push({
          name: '',
          type: 'pie',
          clockWise: false, //顺时加载
          hoverAnimation: false, //鼠标移入变大
          radius: [73 - i * 15 + '%', 68 - i * 15 + '%'],
          center: ['30%', '55%'],
          label: {
            show: false
          },
          itemStyle: {
            label: {
              show: false
            },
            labelLine: {
              show: false
            },
            borderWidth: 5
          },
          data: [
            {
              value: data[i].value,
              name: data[i].name
            },
            {
              value: 400 - data[i].value,
              name: '',
              itemStyle: {
                color: 'rgba(0,0,0,0)',
                borderWidth: 0
              },
              tooltip: {
                show: false
              },
              hoverAnimation: false
            }
          ]
        });
        res.series.push({
          name: '',
          type: 'pie',
          silent: true,
          z: 1,
          clockWise: false, //顺时加载
          hoverAnimation: false, //鼠标移入变大
          radius: [73 - i * 10 + '%', 68 - i * 10 + '%'],
          center: ['30%', '55%'],
          label: {
            show: false
          },
          itemStyle: {
            label: {
              show: false
            },
            labelLine: {
              show: false
            },
            borderWidth: 4
          },
          data: [
            {
              value: 0,
              itemStyle: {
                color: 'rgb(0,0,0,0)',
                borderWidth: 0
              },
              tooltip: {
                show: false
              },
              hoverAnimation: false
            },
            {
              value: 0,
              name: '',
              itemStyle: {
                color: 'rgba(0,0,0,0)',
                borderWidth: 0
              },
              tooltip: {
                show: false
              },
              hoverAnimation: false
            },
            {
              value: 0,
              name: '',
              itemStyle: {
                color: 'rgba(0,0,0,0)',
                borderWidth: 0
              },
              tooltip: {
                show: false
              },
              hoverAnimation: false
            }
          ]
        });
        res.yAxis.push(data[i].name);
      }
      return res;
    }

    const option = {
      legend: {
        show: true,
        icon: 'none',
        top: 'center',
        left: '60%',
        data: arrName,
        width: 50,
        padding: [0, 5],
        itemGap: 30,
        formatter: function (name) {
          return '{title|' + name + '任务' + '}{value|' + objData[name].value + '}';
        },

        textStyle: {
          rich: {
            title: {
              fontSize: 30,
              color: '#8DCDFF '
            },
            value: {
              fontSize: 50,
              fontWeight: 'bold',
              color: '#8DCDFF '
            }
          }
        }
      },
      tooltip: {
        show: false,
        trigger: 'item',
        backgroundColor: '#23334e', // 背景色
        textStyle: {
          color: '#fff' // 字体颜色
        },
        formatter: '{a}<br>{b}:{c}({d}%)'
      },
      color: ['rgb(143, 206, 252)', 'rgb(119, 170, 205)', 'rgb(89, 128, 157)'],
      grid: {
        top: '16%',
        bottom: '62%',
        left: '30%',
        containLabel: false
      },
      yAxis: [
        {
          type: 'category',
          inverse: true,
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            interval: 0,
            inside: true,
            textStyle: {
              color: '#8DCDFF ',
              fontSize: 20
            },
            show: true
          },
          data: optionData.yAxis
        }
      ],
      xAxis: [
        {
          show: false
        }
      ],
      series: optionData.series
    };
    notificationEcharts.setOption(option);
  }
};

//处置列表
const disposeList = ref([]);
const getFlowListFc = async () => {
  const res = await getFlowList({});
  if (res.code == 200) {
    disposeList.value = res.data || [];
  }
};

//通讯录
const input = ref('');
const jyList = ref([
  {
    name: '救援一队',
    list: [
      {
        name: '孔愸暐',
        phone: '18317881394'
      },
      {
        name: '石忠信',
        phone: '17683267562'
      },
      {
        name: '张三',
        phone: '13123456789'
      },
      {
        name: '张三',
        phone: '13123456789'
      },
      {
        name: '张三',
        phone: '13123456789'
      },
      {
        name: '张三',
        phone: '13123456789'
      },
      {
        name: '张三',
        phone: '13123456789'
      },
      {
        name: '张三',
        phone: '13123456789'
      }
    ]
  },
  {
    name: '救援二队',
    list: [
      {
        name: '张三',
        phone: '13123456789'
      },
      {
        name: '张三',
        phone: '13123456789'
      },
      {
        name: '张三',
        phone: '13123456789'
      },
      {
        name: '张三',
        phone: '13123456789'
      },
      {
        name: '张三',
        phone: '13123456789'
      },
      {
        name: '张三',
        phone: '13123456789'
      },
      {
        name: '张三',
        phone: '13123456789'
      },
      {
        name: '张三',
        phone: '13123456789'
      }
    ]
  },
  {
    name: '救援三队',
    list: [
      {
        name: '张三',
        phone: '13123456789'
      },
      {
        name: '张三',
        phone: '13123456789'
      },
      {
        name: '张三',
        phone: '13123456789'
      },
      {
        name: '张三',
        phone: '13123456789'
      },
      {
        name: '张三',
        phone: '13123456789'
      },
      {
        name: '张三',
        phone: '13123456789'
      },
      {
        name: '张三',
        phone: '13123456789'
      },
      {
        name: '张三',
        phone: '13123456789'
      }
    ]
  }
]);
//当前救援小队
const currentIndex = ref(0);
const tabFc = (index) => {
  currentIndex.value = index;
};
//语音
const voiceFc = async (phone?: any) => {
  // phone
  const res = await postNotify({
    type: 'phone',
    phone: phone ? [phone] : checkList.value,
    warnId: '2'
  });
  if (res.code == 200) {
    ElMessage.success(res.msg);
  } else {
    ElMessage.error(res.msg);
  }
};
//叮消息
const dingFc = async (phone?: any) => {
  // sms
  const res = await postNotify({
    type: 'sms',
    phone: phone ? [phone] : checkList.value,
    warnId: '2'
  });
  if (res.code == 200) {
    ElMessage.success(res.msg);
  } else {
    ElMessage.error(res.msg);
  }
};
//选中数据
const checkList = ref([]);

onMounted(() => {
  initNotificationEcharts();
  getWarningStatisticsFc();
  getFlowListFc();
  window.addEventListener('resize', handleResize);
});

// 组件卸载前移除事件监听
onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize);
  // 销毁图表实例
  notificationEcharts?.dispose();
  notificationEcharts = null;
});
</script>
<style scoped lang="scss">
.emergency {
  .emergency-right {
    height: 1377px;
    width: 1442px;
    position: absolute;
    bottom: 44px;
    right: 60px;
    display: flex;
    justify-content: space-between;
    z-index: 1;
    .emergency-box1 {
      height: 100%;
      width: 48%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      .Box1 {
        height: 581px;
        width: 100%;
        position: relative;
        .name {
          background: #262c3f;
          width: 100%;
          height: 62px;
          display: flex;
          color: #fff;
          margin-top: 20px;
          span {
            font-size: 19px;
            font-weight: bold;
            font-family: 'Microsoft YaHei';
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            width: 20%;
          }
        }
        .list {
          width: 100%;
          height: calc(100% - 123px);
          margin-top: 10px;
          overflow-y: scroll;
          .line {
            display: flex;
            width: 100%;
            height: 62px;

            .line-span {
              color: #fff;
              font-size: 17px;
              font-family: 'Microsoft YaHei';
              height: 100%;
              width: 20%;
              line-height: 62px;
              text-align: center;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              padding: 0 5px;
              box-sizing: border-box;
              i {
                width: 68px;
                height: 29px;
                display: inline-block;
                display: flex;
                justify-content: center;
                align-items: center;
                font-style: normal;
                background: #2e4b79;
                cursor: pointer;
              }
            }
          }
        }
        /* 隐藏垂直滚动条 */
        .list::-webkit-scrollbar {
          width: 0;
        }
      }
      .Box2 {
        height: 761px;
        width: 100%;
        position: relative;
        .video {
          width: 100%;
          height: 100%;
          display: flex;
          flex-wrap: wrap;
          justify-content: space-between;
          overflow-y: scroll;
          margin-top: 30px;
          /* 隐藏垂直滚动条 */
          &::-webkit-scrollbar {
            width: 0;
          }
          .video-box {
            width: 48%;
            .tit {
              font-size: 20px;
              color: #fff;
              font-family: 'Microsoft YaHei';
              font-weight: bold;
            }
            .vid {
              width: 100%;
              height: 164px;
              background: url('@/assets/emergency/videoBG.png') no-repeat center;
              background-size: 100%;
            }
          }
        }
      }
    }
    .emergency-box2 {
      height: 100%;
      width: 48%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      .Box1 {
        height: 476px;
        width: 100%;
        position: relative;
        .notificationEcharts {
          flex: 1;
          width: 100%;
        }
      }
      .Box2 {
        height: 356px;
        width: 100%;
        position: relative;
        .list {
          width: 100%;
          height: 100%;
          position: relative;
          padding-top: 10px;
          overflow: hidden;

          .tit {
            width: 100%;
            height: 46px;
            padding: 9px 32px;
            display: flex;
            background: rgba(254, 75, 89, 0.24);
            .img {
              display: inline-block;
              width: 29px;
              height: 29px;
              background: url('@/assets/secialVehicle/bjdj1.png') no-repeat center;
              background-size: 100%;
            }
            span {
              font-size: 22px;
              color: #ff4343;
              font-family: 'Microsoft YaHei';
            }
          }
          .con {
            width: 100%;
            height: calc(100% - 46px);
            overflow-y: scroll;
            /* 隐藏垂直滚动条 */
            &::-webkit-scrollbar {
              width: 0;
            }
            .line {
              width: 100%;
              height: 46px;
              padding: 9px 32px;
              background: #20252e;
              margin-top: 18px;
              display: flex;
              justify-content: space-between;
              span {
                font-size: 20px;
                color: #fff;
                font-family: 'Microsoft YaHei';
              }
              span:nth-child(1) {
                display: inline-block;
                flex: 1;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }
            }
          }
        }
      }
      .Box3 {
        height: 476px;
        width: 100%;
        position: relative;
        .content {
          width: 100%;
          height: 100%;
          position: relative;
          padding-top: 30px;
          .custom-suffix-icon {
            :deep(.el-input__suffix) {
              font-size: 25px;
            }
            :deep(.el-input__wrapper) {
              background: #474750;
              color: #fff;
            }
            :deep(.el-input__inner) {
              color: #fff;
              font-size: 25px;
            }
          }
          .con {
            width: 100%;
            height: calc(100% - 52px);
            position: relative;
            display: flex;
            padding-top: 12px;
            justify-content: space-between;
            .con-left {
              width: 94px;
              height: 100%;
              display: flex;
              flex-direction: column;
              overflow-y: scroll;
              /* 隐藏垂直滚动条 */
              &::-webkit-scrollbar {
                width: 0;
              }
              .left-box {
                display: flex;
                flex-direction: column;
                align-items: center;
                margin: 20px 0px;
                cursor: pointer;
                span:nth-child(1) {
                  display: inline-block;
                  background: url('@/assets/emergency/jyxd.png') no-repeat center;
                  background-size: 100%;
                  width: 50px;
                  height: 36px;
                }
                span:nth-child(2) {
                  font-size: 16px;
                  color: #fff;
                  font-family: 'Microsoft YaHei';
                }
              }
              .current {
                opacity: 0.5;
              }
            }
            .con-right {
              width: calc(100% - 104px);
              margin-left: 10px;
              height: 100%;
              padding: 36px 40px 24px 40px;
              .checkList {
                display: flex;
                flex-wrap: wrap;
                height: 186px;
                width: 100%;
                overflow-y: scroll;
                /* 隐藏垂直滚动条 */
                &::-webkit-scrollbar {
                  width: 0;
                }
                .checkBox {
                  width: 200px;
                  .el-checkbox__input {
                    :deep(.el-checkbox__inner) {
                      width: 22px !important;
                      height: 22px !important;
                    }
                  }
                  :deep(.el-checkbox__label) {
                    display: inline-block;
                    width: 80% !important;
                    display: flex;

                    .label {
                      color: #fff;
                      font-size: 20px;
                      width: 60px;
                      font-family: 'Microsoft YaHei';
                    }
                    .btn1 {
                      width: 32px;
                      height: 22px;
                      background: url('@/assets/emergency/phone.png') no-repeat center;
                      background-size: 100%;
                      cursor: pointer;
                      margin-left: 20px;
                    }
                    .btn2 {
                      background: url('@/assets/emergency/dxx.png') no-repeat center;
                      background-size: 100%;
                      width: 32px;
                      height: 22px;
                      cursor: pointer;
                      margin-left: 10px;
                    }
                  }
                }
              }
              .bot-btn {
                width: 100%;
                height: 32px;
                display: flex;
                padding-right: 80px;
                justify-content: space-between;
                margin-top: 30px;
                .btn {
                  width: 46%;
                  height: 100%;
                  background: #304c7a;
                  display: flex;
                  justify-content: center;
                  align-items: center;
                  cursor: pointer;

                  span {
                    font-size: 18px;
                    color: #fff;
                    font-family: 'Microsoft YaHei';
                  }
                }
                .btn:nth-child(1) {
                  span:nth-child(1) {
                    background: url('@/assets/emergency/phone.png') no-repeat center;
                    display: inline-block;
                    width: 22px;
                    height: 22px;
                  }
                }
                .btn:nth-child(2) {
                  span:nth-child(1) {
                    background: url('@/assets/emergency/dxx.png') no-repeat center;
                    display: inline-block;
                    width: 22px;
                    height: 22px;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
.PopUp1 {
  .modal-content {
    width: 100%;
    display: flex;
    flex-direction: column;
    padding: 20px;
    .list {
      width: 100%;
      height: 310px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      .line {
        width: 100%;
        height: 40px;
        span {
          display: inline-block;
          width: 50%;
          height: 100%;
          color: #fff;
          font-size: 30px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          font-family: 'Microsoft YaHei';
          i {
            font-style: normal;
            color: #accbff;
          }
        }
        .line-con {
          width: 100%;
          white-space: wrap;
        }
      }
    }
    .video {
      width: 100%;
      height: 258px;
      display: flex;
      justify-content: space-between;
      margin: 64px 0 0px 0;
      .video-box {
        width: 48%;
        height: 100%;
        // background: url('@/assets/secialVehicle/videoBg.png') no-repeat center;
        // background-size: 100% 100%;
      }
    }
  }
}
</style>
