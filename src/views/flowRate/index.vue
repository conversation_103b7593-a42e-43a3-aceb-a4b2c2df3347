<template>
  <div class="flowRate">
    <div class="flow-right">
      <div class="flow-box1">
        <Box :title="'历史流量'" class="Box1">
          <template #unit>
            <div class="unit">
              <el-select v-model="value5" placeholder="Select" @change="flowTime" size="large" style="width: 92px">
                <el-option v-for="item in options5" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
              <!-- <el-select v-model="value" placeholder="Select" size="large" style="width: 120px">
                <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
              </el-select> -->
            </div>
          </template>
          <template #content>
            <div class="historyFlowEcharts" ref="historyFlowRef"></div>
          </template>
        </Box>
        <Box :title="'压力分布'" class="Box2">
          <template #content>
            <div class="stress">
              <div class="box" v-for="(item, index) in stressList" :key="index">
                <span>{{ item.name }}</span>
                <span>{{ item.content }}</span>
              </div>
            </div>
          </template>
        </Box>
      </div>
      <div class="flow-box2">
        <Box :title="'拥堵指数'" class="Box1">
          <template #content>
            <div class="jamEchats" ref="jamRef"></div>
            <div class="list">
              <div class="tit">
                <span>总车次</span>
                <span>{{ carTotalValue }}</span>
                <span>辆</span>
              </div>
              <div class="list-box">
                <span class="j"></span>
                <span>进</span>
                <span>{{ carInValue }}</span>
                <span>辆</span>
              </div>
              <div class="list-box">
                <span class="c"></span>
                <span>出</span>
                <span>{{ carOutValue }}</span>
                <span>辆</span>
              </div>
            </div>
          </template>
        </Box>
        <Box :title="'任务情况'" class="Box2">
          <template v-slot:content>
            <div class="con-1">
              <div class="img"></div>
              <div class="list list1">
                <span>任务总数</span>
                <span
                  ><i>{{ totalCount }}</i
                  >件</span
                >
              </div>
            </div>
            <div class="bg"></div>
            <div class="con-1 con-2">
              <div class="con">
                <div class="img1"></div>
                <div class="list">
                  <span>未启动</span>
                  <span
                    ><i>{{ statusCount[0]?.count || 0 }}</i
                    >件</span
                  >
                </div>
              </div>
              <div class="con">
                <div class="img2"></div>
                <div class="list">
                  <span>进行中</span>
                  <span
                    ><i>{{ statusCount[1]?.count || 0 }}</i
                    >件</span
                  >
                </div>
              </div>
              <div class="con">
                <div class="img4"></div>
                <div class="list">
                  <span>已关闭</span>
                  <span
                    ><i>{{ statusCount[2]?.count || 0 }}</i
                    >件</span
                  >
                </div>
              </div>
            </div>
            <div class="line">
              <div class="line-box">
                <div class="se">
                  <span>发生时间</span>
                  <el-select v-model="value1" placeholder="Select" @change="updateTime" size="large" style="width: 173px; height: 32px">
                    <el-option v-for="item in options1" :key="item.value" :label="item.label" :value="item.value" />
                  </el-select>
                </div>
                <div class="se">
                  <span>预警类型</span>
                  <el-select v-model="value2" placeholder="Select" @change="updateType" size="large" style="width: 173px; height: 32px">
                    <el-option v-for="item in options2" :key="item.value" :label="item.label" :value="item.value" />
                  </el-select>
                </div>
                <!-- <div class="se">
                  <span>预警等级</span>
                  <el-select v-model="value3" placeholder="Select" size="large" style="width: 173px; height: 32px">
                    <el-option v-for="item in options3" :key="item.value" :label="item.label" :value="item.value" />
                  </el-select>
                </div>
                <div class="se">
                  <span>处置时间</span>
                  <el-select v-model="value4" placeholder="Select" size="large" style="width: 173px; height: 32px">
                    <el-option v-for="item in options4" :key="item.value" :label="item.label" :value="item.value" />
                  </el-select>
                </div> -->
              </div>
              <div class="box-list">
                <div class="box" v-for="item in customColors" :key="item.name">
                  <span> {{ item.name }}：</span>
                  <el-progress :text-inside="true" :stroke-width="19" :percentage="item.percentage" />
                </div>
                <noData v-if="customColors.length == 0" />
              </div>
            </div>
          </template>
        </Box>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue';
import Box from '@/components/Box/index.vue';
import * as echarts from 'echarts';
import ydzsImg from '@/assets/flowRate/ydzs.png';
import { getStatistics } from '@/api/bridge/dailyWork';
import { getHubFlowStats, getDirectionFlowStats } from '@/api/bridge/trafficFlow';
import noData from '@/components/noData/index.vue';

// 图表引用
const historyFlowRef = ref<HTMLElement | null>(null);
const jamRef = ref<HTMLElement | null>(null);

// 图表实例
let historyFlowEcharts: echarts.ECharts | null = null;
let jamEcharts: echarts.ECharts | null = null;

// 监听窗口大小变化，调整图表大小
const handleResize = () => {
  historyFlowEcharts?.resize();
  jamEcharts?.resize();
};

//历史流量
const value5 = ref('3');
const options5 = [
  {
    value: '1',
    label: '日'
  },
  {
    value: '2',
    label: '月'
  },
  {
    value: '3',
    label: '年'
  }
];
//历史流量时间
const flowTime = (val) => {
  initHistoryEcharts({ time: val });
};
const value = ref('节假日');
const options = [
  {
    value: 'Option1',
    label: '节假日'
  },
  {
    value: 'Option2',
    label: 'Option2'
  },
  {
    value: 'Option3',
    label: 'Option3'
  }
];
const initHistoryEcharts = async (data?: {}) => {
  // 使用新的历史流量接口
  const hubRes = await getHubFlowStats();
  let xData = [];
  let yData = [];

  if (hubRes.code === 200 && hubRes.data.length > 0) {
    // 新接口返回的是断面流量统计数据
    hubRes.data.forEach((item: any) => {
      xData.push(item.hubCode01);
      yData.push(item.totalVehicleCls);
    });
  }
  if (historyFlowRef.value) {
    historyFlowEcharts = echarts.init(historyFlowRef.value);
    const option = {
      barWidth: 15,
      grid: {
        top: 40,
        right: 40,
        bottom: 0,
        left: 0,
        containLabel: true,
        show: true,
        borderWidth: 0 // 不显示外边框
      },
      xAxis: {
        type: 'value',
        splitLine: {
          lineStyle: {
            color: 'rgba(255,255,255,0.2)'
          }
        },
        axisTick: {
          show: false
        },
        axisLine: {
          //  改变x轴颜色
          lineStyle: {
            color: 'rgba(108, 202, 248,0.4)'
          }
        },
        axisLabel: {
          //  改变x轴字体颜色和大小
          textStyle: {
            color: '#fff',
            fontSize: 16
          }
        }
      },
      yAxis: {
        type: 'category',
        data: xData ? xData : ['鲁家峙大桥', '新城大桥', '响礁门大桥', '岑港大桥', '桃夭门大桥', '西堠门大桥'],
        splitLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisLine: {
          //  改变y轴颜色
          lineStyle: {
            color: 'rgba(108, 202, 248,0.4)'
          }
        },
        axisLabel: {
          //  改变y轴字体颜色和大小
          // formatter: '{value} m³ ', //  给y轴添加单位
          textStyle: {
            color: '#fff',
            fontSize: 18
          }
        }
      },
      series: [
        {
          type: 'bar',
          name: '',
          itemStyle: {
            normal: {
              label: {
                show: true, //开启显示
                position: 'right', //在上方显示
                textStyle: {
                  //数值样式
                  color: '#fff',
                  fontSize: 20
                  // fontWeight: 600
                }
              },
              color: new echarts.graphic.LinearGradient(1, 0, 0, 0, [
                {
                  offset: 0,
                  color: 'rgba(108, 202, 248,1)'
                },
                {
                  offset: 1,
                  color: 'rgba(108, 202, 248,0)'
                }
              ]),
              borderWidth: 2,
              barBorderRadius: 15
            }
          },
          data: yData ? yData : [1266, 1500, 2336, 2736, 2435, 3398]
        }
      ]
    };
    historyFlowEcharts.setOption(option);
  }
};

//压力分布
const stressList = ref([
  {
    'name': '金塘大桥',
    'content': 'K360-K380',
    'state': '正常'
  },
  {
    'name': '西喉门大桥',
    'content': '',
    'state': '正常'
  },
  {
    'name': '岑港大桥',
    'content': '',
    'state': '正常'
  },
  {
    'name': '桃夭门大桥',
    'content': '',
    'state': '维护中'
  },
  {
    'name': '新城大桥',
    'content': '',
    'state': '正常'
  }
]);

const carInValue = ref(0);
const carOutValue = ref(0);
const carTotalValue = ref(0);
//拥堵指数 - 使用新的方向流量统计接口
const initJamEcharts = async () => {
  let inValue = 0; // 进的数据，从接口获取
  let outValue = 0; // 出的数据，从接口获取
  let totalValue = 0;
  try {
    // 获取方向流量统计数据
    const res = await getDirectionFlowStats();
    if (res.code === 200) {
      const data = res.data;
      inValue = data.舟向; // 宁向作为进
      outValue = data.非舟向; // 舟向作为出
      totalValue = data.totalCount;
      carInValue.value = inValue;
      carOutValue.value = outValue;
      carTotalValue.value = totalValue;
    } else {
      console.error('方向流量统计接口返回错误:', res.msg);
      return; // 如果接口失败，不渲染图表
    }
  } catch (error) {
    console.error('获取方向流量数据失败:', error);
    return; // 如果接口失败，不渲染图表
  }

  if (jamRef.value) {
    jamEcharts = echarts.init(jamRef.value);
    const color = ['#71bbf1', '#8e2ef8', '#f474bd', '#bf33f8'];
    const dataStyle = {
      normal: {
        label: {
          show: false
        },
        labelLine: {
          show: false
        },
        shadowBlur: 40,
        borderWidth: 10,
        shadowColor: 'rgba(0, 0, 0, 0)' //边框阴影
      }
    };
    const placeHolderStyle = {
      normal: {
        color: '#292c46',
        label: {
          show: false
        },
        labelLine: {
          show: false
        }
      },
      emphasis: {
        color: '#393d50'
      }
    };
    const option = {
      graphic: [
        {
          type: 'image',
          style: {
            image: ydzsImg, // 图片URL
            width: 169,
            height: 169
          },
          left: '17%',
          top: 'center',
          z: 10
        }
      ],
      tooltip: {
        trigger: 'item',
        show: true,
        formatter: '{b} : <br/>{d}%',
        backgroundColor: '#23334e', // 背景色
        textStyle: {
          color: '#fff' // 字体颜色
        }
      },
      // legend: {
      //     orient: 'vertical',
      //     // icon: 'circle',
      //     left: 'left',
      //     top: '20',
      //     itemGap:20,
      //     data: ['二级匹配度', '三级匹配度'],
      //     textStyle: {
      //         color: '#fft'
      //     }
      // },
      series: [
        {
          name: 'Line 1',
          type: 'pie',
          clockWise: false,
          radius: [146, 160],
          center: ['30%', '50%'],
          itemStyle: dataStyle,
          hoverAnimation: false,
          startAngle: 90,
          label: {
            borderRadius: '10'
          },
          data: [
            {
              value: inValue,
              name: '进',
              itemStyle: {
                normal: {
                  color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                      offset: 0,
                      color: color[0]
                    },
                    {
                      offset: 1,
                      color: color[1]
                    }
                  ])
                }
              }
            },
            {
              value: Math.max(0, totalValue - inValue), // 占位符，确保圆环完整
              name: '',
              tooltip: {
                show: false
              },
              itemStyle: placeHolderStyle
            }
          ]
        },
        {
          name: 'Line 2',
          type: 'pie',
          clockWise: false,
          radius: [106, 120],
          center: ['30%', '50%'],
          itemStyle: dataStyle,
          hoverAnimation: false,
          startAngle: 90,
          data: [
            {
              value: outValue,
              name: '出',
              itemStyle: {
                normal: {
                  color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                      offset: 0,
                      color: color[2]
                    },
                    {
                      offset: 1,
                      color: color[3]
                    }
                  ])
                }
              }
            },
            {
              value: Math.max(0, totalValue - outValue), // 占位符，确保圆环完整
              name: '',
              tooltip: {
                show: false
              },
              itemStyle: placeHolderStyle
            }
          ]
        }
      ]
    };
    jamEcharts.setOption(option);
  }
};

//任务情况
const value1 = ref('3');
const options1 = [
  {
    value: '1',
    label: '一天'
  },
  {
    value: '2',
    label: '一个月'
  },
  {
    value: '3',
    label: '一年'
  }
];

const value2 = ref('All');
const options2 = [
  {
    value: 'All',
    label: '全部'
  },
  {
    value: '1',
    label: '自然灾害'
  },
  {
    value: '2',
    label: '事故灾难'
  },
  {
    value: '3',
    label: '公共卫生事件'
  },
  {
    value: '4',
    label: '社会安全事件'
  }
];

const value3 = ref('全部');
const options3 = [
  {
    value: 'Option1',
    label: '全部'
  },
  {
    value: 'Option2',
    label: 'Option2'
  }
];

const value4 = ref('全部');
const options4 = [
  {
    value: 'Option1',
    label: '全部'
  },
  {
    value: 'Option2',
    label: 'Option2'
  }
];
//时间筛选
const updateTime = (value) => {
  // console.log(value);
  value1.value = value;
  getStatisticsFc({
    eventCategory: value2.value,
    happenTime: value1.value
  });
};
//类型筛选
const updateType = (value) => {
  // console.log(value);
  value2.value = value;
  getStatisticsFc({
    eventCategory: value2.value,
    happenTime: value1.value
  });
};
//任务情况接口
const totalCount = ref('0');
const statusCount = ref([]);
const getStatisticsFc = async (params?: {}) => {
  const res = await getStatistics(params);
  if (res.code == 200) {
    // console.log(res);
    totalCount.value = res.data.totalCount || '0';
    statusCount.value = res.data.statusCount || [];
    customColors.value = res.data.departmentCount || [];
  }
};

const customColors = ref([
  { percentage: 56, count: 56, name: '养护管理处' },
  { percentage: 47, count: 47, name: '安全监督处' },
  { percentage: 62, count: 62, name: '舟山交通局' },
  { percentage: 25, count: 25, name: '舟山消防' }
]);

onMounted(() => {
  initHistoryEcharts({ time: value5.value });
  initJamEcharts();
  getStatisticsFc({
    eventCategory: value2.value,
    happenTime: value1.value
  });
  window.addEventListener('resize', handleResize);
});

// 组件卸载前移除事件监听
onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize);
  // 销毁图表实例
  historyFlowEcharts?.dispose();
  historyFlowEcharts = null;
  jamEcharts?.dispose();
  jamEcharts = null;
});
</script>
<style scoped lang="scss">
.flowRate {
  .flow-right {
    height: 1377px;
    width: 1442px;
    position: absolute;
    bottom: 44px;
    right: 60px;
    display: flex;
    justify-content: space-between;
    z-index: 1;
    .flow-box1 {
      height: 100%;
      width: 48%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      .Box1 {
        height: 798px;
        width: 100%;
        position: relative;
        .unit {
          margin-left: 20px;
          height: 42px;
          display: flex;
          align-items: center;
          .el-select {
            margin-right: 10px;
            background: #35373c;
            .el-select--large .el-select__wrapper {
              background: #35373c;
            }
            ::v-deep span {
              color: #fff;
              font-family: 'Microsoft YaHei';
              font-size: var(--font-size-content); // 使用统一变量 25px
              font-weight: bold;
            }
          }
        }
        .historyFlowEcharts {
          width: 100%;
          height: 100%;
        }
      }
      .Box2 {
        height: 544px;
        width: 100%;
        position: relative;
        .stress {
          width: 100%;
          height: 100%;
          position: relative;
          .box {
            position: absolute;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            span {
              color: #fff;
              font-family: 'Microsoft YaHei';
            }
            span:nth-child(1) {
              font-weight: bold;
              font-size: 48px; // 保持大数据显示
            }
            span:nth-child(2) {
              font-size: var(--font-size-data-medium); // 使用统一变量 26px
            }
          }
          .box:nth-child(1) {
            width: 389px;
            height: 389px;
            left: 50%;
            transform: translateX(-50%);
            top: 35px;
            background: url('@/assets/flowRate/h.png') no-repeat center;
            background-size: 100%;
          }
          .box:nth-child(2) {
            width: 248px;
            height: 248px;
            left: 0;
            top: 0;
            background: url('@/assets/flowRate/hc.png') no-repeat center;
            background-size: 100%;
            span:nth-child(1) {
              font-weight: bold;
              font-size: var(--font-size-data-large); // 使用统一变量 30px
            }
          }
          .box:nth-child(3) {
            width: 248px;
            height: 248px;
            right: 0;
            top: 0;
            background: url('@/assets/flowRate/jh.png') no-repeat center;
            background-size: 100%;
            span:nth-child(1) {
              font-weight: bold;
              font-size: var(--font-size-data-large); // 使用统一变量 30px
            }
          }
          .box:nth-child(4) {
            width: 248px;
            height: 248px;
            left: 0;
            bottom: 0;
            background: url('@/assets/flowRate/lv.png') no-repeat center;
            background-size: 100%;
            span:nth-child(1) {
              font-weight: bold;
              font-size: var(--font-size-data-large); // 使用统一变量 30px
            }
          }
          .box:nth-child(5) {
            width: 248px;
            height: 248px;
            right: 0;
            bottom: 0;
            background: url('@/assets/flowRate/sl.png') no-repeat center;
            background-size: 100%;
            span:nth-child(1) {
              font-weight: bold;
              font-size: var(--font-size-data-large); // 使用统一变量 30px
            }
          }
        }
      }
    }
    .flow-box2 {
      height: 100%;
      width: 48%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      .Box1 {
        width: 100%;
        height: 476px;
        position: relative;
        .jamEchats {
          width: 100%;
          height: 100%;
        }
        .list {
          position: absolute;
          right: 35px;
          top: 50%;
          transform: translateY(-50%);
          .tit {
            span {
              color: #fff;
              font-family: 'Microsoft YaHei';
            }
            span:nth-child(1) {
              font-weight: bold;
              font-size: var(--font-size-data-large); // 使用统一变量 30px
            }
            span:nth-child(2) {
              font-weight: bold;
              font-size: 40px;
              margin: 0 14px 0 19px;
            }
            span:nth-child(3) {
              font-size: 22px;
            }
          }
          .list-box {
            margin-top: 20px;
            span {
              color: #fff;
              font-family: 'Microsoft YaHei';
            }
            span:nth-child(1) {
              display: inline-block;
              background-size: 100%;
              width: 20px;
              height: 20px;
              margin-left: 18px;
            }
            span:nth-child(2) {
              font-size: var(--font-size-data-medium); // 使用统一变量 26px
              margin-left: 26px;
            }
            span:nth-child(3) {
              font-size: 32px;
              font-weight: bold;
              margin: 0 24px 0 29px;
            }
            span:nth-child(4) {
              font-size: 22px;
            }
            .j {
              background: url('@/assets/flowRate/j.png') no-repeat center;
            }
            .c {
              background: url('@/assets/flowRate/c.png') no-repeat center;
            }
          }
        }
      }
      .Box2 {
        width: 100%;
        height: 866px;
        position: relative;
        .bg {
          position: absolute;
          width: 203px;
          height: 254px;
          top: 60px;
          right: 37px;
          background: url('@/assets/dailyWork/rwqkBg.png') no-repeat;
        }
        .con-1 {
          width: 100%;
          height: 140px;
          margin-top: 35px;
          padding-left: 20px;
          display: flex;
          align-items: center;
          background: #232531;
          .img {
            width: 100px;
            height: 100px;
            background: url('@/assets/dailyWork/rwzs.png') no-repeat;
            background-size: 100% 100%;
          }
          .img1 {
            width: 66px;
            height: 66px;
            background: url('@/assets/dailyWork/zcczz.png') no-repeat;
            background-size: 100% 100%;
          }
          .img2 {
            width: 66px;
            height: 66px;
            background: url('@/assets/dailyWork/yq.png') no-repeat;
            background-size: 100% 100%;
          }
          .img4 {
            width: 66px;
            height: 66px;
            background: url('@/assets/dailyWork/ygb.png') no-repeat;
            background-size: 100% 100%;
          }
          .list {
            color: #fff;
            display: flex;
            flex-direction: column;
            font-family: 'Microsoft YaHei';
            margin-left: 5px;
            span:nth-child(1) {
              font-size: var(--font-size-content); // 使用统一变量 25px
              margin-bottom: 10px;
            }
            span:nth-child(2) {
              font-size: 21px;
              i {
                font-style: normal;
                font-size: 35px;
                font-weight: bold;
                margin-right: 10px;
              }
            }
          }
          .list1 {
            display: flex;
            flex-direction: row;
            align-items: center;
            span:nth-child(1) {
              margin-bottom: 0px;
            }
            span:nth-child(2) {
              i {
                margin: 0px 17px;
              }
            }
          }
          .con {
            width: 25%;
            height: 100%;
            display: flex;
            align-items: center;
          }
        }
        .con-2 {
          justify-content: space-between;
        }
        .line {
          flex: 1;
          margin-top: 20px;
          padding: 20px;
          background: #232531;
          overflow: hidden;
          .line-box {
            width: 100%;
            height: 84px;
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            margin-bottom: 30px;
            .se {
              width: 46%;
              height: 32px;
              display: flex;
              justify-content: space-between;
              align-items: center;
              span {
                color: #fff;
                font-size: var(--font-size-content); // 使用统一变量 25px
                font-family: 'Microsoft YaHei';
              }
              .el-select {
                margin-right: 10px;
                background: #35373c;
                .el-select--large .el-select__wrapper {
                  background: #35373c;
                }
                ::v-deep span {
                  color: #fff;
                  font-family: 'Microsoft YaHei';
                  font-size: var(--font-size-content); // 使用统一变量 25px
                  font-weight: bold;
                }
              }
            }
            .se:nth-child(3),
            .se:nth-child(4) {
              margin-top: 20px;
            }
          }
          .box-list {
            width: 100%;
            height: calc(100% - 134px);
            overflow-y: scroll;
            .box {
              height: 60px;
              width: 100%;
              margin-bottom: 30px;
              display: flex;
              flex-direction: column;
              span:nth-child(1) {
                font-size: var(--font-size-content); // 使用统一变量 25px
                color: #fff;
                font-family: 'Microsoft YaHei';
                margin-bottom: 17px;
              }
              .el-progress {
                width: 100%;
                height: 19px;
                :deep(.el-progress-bar__outer) {
                  background-color: #2b3542; //这里是背景颜色
                }
              }
            }
          }
        }
        ::-webkit-scrollbar-track {
          background-color: #0d0d16;
          border-radius: 20px;
        }

        /* 自定义滚动条宽度和高度 */
        ::-webkit-scrollbar {
          width: 0px;
          height: 0px;
        }
      }
    }
  }
}
</style>
