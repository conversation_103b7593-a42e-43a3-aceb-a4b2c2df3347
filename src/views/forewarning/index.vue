<template>
  <div class="forewarning">
    <div class="for-left">
      <Box :title="'预警类型'" class="Box1 interactive-card">
        <template v-slot:content>
          <div class="content">
            <div class="mid-box interactive-button" @click="warningType('All')">
              <span>{{ countByTitle.total }}</span>
              <span>总预警</span>
            </div>
            <div v-for="(item, index) in countByTitle.details" :key="index" :class="[`box${index + 1}`, 'interactive-button']" @click="warningType(item)">
              <span>{{ item.count }}</span>
              <span>{{ item.title }}</span>
            </div>
          </div>
        </template>
      </Box>
      <Box :title="'安全指数'" class="Box2 interactive-card">
        <template v-slot:content>
          <div class="content">
            <div class="head">
              <div class="head-box">
                <span>极低风险</span>
                <span>Dr < 1.5</span>
              </div>
              <div class="head-box">
                <span>低风险</span>
                <span>1.5 ≤ Dr < 2.5</span>
              </div>
              <div class="head-box">
                <span>中风险</span>
                <span>2.5 ≤ Dr < 3.5</span>
              </div>
              <div class="head-box">
                <span>高风险</span>
                <span>Dr ≥ 3.5</span>
              </div>
            </div>
            <div class="bot" :class="riskFc(1.2)">
              <div class="bot-tit">
                <span>当前值</span>
                <span>1.2</span>
              </div>
              <div class="bot-list1">极低风险</div>
              <div class="bot-list2">低风险</div>
              <div class="bot-list3">中风险</div>
              <div class="bot-list4">高风险</div>
            </div>
          </div>
        </template>
      </Box>
    </div>
    <div class="for-right">
      <Box :title="'预警分时'" class="Box1">
        <template v-slot:unit>
          <div class="unit">
            <el-select v-model="value3" placeholder="Select" @change="updateTime3" size="large" style="width: 72px; margin-left: 10px">
              <el-option v-for="item in options3" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </div>
        </template>
        <template v-slot:content>
          <div class="statistics1" ref="statisticsRef"></div>
        </template>
      </Box>
      <Box :title="'预警统计'" class="Box2">
        <template v-slot:unit>
          <div class="unit">
            <!-- <el-dropdown trigger="click">
              <el-button type="primary">
                位置<el-icon class="el-icon--right"><arrow-down /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item>Action 1</el-dropdown-item>
                  <el-dropdown-item>Action 2</el-dropdown-item>
                  <el-dropdown-item>Action 3</el-dropdown-item>
                  <el-dropdown-item>Action 4</el-dropdown-item>
                  <el-dropdown-item>Action 5</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown> -->
            <el-select v-model="value" placeholder="Select" @change="updateData" size="large" style="width: 180px">
              <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </div>
        </template>
        <template v-slot:content>
          <div class="line">
            <div class="box" v-for="(item, index) in customColors" :key="index">
              <span>{{ index + 1 }}</span>
              <span> {{ item?.location }}</span>
              <el-progress :percentage="item.percentage" color="#1989fa" :show-text="false" />
              <span
                ><i>{{ item?.count }}</i></span
              >
            </div>
            <noData v-if="customColors.length == 0"></noData>
          </div>
        </template>
      </Box>
      <Box :title="'预警列表'" class="Box3" :onTitleClick="clickListFc">
        <!-- <template v-slot:unit>
          <div class="unit">
            <div class="btn"></div>
          </div>
        </template> -->
        <template v-slot:content>
          <div class="content">
            <div class="head">
              <div class="head-lin">
                <span class="img1"></span>
                <span>全部</span>
                <span>{{ incidentData.totalCount || 0 }}</span>
              </div>
              <div class="head-lin">
                <span class="img4"></span>
                <span>未处理</span>
                <span>{{ incidentData.statusStats[1] || 0 }}</span>
              </div>
              <div class="head-lin">
                <span class="img2"></span>
                <span>处理中</span>
                <span>{{ incidentData.statusStats[2] || 0 }}</span>
              </div>
              <div class="head-lin">
                <span class="img3"></span>
                <span>已处理</span>
                <span>{{ incidentData.statusStats[3] || 0 }}</span>
              </div>
            </div>
            <div class="list">
              <div class="list-box" v-for="(item, index) in incidentData.events" :key="index">
                <div class="img">
                  <img v-if="item.fileUrl[0]" :src="item.fileUrl[0]" alt="" />
                  <noData v-else text="暂无图片"></noData>
                </div>
                <div class="con">
                  <span>发生时间：{{ item.publishTime }}</span>
                  <span>发生地点：{{ item.location }}</span>
                  <span>预警事件：{{ item.title }}</span>
                  <span
                    >状态：<i :class="warningState(item.status)">{{
                      item?.status == '1' ? '未处理' : item?.status == '2' ? '处理中' : '已处理'
                    }}</i></span
                  >
                </div>
                <div class="btn">
                  <div class="btn1"></div>
                  <div class="btn2" @click="addressClick(item)"></div>
                  <div class="btn3"></div>
                </div>
              </div>
              <noData v-if="incidentData.events.length == 0"></noData>
            </div>
          </div>
        </template>
      </Box>
    </div>
    <div class="for-bot">
      <Box :title="'预警分布'" class="Box1">
        <template v-slot:unit>
          <div class="unit">
            <el-select v-model="value1" placeholder="Select" @change="updateTime1" size="large" style="width: 120px">
              <el-option v-for="item in options1" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
            <el-select v-model="value2" placeholder="Select" @change="updateTime2" size="large" style="width: 72px; margin-left: 10px">
              <el-option v-for="item in options2" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </div>
        </template>
        <template v-slot:content>
          <div class="content">
            <div class="chart" ref="chartRef"></div>
          </div>
        </template>
      </Box>
    </div>
  </div>
  <!-- 预警类型弹窗 -->
  <PopUp :visible="showModal1" :title="enTitle" @update:visible="showModal1 = $event" class="PopUp1">
    <div class="modal-content">
      <!-- 搜索框 -->
      <div class="btn">
        <span v-for="item in activeName" :key="item.value" :class="item.value == currentIndex ? 'current' : ''" @click="questList(item.value)">{{
          item.name
        }}</span>
      </div>
      <Table :columns="columns" :fetchData="incidentFc" :searchParams="searchParams">
        <!-- 自定义列内容 -->
        <template #details="scope">
          <el-button type="primary" @click="viewDetails(scope.row)" style="width: 78px; height: 32px"
            ><el-icon><Document /></el-icon>详情</el-button
          >
        </template>
      </Table>
    </div>
  </PopUp>
  <!-- 预警类型详情弹窗 -->
  <PopUp :visible="showModal2" title="详情" @update:visible="showModal2 = $event" class="PopUp2" style="left: 24%; width: 1014px">
    <div class="modal-content">
      <div class="list">
        <div class="line">
          <span
            >预警类型：<i>{{ incidentList.title || '' }}</i></span
          ><span
            >预警ID：<i>{{ incidentList.id || '' }}</i></span
          >
        </div>
        <div class="line">
          <span
            >桥梁名称：<i>{{ incidentList.bridgeName || '' }}</i></span
          ><span
            >发布地点：<i>{{ incidentList.location || '' }}</i></span
          >
        </div>
        <div class="line">
          <span
            >经纬度：<i>{{ incidentList.lon }},{{ incidentList.lat }}</i></span
          >
          <span
            >预警时间：<i>{{ incidentList.publishTime || '' }}</i></span
          >
        </div>
        <div class="line">
          <span
            >预警内容：<i>{{ incidentList.content || '' }}</i></span
          >
          <span></span>
        </div>
      </div>
      <div class="video">
        <img class="video-box" v-if="incidentList.fileUrl[0]" :src="incidentList.fileUrl[0]" alt="" />
        <noData class="video-box" v-else text="暂无图片"></noData>
      </div>
    </div>
  </PopUp>
</template>

<script setup lang="ts">
import Box from '@/components/Box/index.vue';
import { ref, onMounted, onBeforeUnmount } from 'vue';
import * as echarts from 'echarts';
import txImg from '@/assets/forewarning/tx.png';
import dwImg from '@/assets/forewarning/dw.png';
import { getCountByTitle, getLast24Hours, getForewarnCount, getSpecialEvents, getBridgeWarning } from '@/api/bridge/forewarning';
import { getCameraMonitorPointList } from '@/api/bridge/monitor';
import noData from '@/components/noData/index.vue';
import { setViewToCoordinates, getPointById, updatePointCoordinates, addPoint, clearTypeFeatures } from '@/utils/mapMethods';
import PopUp from '@/components/PopUp/index.vue';
import Table from '@/components/Table/index.vue';
import policeImg from '@/assets/conduct/police.png';

//预警类型
const countByTitle = ref({
  details: [],
  total: 0
});
const getCountByTitleData = async () => {
  const res = await getCountByTitle({});
  if (res.code === 200) {
    // console.log(res.data);
    countByTitle.value = res.data;
  }
};

const enTitle = ref('预警类型');
//预警类型点击
const warningType = (item) => {
  showModal1.value = true;
  enTitle.value = '预警类型';
  if (item == 'All') {
    searchParams.value.title = 'All';
  } else {
    searchParams.value.title = item.title;
  }
};

//预警类型弹窗列表
const showModal1 = ref(false);
const columns = ref([
  {
    prop: 'bridgeName',
    label: '桥梁名称',
    align: 'center'
  },
  {
    prop: 'title',
    label: '预警类型',
    align: 'center'
  },
  {
    prop: 'location',
    label: '发生地点',
    align: 'center'
  },
  {
    prop: 'publishTime',
    label: '发生时间',
    align: 'center'
  },
  {
    'prop': 'details',
    'label': '操作',
    'slot': 'details', // 使用插槽自定义操作按钮（如"详情"链接）
    width: 120
  }
]);
const activeName = ref([
  {
    name: '未处理',
    value: 1
  },
  {
    name: '处理中',
    value: 2
  },
  {
    name: '已处理',
    value: 3
  }
]);
const currentIndex = ref(1);
//条件查询
const questList = (status) => {
  currentIndex.value = status;
  searchParams.value.status = status;
};
const searchParams = ref({
  status: 1,
  title: ''
});
//获取预警类型列表数据
const incidentFc = async (params: {}) => {
  try {
    const res = await getBridgeWarning(params);
    if (res.code == 200) {
      return {
        data: res.data.list || [],
        total: res.data.total || 0
      };
    }
  } catch (error) {
    console.error('获取预警类型列表数据失败:', error);
    ElMessage.error('获取数据失败');
    return {
      data: [],
      total: 0
    };
  }
};
const incidentList = ref<any>({});
const viewDetails = (item) => {
  showModal2.value = true;
  incidentList.value = {
    ...item,
    fileUrl: JSON.parse(item.fileUrl)
  };
  // console.log(incidentList.value);
};
//预警类型详情弹窗
const showModal2 = ref(false);

//预警统计数据
const value = ref('全部');
const options = [
  {
    value: '全部',
    label: '全部'
  },
  {
    value: '交通事故预警',
    label: '交通事故预警'
  },
  {
    value: '环境污染预警',
    label: '环境污染预警'
  },
  {
    value: '地质灾害预警',
    label: '地质灾害预警'
  },
  {
    value: '公共卫生灾害预警',
    label: '公共卫生灾害预警'
  },
  {
    value: '气象灾害预警',
    label: '气象灾害预警'
  },
  {
    value: '应急事件',
    label: '应急事件'
  }
];
const customColors = ref([
  { count: 0, location: '舟岱大桥', percentage: 0 },
  { count: 0, location: '金塘大桥', percentage: 0 },
  { count: 0, location: '鱼山大桥', percentage: 0 },
  { count: 0, location: '西堠门大桥', percentage: 0 }
]);
const getForewarnCountFc = async (params: {}) => {
  const res = await getForewarnCount(params);
  if (res.code == 200) {
    customColors.value = res.data.map((item) => {
      return {
        percentage: ((item.count / res.data[0].count) * 100).toFixed(2),
        ...item
      };
    });
  }
};
//类型切换
const updateData = (value) => {
  // console.log(value);
  if (value == '全部') {
    getForewarnCountFc({});
  } else {
    getForewarnCountFc({ warnType: value });
  }
};

//预警列表数据
const incidentData = ref<any>({
  events: [],
  statusStats: {},
  totalCount: 0
});
//预警地址点击
const addressClick = (item) => {
  if (item.lat && item.lon) {
    setViewToCoordinates([item.lon, item.lat]);
  }
};

//根据预警状态更改颜色
const warningState = (state) => {
  // 1 未处理 2 处理中 3 已处理
  if (state == '3') {
    return 'current0';
  } else if (state == '1') {
    return 'current1';
  } else {
    return 'current2';
  }
};
//预警列表
const clickListFc = () => {
  showModal1.value = true;
  enTitle.value = '预警列表';
  searchParams.value.title = 'All';
};
const getSpecialEventsFc = async () => {
  const res = await getSpecialEvents({});
  if (res.code == 200) {
    // console.log(res);
    incidentData.value.events = res.data.events.map((item) => {
      const pointId = `point_warning${item.id}`;
      const fileUrl = JSON.parse(item.fileUrl);
      // 弹窗内容
      const popupContent = `
          <div class="popup-title" style="min-width:700px;min-height:60px;display:flex;justify-content: space-between;align-items: center;background: linear-gradient(141deg, rgba(40, 40, 61, 0.85) 0%, rgba(0, 0, 0, 0.57) 44%, rgba(4, 3, 9, 0) 100%);padding:0px 25px;">
        <span style="color:#fff;font-size:25px;font-weight: bold;">${item.title}</span><button class="popup-close" style="font-size:20px">X</button>
      </div>
      <div class="popup-content" style="min-width:700px;min-height:200px;background: linear-gradient(141deg, rgba(40, 40, 61, 0.85) 0%, rgba(0, 0, 0, 0.57) 44%, rgba(4, 3, 9, 0) 100%);padding:25px;border-top: 1px solid #accbff; ">
        <div class="ship-popup" style="width:100%;height:auto;">
              <div class="ship-info" style="width:100%;font-size: 20px;color: #fff;display:flex;justify-content: space-between;margin-bottom:20px;">
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">预警类型: ${item.title || ''}</span>
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">预警ID: ${item.id || ''}</span>
              </div>
              <div class="ship-info" style="width:100%;font-size: 20px;color: #fff;display:flex;justify-content: space-between;margin-bottom:20px;">
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">桥梁名称: ${item.bridgeName || ''}</span>
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">发布地点: ${item.location || ''}</span>
              </div>
              <div class="ship-info" style="width:100%;font-size: 20px;color: #fff;display:flex;justify-content: space-between;margin-bottom:20px;">
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">经纬度: ${item.lon},${item.lat}</span>
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">预警时间: ${item.publishTime || ''}</span>
              </div>
              <div class="ship-info" style="width:100%;font-size: 20px;color: #fff;display:flex;justify-content: space-between;margin-bottom:20px;">
                <span style="display:inline-block;width:100%;overflow: hidden;text-overflow: ellipsis;white-space: wrap;">预警内容: ${item.content || ''}</span>
              </div>
              <div class="ship-info" style="font-size: 20px;color: #fff;display:flex;margin-bottom:20px;">
                ${
                  fileUrl && fileUrl[0]
                    ? `<img style="width:300px;height:100px;object-fit:cover;" src="${fileUrl[0]}" alt="预警图片" />`
                    : `<div style="width:300px;height:100px;background:#2f3039;display:flex;justify-content:center;align-items:center;color:#fff;">暂无图片</div>`
                }
              </div>
            </div>
 </div>
          `;

      //判断有没有
      const existingPoint = getPointById(pointId);
      if (existingPoint) {
        // 更新点位坐标
        updatePointCoordinates(existingPoint, [item.lon, item.lat]);
      } else {
        // 添加新的点位
        addPoint([item.lon, item.lat], policeImg, 1, popupContent, pointId);
      }
      return {
        ...item,
        fileUrl
      };
    });
    incidentData.value.statusStats = res.data.statusStats;
    incidentData.value.totalCount = res.data.totalCount;
  }
};

// 图表引用
const statisticsRef = ref<HTMLElement | null>(null);
const chartRef = ref<HTMLElement | null>(null);

// 图表实例
let statisticsEcharts: echarts.ECharts | null = null;
let chartEcharts: echarts.ECharts | null = null;

// 监听窗口大小变化，调整图表大小
const handleResize = () => {
  statisticsEcharts?.resize();
  chartEcharts?.resize();
};

//安全指数风险
const riskFc = (num) => {
  if (num < 1.5) {
    return 'jdfx';
  } else if (num >= 1.5 && num < 2.5) {
    return 'dfx';
  } else if (num >= 2.5 && num < 3.5) {
    return 'zfx';
  } else {
    return 'gfx';
  }
};

//预警分布 - 使用新的监控点接口
const initChartEcharts = async (data?: {}) => {
  // 获取监控点数据
  const monitorRes = await getCameraMonitorPointList();
  let bridgeData = [];

  if (monitorRes.code === 200) {
    // 将监控点数据转换为图表数据格式
    bridgeData = monitorRes.data.map((point, index) => ({
      key: point.name,
      value1: point.status === '1' ? 1 : 0, // 在线状态
      value2: point.latitude ? 1 : 0, // 是否有位置信息
      value3: point.mgtcentername === '舟山管理中心' ? 1 : 0 // 是否为舟山管理中心
    }));
  }

  if (chartRef.value) {
    chartEcharts = echarts.init(chartRef.value);
    // 生成 X 轴数据
    const xAxisData = [];
    bridgeData.forEach((bridge) => {
      xAxisData.push(`${bridge.key}`);
    });

    // 生成数据
    const arrowData1 = xAxisData.map((name, index) => {
      return {
        name: name,
        value: [index, bridgeData[index].value1]
      };
    });
    const arrowData2 = xAxisData.map((name, index) => {
      return {
        name: name,
        value: [index, bridgeData[index].value2]
      };
    });
    const arrowData3 = xAxisData.map((name, index) => {
      return {
        name: name,
        value: [index, bridgeData[index].value3]
      };
    });

    // 渲染箭头
    const arrowSize = 20;
    const renderArrow = function (param, api) {
      const point = api.coord([api.value(0), api.value(1)]);
      return {
        type: 'image',
        style: {
          image: dwImg,
          x: -arrowSize / 2,
          y: -arrowSize / 2,
          width: arrowSize,
          height: arrowSize
        },
        position: point
      };
    };

    const option = {
      title: {
        text: '',
        left: 'center',
        textStyle: {
          fontSize: 18,
          fontWeight: 'bold',
          color: '#333'
        }
      },
      tooltip: {
        trigger: 'item',
        backgroundColor: '#23334e',
        textStyle: {
          color: '#fff'
        },
        formatter: function (params) {
          return `桥段: ${params.name}<br>${params.seriesName}: ${params.value[1]}`;
        }
      },
      legend: {
        data: ['监控数量', '广播数量', '报警数量'],
        textStyle: {
          color: '#fff'
        },
        top: 10,
        right: 0
      },
      xAxis: {
        type: 'category',
        data: xAxisData,
        axisLabel: {
          fontSize: 20,
          color: '#fff'
        },
        axisTick: {
          alignWithLabel: true
        },
        axisLine: {
          lineStyle: {
            color: '#999'
          }
        }
      },
      yAxis: {
        axisLabel: {
          fontSize: 20,
          show: false
        }
      },
      grid: {
        top: 40,
        right: 0,
        left: 0
      },
      dataZoom: [
        {
          type: 'slider',
          xAxisIndex: 0,
          start: 0,
          end: 5,
          bottom: 20,
          height: 20,
          minSpan: 5,
          backgroundColor: '#254d89',
          borderColor: '#ddd',
          fillerColor: '#d2e7fb',
          handleStyle: {
            color: '#5470C6'
          },
          textStyle: {
            color: '#fff'
          }
        },
        {
          type: 'inside',
          xAxisIndex: 0
        }
      ],
      series: [
        {
          name: '监控数量',
          type: 'scatter',
          renderItem: renderArrow,
          data: arrowData1,
          z: 10
        },
        {
          name: '广播数量',
          type: 'scatter',
          renderItem: renderArrow,
          data: arrowData2,
          z: 9
        },
        {
          name: '报警数量',
          type: 'scatter',
          renderItem: renderArrow,
          data: arrowData3,
          z: 8,
          itemStyle: {
            color: '#ff0000'
          }
        }
      ]
    };
    chartEcharts.setOption(option);
  }
};
const value1 = ref('All');
const options1 = [
  {
    value: 'All',
    label: '全部'
  },
  {
    value: '1',
    label: '新江南大桥'
  },
  {
    value: '2',
    label: '官山大桥'
  },
  {
    value: '3',
    label: '秀山大桥'
  },
  {
    value: '4',
    label: '金塘大桥'
  },
  {
    value: '5',
    label: '西堠门大桥'
  },
  {
    value: '6',
    label: '桃天门大桥'
  }
];
const value2 = ref(1);
const options2 = [
  {
    value: 1,
    label: '日'
  },
  {
    value: 2,
    label: '月'
  },
  {
    value: 3,
    label: '年'
  }
];
//更改桥名
const updateTime1 = (value) => {
  initChartEcharts({ bridgeName: value, time: value2.value });
};
//更改时间
const updateTime2 = (value) => {
  initChartEcharts({ bridgeName: value1.value, time: value });
};

//预警分时图表
const value3 = ref(3);
const options3 = [
  {
    value: 1,
    label: '日'
  },
  {
    value: 2,
    label: '月'
  },
  {
    value: 3,
    label: '年'
  }
];
//更改时间
const updateTime3 = (value) => {
  initStatisticsEcharts({ time: value });
};
//预警分时
const initStatisticsEcharts = async (params?: {}) => {
  const res = await getLast24Hours(params);
  let xData = [];
  let yData = [];
  if (res.code == 200) {
    // console.log(res);
    res.data.data.forEach((item) => {
      xData.push(item.xAxis);
      yData.push(item.series);
    });
  }
  if (statisticsRef.value) {
    statisticsEcharts = echarts.init(statisticsRef.value);
    const option = {
      title: {
        text: '件数',
        right: 30,
        textStyle: {
          color: '#86868d',
          fontSize: 20
        }
      },
      grid: {
        top: 40,
        right: 0,
        bottom: 0,
        left: 20,
        containLabel: true,
        show: true,
        borderWidth: 0 // 不显示外边框
      },
      xAxis: {
        type: 'category',
        data: xData || ['00:00', '03:00', '06:00', '09:00', '12:00', '15:00', '18:00', '21:00'],
        axisLabel: {
          color: '#86868d',
          fontSize: 20
        },
        axisLine: {
          lineStyle: {
            color: '#86868d'
          }
        },
        splitLine: {
          show: false
        }
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          color: '#86868d',
          fontSize: 20
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: '#86868d'
          }
        },
        splitLine: {
          show: false // 不显示 Y 轴的网格线
        }
      },
      tooltip: {
        trigger: 'axis',
        backgroundColor: '#23334e', // 背景色
        textStyle: {
          color: '#fff' // 字体颜色
        }
      },
      series: [
        {
          data: yData || [0, 0, 0, 0, 0, 0, 0, 0],
          type: 'line',
          smooth: true,
          areaStyle: {
            color: 'rgba(128, 194, 242, 0.2)' // 设置面积颜色
          },
          lineStyle: {
            color: '#85cafc' // 设置折线颜色
          }
        }
      ]
    };
    statisticsEcharts.setOption(option);
  }
};

onMounted(() => {
  initStatisticsEcharts({ time: value3.value });
  initChartEcharts({ bridgeName: 'All', time: 1 });
  getCountByTitleData();
  getForewarnCountFc({});
  getSpecialEventsFc();
  window.addEventListener('resize', handleResize);
});

// 组件卸载前移除事件监听
onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize);
  // 销毁图表实例
  statisticsEcharts?.dispose();
  statisticsEcharts = null;
  chartEcharts?.dispose();
  chartEcharts = null;
  clearTypeFeatures('point_warning'); //清除预警点位
});
</script>

<style lang="scss" scoped>
.forewarning {
  height: 1377px;
  width: 1442px;
  position: absolute;
  bottom: 44px;
  right: 60px;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  z-index: 1;
  .for-left {
    height: 1037px;
    width: 48%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .Box1 {
      position: relative;
      width: 100%;
      height: 544px;
      .content {
        position: relative;
        flex: 1;
        overflow: hidden;
        .mid-box {
          width: 301px;
          height: 301px;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          background: url('@/assets/forewarning/lxBg.png') no-repeat center;
          background-size: 100% 100%;
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          span:nth-child(1) {
            color: #fff;
            font-weight: bold;
            font-size: 50px;
            font-family: 'Microsoft YaHei';
          }
          span:nth-child(2) {
            color: #fff;
            font-size: 25px;
            font-family: 'Microsoft YaHei';
          }
        }
        .box1 {
          width: 164px;
          height: 164px;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          position: absolute;
          left: 14%;
          top: 0;
          background: url('@/assets/forewarning/qmsg.png') no-repeat center;
          background-size: 100% 100%;
          span:nth-child(1) {
            color: #fff;
            font-weight: bold;
            font-size: 50px;
            font-family: 'Microsoft YaHei';
            margin-top: 60px;
          }
          span:nth-child(2) {
            color: #fff;
            font-size: 25px;
            font-family: 'Microsoft YaHei';
            margin-top: 20px;
          }
        }
        .box2 {
          width: 164px;
          height: 164px;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          background: url('@/assets/flowRate/h.png') no-repeat center;
          background-size: 100% 100%;
          position: absolute;
          left: 55%;
          transform: translateX(-50%);
          top: -8%;
          span:nth-child(1) {
            color: #fff;
            font-weight: bold;
            font-size: 50px;
            font-family: 'Microsoft YaHei';
            margin-top: 60px;
          }
          span:nth-child(2) {
            color: #fff;
            font-size: 25px;
            font-family: 'Microsoft YaHei';
            margin-top: 20px;
          }
        }
        .box3 {
          width: 164px;
          height: 164px;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          position: absolute;
          right: 6%;
          top: 10%;
          background: url('@/assets/forewarning/dllyj.png') no-repeat center;
          background-size: 100% 100%;
          span:nth-child(1) {
            color: #fff;
            font-weight: bold;
            font-size: 50px;
            font-family: 'Microsoft YaHei';
            margin-top: 60px;
          }
          span:nth-child(2) {
            color: #fff;
            font-size: 25px;
            font-family: 'Microsoft YaHei';
            margin-top: 20px;
          }
        }
        .box4 {
          width: 164px;
          height: 164px;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          position: absolute;
          bottom: 35px;
          left: 10%;
          background: url('@/assets/forewarning/yq.png') no-repeat;
          span:nth-child(1) {
            color: #fff;
            font-weight: bold;
            font-size: 50px;
            font-family: 'Microsoft YaHei';
            margin-top: 70px;
          }
          span:nth-child(2) {
            color: #fff;
            font-size: 25px;
            font-family: 'Microsoft YaHei';
            margin-top: 20px;
          }
        }
        .box5 {
          width: 164px;
          height: 164px;
          display: flex;
          flex-direction: column;
          align-items: center;
          position: absolute;
          bottom: 2%;
          left: 50%;
          transform: translateX(-50%);
          background: url('@/assets/flowRate/jh.png') no-repeat center;
          background-size: 100% 100%;
          span:nth-child(1) {
            color: #fff;
            font-weight: bold;
            font-size: 50px;
            font-family: 'Microsoft YaHei';
            margin-top: 50px;
          }
          span:nth-child(2) {
            color: #fff;
            font-size: 25px;
            font-family: 'Microsoft YaHei';
            margin-top: 20px;
          }
        }
        .box6 {
          width: 164px;
          height: 164px;
          display: flex;
          flex-direction: column;
          align-items: center;
          position: absolute;
          bottom: 10%;
          right: 10%;
          background: url('@/assets/forewarning/gd.png') no-repeat center;
          background-size: 100% 100%;
          span:nth-child(1) {
            color: #fff;
            font-weight: bold;
            font-size: 50px;
            font-family: 'Microsoft YaHei';
            margin-top: 50px;
          }
          span:nth-child(2) {
            color: #fff;
            font-size: 25px;
            font-family: 'Microsoft YaHei';
            margin-top: 20px;
          }
        }
      }
    }
    .Box2 {
      position: relative;
      width: 100%;
      height: 462px;
      .content {
        flex: 1;
        overflow: hidden;
        position: relative;
        margin-top: 20px;
        .head {
          width: 100%;
          height: 116px;
          display: flex;
          justify-content: space-around;
          .head-box {
            width: 24%;
            height: 100%;
            background: #292d3c;
            display: flex;
            flex-direction: column;
            justify-content: space-around;
            align-items: center;
            border-radius: 4px;
            span:nth-child(1) {
              font-size: 20px;
              font-family: 'Microsoft YaHei';
              color: #fff;
            }
            span:nth-child(2) {
              font-size: 22px;
              font-family: 'Microsoft YaHei';
              color: #fff;
            }
          }
        }
        .bot {
          width: 100%;
          height: calc(100% - 116px);
          position: relative;
          .bot-tit {
            display: flex;
            flex-direction: column;
            position: absolute;
            left: 40%;
            top: 10%;
            span {
              color: #fff;
            }
            span:nth-child(1) {
              font-size: 26px;
              font-family: 'Microsoft YaHei';
            }
            span:nth-child(2) {
              font-size: 70px;
              font-weight: bold;
              font-family: 'Microsoft YaHei';
            }
          }
          .bot-list1 {
            color: #fff;
            font-family: 'Microsoft YaHei';
            font-size: 26px;
            position: absolute;
            bottom: 40px;
          }
          .bot-list2 {
            color: #fff;
            font-family: 'Microsoft YaHei';
            font-size: 26px;
            position: absolute;
            bottom: 35px;
            left: 28%;
          }
          .bot-list3 {
            color: #fff;
            font-family: 'Microsoft YaHei';
            font-size: 26px;
            position: absolute;
            bottom: 35px;
            left: 54%;
          }
          .bot-list4 {
            color: #fff;
            font-family: 'Microsoft YaHei';
            font-size: 26px;
            position: absolute;
            bottom: 40px;
            right: 55px;
          }
        }
        .jdfx {
          background: url('@/assets/forewarning/jdfx.png') no-repeat;
          background-position: 0% 50%;
        }
        .dfx {
          background: url('@/assets/forewarning/dfx.png') no-repeat;
          background-position: 0% 50%;
        }
        .zfx {
          background: url('@/assets/forewarning/zfx.png') no-repeat;
          background-position: 0% 50%;
        }
        .gfx {
          background: url('@/assets/forewarning/gfx.png') no-repeat;
          background-position: 0% 50%;
        }
      }
    }
  }
  .for-right {
    width: 48%;
    height: 1037px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .Box1 {
      height: 260px;
      width: 100%;
      position: relative;
      .el-select {
        background: #35373c;
        .el-select--large .el-select__wrapper {
          background: #35373c;
        }
        ::v-deep span {
          color: #fff;
          font-family: 'Microsoft YaHei';
          font-size: 17px;
          font-weight: bold;
        }
      }
      .statistics1 {
        flex: 1;
        width: 100%;
      }
    }
    .Box2 {
      position: relative;
      width: 100%;
      height: 260px;
      .unit {
        position: absolute;
        top: 24px;
        right: 0;
        width: 70%;
        height: 42px;
        display: flex;
        align-items: center;
        // .el-dropdown {
        //   background: #35373c;
        //   margin-left: 20px;
        //   .el-button {
        //     background: #35373c;
        //   }
        //   ::v-deep .el-button > span {
        //     color: #fff;
        //     font-family: 'Microsoft YaHei';
        //     font-size: 17px;
        //     font-weight: bold;
        //   }
        // }
        .el-select {
          background: #35373c;
          .el-select--large .el-select__wrapper {
            background: #35373c;
          }
          ::v-deep span {
            color: #fff;
            font-family: 'Microsoft YaHei';
            font-size: 17px;
            font-weight: bold;
          }
        }
      }
      .line {
        flex: 1;
        overflow-y: scroll;
        padding-right: 20px;
        margin-top: 20px;
        .box {
          height: 66px;
          width: 100%;
          margin-bottom: 20px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          background: #232531;
          span:nth-child(1) {
            font-size: 26px;
            font-weight: bold;
            color: #fff;
            font-family: 'Microsoft YaHei';
            margin-left: 20px;
          }
          span:nth-child(2) {
            font-size: 21px;
            color: #9d9ea0;
            font-family: 'Microsoft YaHei';
          }
          span:last-child {
            font-size: 21px;
            font-family: 'Microsoft YaHei';
            color: #9d9ea0;
            i {
              font-style: normal;
              color: #fff;
              font-size: 25px;
              font-weight: bold;
            }
          }
          .el-progress {
            width: 335px;
            height: 12px;
          }
        }
      }
      /* 自定义滚动条宽度和高度 */
      ::-webkit-scrollbar {
        width: 0px;
        height: 0px;
      }
    }
    .Box3 {
      position: relative;
      width: 100%;
      height: 462px;
      .unit {
        position: absolute;
        top: 24px;
        right: 0;
        width: 70%;
        height: 42px;
        display: flex;
        align-items: center;
        .btn {
          height: 33px;
          width: 33px;
          border-radius: 4px;
          margin-left: 20px;
          cursor: pointer;
          background: #424447 url('@/assets/forewarning/sz.png') no-repeat center;
        }
      }
      .content {
        flex: 1;
        overflow: hidden;
        .head {
          height: 118px;
          padding: 10px;
          width: 100%;
          display: flex;
          flex-wrap: wrap;
          justify-content: space-between;
          align-items: center;
          margin-top: 10px;
          background: #0e0f15;
          border-radius: 4px;
          .head-lin {
            color: #fff;
            display: flex;
            justify-content: space-around;
            align-items: center;
            width: 48%;
            background: #222730;
            border-radius: 4px;
            .img1 {
              width: 16px;
              height: 16px;
              background: url('@/assets/forewarning/qb.png') no-repeat center;
              background-size: 100% 100%;
            }
            .img2 {
              width: 16px;
              height: 16px;
              background: url('@/assets/forewarning/czz.png') no-repeat center;
              background-size: 100% 100%;
            }
            .img3 {
              width: 16px;
              height: 16px;
              background: url('@/assets/forewarning/ycl.png') no-repeat center;
              background-size: 100% 100%;
            }
            .img4 {
              width: 16px;
              height: 16px;
              background: url('@/assets/forewarning/wcl.png') no-repeat center;
              background-size: 100% 100%;
            }
            span:nth-child(1) {
              margin-left: 20px;
            }
            span:nth-child(2) {
              font-size: 22px;
              font-family: 'Microsoft YaHei';
              flex: 1;
              margin-left: 20px;
            }
            span:nth-child(3) {
              font-size: 17px;
              font-family: 'Microsoft YaHei';
              font-weight: bold;
              margin-right: 40px;
            }
          }
        }
        .list {
          overflow-y: scroll;
          width: 100%;
          height: calc(100% - 128px);
          .list-box {
            width: 100%;
            height: 150px;
            display: flex;
            align-items: center;
            background: #1a1e23;
            margin-bottom: 20px;
            .img {
              width: 211px;
              height: 125px;
              margin-left: 10px;
              img {
                width: 100%;
                height: 100%;
              }
            }
            .con {
              display: flex;
              height: 100%;
              flex: 1;
              flex-direction: column;
              justify-content: space-around;
              margin-left: 10px;
              span {
                color: #fff;
                display: inline-block;
                font-size: 17px;
                font-family: 'Microsoft YaHei';
                i {
                  font-style: normal;
                  color: red;
                }
                .current0 {
                  color: #00ff6d;
                }
                .current1 {
                  color: red;
                }
                .current2 {
                  color: #efff00;
                }
              }
            }
            .btn {
              width: 33px;
              height: 125px;
              display: flex;
              flex-direction: column;
              justify-content: space-between;
              margin-right: 20px;
              > div {
                width: 33px;
                height: 33px;
                border-radius: 4px;
                cursor: pointer;
              }
              .btn1 {
                background: #2a2b39 url('@/assets/forewarning/fxdj.png') no-repeat center;
              }
              .btn2 {
                background: #2a2b39 url('@/assets/forewarning/dw.png') no-repeat center;
              }
              .btn3 {
                background: #2a2b39 url('@/assets/forewarning/sxt.png') no-repeat center;
              }
            }
          }
        }
        /* 自定义滚动条宽度和高度 */
        ::-webkit-scrollbar {
          width: 0px;
          height: 0px;
        }
      }
    }
  }
  .for-bot {
    width: 100%;
    height: 300px;
    margin-top: 40px;
    .Box1 {
      position: relative;
      width: 100%;
      height: 300px;
      .unit {
        position: absolute;
        top: 24px;
        right: 0;
        width: 85%;
        height: 42px;
        display: flex;
        align-items: center;
        .el-select {
          background: #35373c;
          .el-select--large .el-select__wrapper {
            background: #35373c;
          }
          ::v-deep span {
            color: #fff;
            font-family: 'Microsoft YaHei';
            font-size: 17px;
            font-weight: bold;
          }
        }
      }
      .content {
        flex: 1;
        width: 100%;
        position: relative;
        overflow: hidden;
        .chart {
          width: 100%;
          height: 100%;
          background: #172940;
        }
      }
    }
  }
}
.PopUp1 {
  .modal-content {
    width: 100%;
    height: 100%;
    .btn {
      height: 36px;
      width: 100%;
      display: flex;
      padding: 0 20px;
      span {
        display: inline-block;
        width: 165px;
        height: 100%;
        border-radius: 6px;
        color: #b6b6bb;
        font-size: 20px;
        font-family: 'Microsoft YaHei';
        background: #50505a;
        cursor: pointer;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-right: 20px;
      }
      .current {
        background: #416194;
        color: #fff;
      }
    }
  }
}
.PopUp2 {
  .modal-content {
    width: 100%;
    display: flex;
    flex-direction: column;
    padding: 20px;
    .list {
      width: 100%;
      height: 310px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      .line {
        width: 100%;
        height: 40px;
        span {
          display: inline-block;
          width: 50%;
          height: 100%;
          color: #fff;
          font-size: 30px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          font-family: 'Microsoft YaHei';
          i {
            font-style: normal;
            color: #accbff;
          }
        }
      }
    }
    .video {
      width: 100%;
      height: 258px;
      display: flex;
      justify-content: space-between;
      margin: 64px 0 0px 0;
      .video-box {
        width: 48%;
        height: 100%;
        // background: url('@/assets/secialVehicle/videoBg.png') no-repeat center;
        // background-size: 100% 100%;
      }
    }
  }
}
</style>
