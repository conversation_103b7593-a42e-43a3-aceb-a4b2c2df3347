<template>
  <div class="special-vehicle">
    <div class="vehicle-right">
      <div class="vehicle-box1">
        <Box :title="'流量分析'" class="Box1">
          <template v-slot:content>
            <div class="FlowEcharts" ref="FlowEchartsRef"></div>
          </template>
        </Box>
        <Box :title="'车队管理'" class="Box2">
          <template #content>
            <div class="content">
              <div class="con1">
                <div class="con1-box1" @click="getVehicleLocationFc">
                  <span>定位关联</span>
                </div>
                <div class="con1-box2" @click="historicalRoute('ghlx')">
                  <span>车辆行驶状态监控</span>
                </div>
              </div>
              <div class="con2">
                <div class="con2-box1" @click="showModal2 = true">
                  <span>车辆基本信息</span>
                </div>
                <div class="con2-box2" @click="historicalRoute('lsgj')">
                  <span>历史轨迹</span>
                </div>
              </div>
            </div>
          </template>
        </Box>
      </div>
      <div class="vehicle-box2">
        <Box :title="'任务计划'" class="Box1">
          <template #content>
            <div class="content">
              <div class="btn">
                <span
                  v-for="(item, index) in activeName"
                  :key="index"
                  :class="index == currentIndex ? 'current' : ''"
                  @click="questList(index, item.value)"
                  >{{ item.name }}</span
                >
                <span @click="questList(currentIndex, activeName[currentIndex].value)"
                  ><el-icon><RefreshRight /></el-icon>刷新</span
                >
              </div>
              <div class="con">
                <div v-loading="loading" element-loading-text="加载中" element-loading-background="rgba(0, 0, 0, 0.5)">
                  <div
                    class="con-box"
                    v-for="(item, index) in projectData"
                    @click="checkedFc(item)"
                    :class="item.vehicleId == currentIndex3 ? 'active' : ''"
                    :key="index"
                  >
                    <div class="tit">
                      <div class="bt">{{ item.hazardousMaterial }}-运输任务</div>
                      <div class="img">
                        <span></span>
                        <span></span>
                      </div>
                    </div>
                    <span class="td">车队名称：{{ item.teamName }}</span>
                    <span class="td">所属单位：{{ item.owner }}</span>
                    <span class="td">出发时间：{{ item.startTime }}</span>
                    <!-- <span class="td">关联单位：{{ item.owner }}</span> -->
                    <span class="td">相关人员：{{ item.driverName }}</span>
                    <span class="td">行驶路线：{{ item.startLocation }}->{{ item.destinationLocation }}</span>
                    <span class="td">目的地：{{ item.destinationLocation }}</span>
                  </div>
                  <NoData v-if="projectData?.length == 0"></NoData>
                </div>
              </div>
            </div>
          </template>
        </Box>
      </div>
    </div>
    <!-- 历史轨迹弹窗 -->
    <PopUp :visible="showModal1" :title="titleName" @update:visible="showModal1 = $event" class="PopUp1">
      <div class="modal-content">
        <div class="map-container" id="historyMapContainer"></div>
        <div class="name">
          <span>车牌号</span>
          <span>定位时间</span>
          <span>当前位置</span>
          <span>行驶方向</span>
          <span>车辆状态</span>
        </div>
        <div class="list">
          <div class="line" v-for="(item, index) in guardshipList" :key="index">
            <span class="line-span" :title="item.licensePlate">{{ item.licensePlate }}</span>
            <span class="line-span" :title="item.createTime">{{ item.createTime }}</span>
            <span class="line-span" :title="item.lon + ',' + item.lat">{{ item.lon }},{{ item.lat }}</span>
            <span class="line-span" :title="direction">{{ direction }}</span>
            <span class="line-span" :title="item.status">{{ item.status }}</span>
          </div>
          <NoData v-if="guardshipList.length == 0"></NoData>
        </div>
      </div>
    </PopUp>
    <!-- 车辆基本信息弹窗 -->
    <PopUp :visible="showModal2" title="车辆基本信息" @update:visible="showModal2 = $event" class="PopUp2">
      <div class="modal-content">
        <!-- 搜索框 -->
        <div class="search-bar">
          <span>车牌号：</span>
          <el-input v-model="licensePlate" placeholder="车牌号..." clearable style="width: 300px" />
          <span>所属公司：</span>
          <el-input v-model="owner" placeholder="所属公司..." clearable style="width: 300px" />
          <el-button type="primary" @click="handleSearch" style="width: 100px; margin-left: 27px"
            ><el-icon><Search /></el-icon>搜索</el-button
          >
          <el-button type="primary" @click="handlebayonet" style="width: 100px; margin-left: 10px">卡口</el-button>
          <el-button type="primary" @click="handleAccident" style="width: 100px; margin-left: 10px">事故</el-button>
        </div>
        <Table :columns="columns" :fetchData="fetchData" :searchParams="searchParams">
          <!-- 自定义列内容 -->
          <!-- 序号列插槽 -->
          <template #index="scope">
            <span>{{ scope.index + 1 }}</span>
          </template>
          <template #details="scope">
            <el-button type="primary" @click="handleDetails(scope.row)" style="width: 78px; height: 32px"
              ><el-icon><Document /></el-icon>详情</el-button
            >
          </template>
        </Table>
      </div>
    </PopUp>
    <!-- 车辆基本信息弹窗详情 -->
    <PopUp :visible="showModal3" title="详情" @update:visible="showModal3 = $event" class="PopUp3" style="left: 18%">
      <div class="modal-content">
        <div class="video"></div>
        <div class="list">
          <div class="line">
            <span
              >车牌号：<i>{{ detailsList?.licensePlate || '' }}</i></span
            ><span>车辆识别代号：<i>{{}}</i></span>
          </div>
          <div class="line">
            <span>住址：<i>{{}}</i></span
            ><span>通信卡号：<i>{{}}</i></span>
          </div>
          <div class="line">
            <span>使用性质：<i>{{}}</i></span
            ><span>车辆状态：<i>{{}}</i></span>
          </div>
          <div class="line">
            <span>GPS状态：<i>{{}}</i></span
            ><span>注册日期：<i>{{}}</i></span>
          </div>
          <div class="line">
            <span
              >车辆类型：<i>{{ detailsList?.vehicleType || '' }}</i></span
            ><span>发动机号码：<i>{{}}</i></span>
          </div>
          <div class="line">
            <span
              >发证日期：<i>{{ detailsList?.permitExpiry || '' }}</i></span
            ><span>GPS设备号：<i>{{}}</i></span>
          </div>
          <div class="line">
            <span
              >品牌型号：<i>{{ detailsList?.vehicleBrand || '' }}{{ detailsList?.vehicleModel || '' }}</i></span
            ><span
              >所属人：<i>{{ detailsList?.driverName || '' }}</i></span
            >
          </div>
        </div>
      </div>
    </PopUp>

    <!-- 事故信息弹窗 -->
    <PopUp :visible="showAccidentModal" title="危化品事故信息" @update:visible="showAccidentModal = $event" class="PopUp4" style=" width: 3014px;">
      <div class="modal-content">
        <div class="con-box2">
          <div class="tit">
            <span></span>
            <span>危化品事故信息列表</span>
          </div>
          <div class="name">
            <span>事件大类名称</span>
            <span>事件小类名称</span>
            <span>事件状态</span>
            <span>开始桩号(米)</span>
            <span>结束桩号(米)</span>
            <span>开始参考位置</span>
            <span>结束参考位置</span>
            <span>方向名称</span>
            <span>路段名称</span>
            <span>开始时间</span>
            <span>结束时间</span>
            <span>现场情况</span>
          </div>
          <div class="list">
            <div class="line" v-for="(item, index) in accidentData" :key="index">
              <span class="line-span">{{ item.eventTypeName }}</span>
              <span class="line-span">{{ item.subEventTypeName }}</span>
              <span class="line-span">{{ item.status == 1 ? '未处理' : item.status == 2 ? '处理中' : item.status == 3 ? '已处理' : '未知' }}</span>
              <span class="line-span">{{ item.beginMilestone }}</span>
              <span class="line-span">{{ item.endMilestone }}</span>
              <span class="line-span">{{ item.beginRefLocation }}</span>
              <span class="line-span">{{ item.endRefLocation }}</span>
              <span class="line-span">{{ item.directionName }}</span>
              <span class="line-span">{{ item.roadName }}</span>
              <span class="line-span">{{ item.beginTime }}</span>
              <span class="line-span">{{ item.endTime }}</span>
              <span class="line-span">{{ item.situation }}</span>
            </div>
            <noData v-if="accidentData.length == 0"></noData>
          </div>
          <!-- 分页组件 -->
          <div class="pagination" style="margin-top: 20px; text-align: center;">
            <CustomPagination
              v-model:page="accidentCurrentPage"
              v-model:limit="accidentPageSize"
              :page-sizes="[10, 20, 50, 100]"
              :total="accidentTotal"
              @pagination="handleAccidentPaginationChange"
            />
          </div>
        </div>
      </div>
    </PopUp>

    <!-- 卡口设备弹窗 -->
    <PopUp :visible="showModal4" title="卡口" @update:visible="showModal4 = $event" class="PopUp4" style="left: 74%; width: 1014px">
      <div class="modal-content">
        <div class="con-box2">
          <div class="tit">
            <span></span>
            <span>卡口设备</span>
          </div>
          <div class="name">
            <span>设备名称</span>
            <span>安装位置</span>
            <span>设备状态</span>
            <span>联系人</span>
            <span>手机号</span>
          </div>
          <div class="list">
            <div class="line" v-for="(item, index) in lebayonetData" :key="index">
              <span class="line-span">{{ item.deviceName }}</span>
              <span class="line-span">{{ item.installLocation }}</span>
              <span class="line-span">{{ item.status == 1 ? '正常' : item.status == 2 ? '故障' : '维护中' }}</span>
              <span class="line-span">{{ item.responsiblePerson }}</span>
              <span class="line-span">{{ item.contactPhone }}</span>
            </div>
            <noData v-if="lebayonetData.length == 0"></noData>
          </div>
        </div>
        <div class="con-box2">
          <div class="tit">
            <span></span>
            <span>卡口监测数据</span>
          </div>
          <div class="name">
            <span>车牌号</span>
            <span>行驶速度</span>
            <span>是否超载</span>
            <span>是否违章</span>
            <span>违章类型</span>
          </div>
          <div class="list">
            <div class="line" v-for="(item, index) in monitorList" :key="index">
              <span class="line-span">{{ item.licensePlate }}</span>
              <span class="line-span">{{ item.speed }}km/h</span>
              <span class="line-span">{{ item.isOverload == 0 ? '否' : '是' }}</span>
              <span class="line-span">{{ item.isViolation == 0 ? '否' : '是' }}</span>
              <span class="line-span">{{ item.violationType }}</span>
            </div>
            <noData v-if="monitorList.length == 0"></noData>
          </div>
        </div>
        <div class="con-box2">
          <div class="tit">
            <span></span>
            <span>卡口同行数据</span>
          </div>
          <div class="name">
            <span>方向</span>
            <span>地区名称</span>
            <span>起点</span>
            <span>终点</span>
            <span>断面流量</span>
          </div>
          <div class="list">
            <div class="line" v-for="(item, index) in flowData" :key="index">
              <span class="line-span" :title="item.direction">{{ item.direction }}</span>
              <span class="line-span" :title="item.region_name">{{ item.region_name }}</span>
              <span class="line-span" :title="item.hub_code01">{{ item.hub_code01 }}</span>
              <span class="line-span" :title="item.hub_code02">{{ item.hub_code02 }}</span>
              <span class="line-span" :title="item.vehicle_cls">{{ item.vehicle_cls }}</span>
            </div>
            <noData v-if="flowData.length == 0"></noData>
          </div>
        </div>
      </div>
    </PopUp>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, watch } from 'vue';
import * as echarts from 'echarts';
import Box from '@/components/Box/index.vue';
import PopUp from '@/components/PopUp/index.vue';
import { getVehicleList, getTransport, getGpsByVehicleId, getmonitorList, getlistByTransportId, getFlowList, getEventInfoPage } from '@/api/bridge/secialVehicle';
import CustomPagination from '@/components/CustomPagination/index.vue';
import NoData from '@/components/noData/index.vue';
import {
  initMap,
  addPoint,
  clearTypeFeatures,
  clearTrack,
  connectTrackPoints,
  connectTrackPointsWithArrows,
  getPointById,
  updatePointCoordinates,
  setViewToCoordinates
} from '@/utils/mapMethods';
import { getPointDevice } from '@/api/bridge/home';
import carGPS from '@/assets/svg/car_danger.svg';
import { getVehicleGpsTracking } from '@/api/bridge/point';
import cheImg from '@/assets/home/<USER>';

// 图表引用
const FlowEchartsRef = ref<HTMLElement | null>(null);

// 图表实例
let FlowEcharts: echarts.ECharts | null = null;

// 监听窗口大小变化，调整图表大小
const handleResize = () => {
  FlowEcharts?.resize();
};

const titleName = ref('历史轨迹');

// 地图相关
let map: any = null;

// 初始化地图
const initHistoryMap = () => {
  return new Promise((resolve) => {
    setTimeout(() => {
      try {
        // 每次打开都重新初始化地图
        map = initMap('historyMapContainer');
        // 确保地图容器有正确的尺寸
        map.updateSize();
        resolve(map);
      } catch (error) {
        resolve(null);
      }
    }, 100);
  });
};

//获取车辆定位数据
const isShow = ref(false);
const getVehicleLocationFc = async () => {
  if (!currentIndex3.value) return ElMessage.warning('请选择一个任务计划！');
  isShow.value = !isShow.value;
  if (isShow.value) {
    try {
      // 获取车辆GPS定位信息
      const gpsRes: any = await getVehicleGpsTracking({ vehicleId: currentIndex3.value });

      if (gpsRes.code === 200) {
        // 处理GPS定位数据
        if (gpsRes.data && gpsRes.data.length > 0) {
          gpsRes.data.forEach((item: any) => {
            // 验证坐标数据
            if (!item.lon || !item.lat) return;
            const pointId = `vehicle_${item.vehicleId}`;
            // 检查是否已存在该点位
            const existingPoint = getPointById(pointId);
            if (existingPoint) {
              // 更新点位坐标
              updatePointCoordinates(existingPoint, [item.lon, item.lat]);
            } else {
              // 创建弹窗内容
              const popupContent = `
          <div class="popup-title" style="min-width:460px;min-height:60px;display:flex;justify-content: space-between;align-items: center;background: linear-gradient(141deg, rgba(40, 40, 61, 0.85) 0%, rgba(0, 0, 0, 0.57) 44%, rgba(4, 3, 9, 0) 100%);padding:0px 25px;">
            <div style="display: flex; align-items: center; gap: 10px;">
              <span style="color:#fff;font-size:25px;font-weight: bold;">${item.licensePlate || '未知车辆'}</span>
               <img src="${cheImg}" style="width:30px;height:30px;" />
            </div>
            <button class="popup-close" style="font-size:20px">X</button>
          </div>
          <div class="popup-content" style="width:auto;min-height:200px;background: linear-gradient(141deg, rgba(40, 40, 61, 0.85) 0%, rgba(0, 0, 0, 0.57) 44%, rgba(4, 3, 9, 0) 100%);padding:25px;border-top: 1px solid #accbff; ">
            <div class="ship-popup" style="min-width:700px;width:auto;height:auto;">
              <div class="ship-info" style="font-size: 20px;color: #fff;display:flex;justify-content: space-between;margin-bottom:20px;">
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">驾驶员姓名: ${item.driverName}</span>
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">车辆类型:${item.vehicleType} </span>
              </div>
              <div class="ship-info" style="font-size: 20px;color: #fff;display:flex;justify-content: space-between;margin-bottom:20px;">
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">所属者名称: ${item.owner}</span>
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">所属者联系方式: ${item.ownerContact}</span>
              </div>
              <div class="ship-info" style="font-size: 20px;color: #fff;display:flex;justify-content: space-between;margin-bottom:20px;">
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">驾驶证编号:${item.driverLicense} </span>
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">状态:${item.status} </span>
              </div>
              <div class="ship-info" style="font-size: 20px;color: #fff;display:flex;justify-content: space-between;margin-bottom:20px;">
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">总里程:${item.mileage}公里 </span>
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">行驶方向:${item.direction} </span>
              </div>
               <div class="ship-info" style="font-size: 20px;color: #fff;display:flex;justify-content: space-between;margin-bottom:20px;">
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">当前速度:${item.speed} km/h </span>
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">核载重量:${item.loadcapacity} </span>
              </div>
            </div>
          </div>
        `;
              // 添加点位
              addPoint([item.lon, item.lat], carGPS, 0.5, popupContent, pointId, item.licensePlate, item);
              setViewToCoordinates([item.lon, item.lat]); //视野聚集过去
            }
          });
        }
      } else {
        console.log('接口返回失败:', { gpsRes });
      }
    } catch (error) {
      console.error('获取车辆定位数据失败:', error);
    }
  } else {
    // 清除地图上车辆点位
    clearTypeFeatures('vehicle_');
  }
};

//卡口监测数据
const monitorList = ref([]);
const handmonitor = async () => {
  const res = await getmonitorList({});
  if (res.code == 200) {
    monitorList.value = res.rows;
  }
};

//卡口流量数据
const flowData = ref([]);
const getFlowListFc = async () => {
  const res = await getFlowList({});
  if (res.code == 200) {
    flowData.value = res.data;
  }
};

//卡口设备
const showModal4 = ref(false);

//事故信息
const showAccidentModal = ref(false);
const accidentData = ref([]);
const accidentCurrentPage = ref(1);
const accidentPageSize = ref(10);
const accidentTotal = ref(0);

const handleAccident = async () => {
  showAccidentModal.value = true;
  await getAccidentData();
};

const getAccidentData = async () => {
  try {
    const res = await getEventInfoPage({
      page: accidentCurrentPage.value,
      limit: accidentPageSize.value
    });
    if (res.code == 200) {
      accidentData.value = res.data.records;
      accidentTotal.value = res.data.total;
    }
  } catch (error) {
    console.error('获取事故数据失败:', error);
    ElMessage.error('获取事故数据失败');
  }
};

const handleAccidentPaginationChange = () => {
  getAccidentData();
};
const lebayonetData = ref([]);
const handlebayonet = async () => {
  showModal4.value = true;
  const res = await getPointDevice({});
  handmonitor();
  getFlowListFc();
  if (res.code == 200) {
    lebayonetData.value = res.rows;
  }
};

//车队管理弹窗状态
//历史轨迹
const showModal1 = ref(false);
const guardshipList = ref([]);
const currentTransportId = ref(-1);

//任务计划选中
const checkedFc = (data) => {
  currentIndex3.value = data.vehicleId;
  currentTransportId.value = data.id;
  direction.value = data.startLocation + '-' + data.destinationLocation;
};
const direction = ref(''); //行驶方向
//历史轨迹
const historicalRoute = async (type) => {
  if (!currentIndex3.value) return ElMessage.warning('请选择一个任务计划！');
  if (showModal1.value) {
    //避免重复
    return;
  }
  showModal1.value = true;
  try {
    await initHistoryMap();
    let res;
    if (type == 'lsgj') {
      titleName.value = '历史轨迹';
      res = await getGpsByVehicleId({ vehicleId: currentIndex3.value });
    } else {
      res = await getlistByTransportId({ vehicleId: currentIndex3.value, transportId: currentTransportId.value });
      titleName.value = '规划路线';
    }
    // 清除之前的轨迹
    clearTrack(`vehicle_${currentIndex3.value}`);
    // 存储点坐标的数组
    const points: [number, number][] = [];
    if (map) {
      // 添加历史轨迹点
      if (res.code == 200) {
        guardshipList.value = res.data;
        res.data.forEach((item) => {
          addPoint(
            [item.lon, item.lat],
            carGPS, // 使用图标
            0.5,
            '',
            `history_${item.id}`,
            titleName.value == '历史轨迹' ? item.gpsTimeStr : item.formattedRouteTime
          );
          // 收集点坐标
          points.push([item.lon, item.lat]);
        });
        // 连接点坐标，添加箭头指向
        connectTrackPointsWithArrows(`vehicle_${currentIndex3.value}`, points, '#00ff00', 3, '#ff0000', 12);
        setViewToCoordinates([res.data[0].lon, res.data[0].lat]);
      }
    }
  } catch (error) {
    console.error('初始化地图失败:', error);
  }
};

//车辆基本信息
const showModal2 = ref(false);
//车牌号输入
const licensePlate = ref('');
//所属公司输入
const owner = ref('');
//搜索
const handleSearch = () => {
  searchParams.value.owner = owner.value;
  searchParams.value.licensePlate = licensePlate.value;
};
const columns = ref([
  { prop: 'index', label: '序号', width: '80', slot: 'index' },
  { prop: 'licensePlate', label: '车牌号码' },
  { prop: 'hazardousPermit', label: '危化品种类' },
  { prop: 'loadcapacity', label: '载重量' },
  { prop: 'owner', label: '所属公司' },
  { prop: 'driverName', label: '驾驶员姓名' },
  { prop: 'ownerContact', label: '联系方式' },
  { prop: 'details', label: '操作', slot: 'details' }
]);
// 搜索条件
const searchParams = ref({
  owner: '',
  licensePlate: ''
});
// 获取车辆基本信息数据
const fetchData = async (params: {}) => {
  try {
    const res = await getVehicleList(params);
    if (res.code == 200) {
      // console.log(res);
      return {
        data: res.data.rows || [],
        total: res.data.total || 0
      };
    }
  } catch (error) {
    console.error('获取车辆数据失败:', error);
    ElMessage.error('获取数据失败');
    return {
      data: [],
      total: 0
    };
  }
};
//车辆基本信息详情
const detailsList = ref<any>([]);
const showModal3 = ref(false);
const handleDetails = (item) => {
  // console.log(item);
  showModal3.value = true;
  detailsList.value = item;
};

// 任务计划条件切换
const activeName = ref([
  {
    name: '全部',
    value: 'All'
  },
  {
    name: '未开始',
    value: '1'
  },
  {
    name: '进行中',
    value: '2'
  },
  {
    name: '已完成',
    value: '3'
  },
  {
    name: '已取消',
    value: '4'
  }
]);
const currentIndex = ref(0);
const currentIndex3 = ref(''); //任务计划选择
// 添加 loading 状态
const loading = ref(false);
const questList = async (index, item) => {
  currentIndex.value = index;
  loading.value = true;
  try {
    await getTransportFc({ transportStatus: item });
  } finally {
    loading.value = false;
  }
};

//流量分析图表
const initFlowEcharts = () => {
  if (FlowEchartsRef.value) {
    FlowEcharts = echarts.init(FlowEchartsRef.value);
    const option = {
      xAxis: {
        type: 'category',
        data: ['00:00', '03:00', '06:00', '09:00', '12:00', '15:00', '18:00', '21:00'],
        axisLabel: {
          color: '#86868d',
          fontSize: 20
        },
        axisLine: {
          lineStyle: {
            color: '#86868d'
          }
        },
        splitLine: {
          show: false
        }
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          color: '#86868d',
          fontSize: 20
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: '#86868d'
          }
        },
        splitLine: {
          show: false // 不显示 Y 轴的网格线
        }
      },
      series: [
        {
          name: '总流量',
          data: [1100, 1400, 900, 1000, 800, 1400, 1000, 1700],
          type: 'line',
          smooth: true,
          lineStyle: {
            color: '#ffffff',
            width: 2
          },
          areaStyle: {
            color: 'rgba(255, 255, 255, 0.2)' // 设置面积颜色
          },
          itemStyle: {
            color: '#a6d8fc'
          }
        },
        {
          name: '危化品流量',
          data: [80, 60, 90, 95, 20, 100, 70, 50],
          type: 'line',
          smooth: true,
          lineStyle: {
            color: '#fe4453',
            width: 2
          },
          areaStyle: {
            color: 'rgba(254, 68, 83, 0.2)' // 设置面积颜色
          },
          itemStyle: {
            color: '#a6d8fc'
          }
        },
        {
          name: '普通车流量',
          data: [400, 800, 300, 600, 700, 1000, 300, 1400],
          type: 'line',
          smooth: true,
          areaStyle: {
            color: 'rgba(133, 202, 252, 0.2)' // 设置面积颜色
          },
          lineStyle: {
            color: '#85cafc',
            width: 2
          },
          itemStyle: {
            color: '#35bfb0'
          }
        }
      ],
      legend: {
        right: 60,
        data: ['总流量', '危化品流量', '普通车流量'],
        textStyle: {
          color: '#fff',
          fontSize: 20
        }
      },
      tooltip: {
        trigger: 'axis',
        backgroundColor: '#23334e', // 背景色
        textStyle: {
          color: '#fff' // 字体颜色
        }
      },
      grid: {
        top: 40,
        right: 0,
        bottom: 0,
        left: 0,
        containLabel: true,
        show: true,
        borderWidth: 0 // 不显示外边框
      }
    };
    FlowEcharts.setOption(option);
  }
};

//任务计划
const getTransportFc = async (params: {}) => {
  const res = await getTransport(params);
  if (res.code == 200) {
    // console.log(res);
    projectData.value = res.data || [];
  }
};
const projectData = ref([
  // {
  //   title: 'xxxxxxxxx',
  //   label1: 'xxxxx',
  //   label2: '舟山市定海区...',
  //   label3: '2024-12-16 14:19',
  //   label4: '舟山市定海区...',
  //   label5: 'xxx、xxx、xxx、xxx、xxx...',
  //   label6: '从xxx出发，沿xxx高速，经过x...',
  //   label7: 'xxxx'
  // },
  // {
  //   title: 'xxxxxxxxx',
  //   label1: 'xxxxx',
  //   label2: '舟山市定海区...',
  //   label3: '2024-12-16 14:19',
  //   label4: '舟山市定海区...',
  //   label5: 'xxx、xxx、xxx、xxx、xxx...',
  //   label6: '从xxx出发，沿xxx高速，经过x...',
  //   label7: 'xxxx'
  // },
  // {
  //   title: 'xxxxxxxxx',
  //   label1: 'xxxxx',
  //   label2: '舟山市定海区...',
  //   label3: '2024-12-16 14:19',
  //   label4: '舟山市定海区...',
  //   label5: 'xxx、xxx、xxx、xxx、xxx...',
  //   label6: '从xxx出发，沿xxx高速，经过x...',
  //   label7: 'xxxx'
  // },
  // {
  //   title: 'xxxxxxxxx',
  //   label1: 'xxxxx',
  //   label2: '舟山市定海区...',
  //   label3: '2024-12-16 14:19',
  //   label4: '舟山市定海区...',
  //   label5: 'xxx、xxx、xxx、xxx、xxx...',
  //   label6: '从xxx出发，沿xxx高速，经过x...',
  //   label7: 'xxxx'
  // }
]);

// 监听弹窗关闭
watch(showModal1, (newVal) => {
  if (!newVal) {
    // 弹窗关闭时清除地图上的历史轨迹要素
    clearTypeFeatures('history_');
    // 销毁地图实例
    if (map) {
      map.setTarget(undefined);
      map = null;
    }
  }
});

// 监听窗口大小变化
onMounted(() => {
  initFlowEcharts();
  questList(0, 'All');
  window.addEventListener('resize', handleResize);
});

// 组件卸载前移除事件监听
onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize);
  // 销毁图表实例
  FlowEcharts?.dispose();
  FlowEcharts = null;
  // 清除地图上车辆点位
  clearTypeFeatures('vehicle_');
});
</script>

<style scoped lang="scss">
.special-vehicle {
  .vehicle-right {
    height: 1377px;
    width: 1442px;
    position: absolute;
    bottom: 44px;
    right: 60px;
    display: flex;
    justify-content: space-between;
    z-index: 1;
    .vehicle-box1 {
      height: 100%;
      width: 48%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      .Box1 {
        width: 100%;
        height: 560px;
        position: relative;
        .FlowEcharts {
          flex: 1;
          width: 100%;
        }
      }
      .Box2 {
        width: 100%;
        height: 782px;
        position: relative;
        .content {
          flex: 1;
          width: 100%;
          .con1 {
            width: 100%;
            height: 315px;
            display: flex;
            justify-content: space-between;
            margin-top: 28px;
            .con1-box1 {
              height: 100%;
              width: 242px;
              background: url('@/assets/secialVehicle/dwgl.png') no-repeat center;
              background-size: 100%;
              cursor: pointer;
              position: relative;
              span {
                color: #fff;
                font-size: 22px;
                font-family: 'Microsoft YaHei';
                font-weight: bold;
                position: absolute;
                bottom: 12px;
                left: 50%;
                transform: translateX(-50%);
              }
            }
            .con1-box2 {
              height: 100%;
              width: 393px;
              background: url('@/assets/secialVehicle/clxsztjk.png') no-repeat center;
              background-size: 100%;
              cursor: pointer;
              position: relative;
              span {
                color: #fff;
                font-size: 22px;
                font-family: 'Microsoft YaHei';
                font-weight: bold;
                position: absolute;
                bottom: 12px;
                left: 50%;
                transform: translateX(-50%);
              }
            }
          }
          .con2 {
            width: 100%;
            height: 315px;
            margin-top: 17px;
            display: flex;
            justify-content: space-between;
            .con2-box1 {
              height: 100%;
              width: 393px;
              background: url('@/assets/secialVehicle/cljbxx.png') no-repeat center;
              background-size: 100%;
              cursor: pointer;
              position: relative;
              span {
                color: #fff;
                font-size: 22px;
                font-family: 'Microsoft YaHei';
                font-weight: bold;
                position: absolute;
                bottom: 12px;
                left: 50%;
                transform: translateX(-50%);
              }
            }
            .con2-box2 {
              height: 100%;
              width: 242px;
              background: url('@/assets/secialVehicle/lsgj.png') no-repeat center;
              background-size: 100%;
              cursor: pointer;
              position: relative;
              span {
                color: #fff;
                font-size: 22px;
                font-family: 'Microsoft YaHei';
                font-weight: bold;
                position: absolute;
                bottom: 12px;
                left: 50%;
                transform: translateX(-50%);
              }
            }
          }
        }
      }
    }
    .vehicle-box2 {
      height: 100%;
      width: 48%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      .Box1 {
        width: 100%;
        height: 100%;
        position: relative;
        .content {
          flex: 1;
          width: 100%;
          overflow: hidden;
          .btn {
            height: 36px;
            width: 100%;
            display: flex;
            justify-content: space-between;
            margin-top: 16px;
            span {
              display: inline-block;
              width: 16%;
              height: 100%;
              border-radius: 6px;
              color: #b6b6bb;
              font-size: 20px;
              font-family: 'Microsoft YaHei';
              background: #50505a;
              cursor: pointer;
              display: flex;
              justify-content: center;
              align-items: center;
            }
            .current {
              background: #416194;
              color: #fff;
            }
          }
          .con {
            width: 100%;
            height: calc(100% - 70px);
            margin-top: 18px;
            overflow-y: scroll;
            .con-box {
              height: 326px;
              width: 100%;
              background: #3c3c44;
              border-radius: 4px;
              margin-bottom: 24px;
              padding: 20px 42px 22px 47px;
              display: flex;
              flex-direction: column;
              justify-content: space-between;
              .tit {
                width: 100%;
                height: 35px;
                display: flex;
                justify-content: space-between;
                .bt {
                  color: #fff;
                  font-size: 22px;
                  font-weight: bold;
                  font-family: 'Microsoft YaHei';
                }
                .img {
                  width: 90px;
                  height: 100%;
                  display: flex;
                  justify-content: space-between;
                  align-items: center;
                  span:nth-child(1) {
                    display: inline-block;
                    width: 35px;
                    height: 35px;
                    background: url('@/assets/secialVehicle/bjdj1.png') no-repeat center;
                    background-size: 100%;
                    cursor: pointer;
                  }
                  span:nth-child(2) {
                    display: inline-block;
                    width: 30px;
                    height: 30px;
                    background: url('@/assets/secialVehicle/sc.png') no-repeat center;
                    background-size: 100%;
                    cursor: pointer;
                  }
                }
              }
              .td {
                display: inline-block;
                width: 100%;
                color: #fff;
                font-size: 20px;
                font-family: 'Microsoft YaHei';
                text-overflow: ellipsis;
                white-space: nowrap;
                overflow: hidden;
              }
            }
            .active {
              background: #27354c;
            }
          }
          /* 隐藏垂直滚动条 */
          .con::-webkit-scrollbar {
            width: 0;
          }
        }
      }
    }
  }
  .PopUp1 {
    .modal-content {
      .map-container {
        width: 100%;
        height: 550px;
        background: #1a1a1a;
        position: relative;
        z-index: 1;
        :deep(.dark-layer) {
          filter: contrast(102%) brightness(93%) saturate(103%) sepia(65%) grayscale(22%) invert(100%);
        }
      }
      .name {
        background: #262c3f;
        width: 100%;
        height: 62px;
        display: flex;
        color: #fff;
        margin-top: 20px;
        span {
          font-size: 19px;
          font-weight: bold;
          font-family: 'Microsoft YaHei';
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          width: 20%;
        }
      }
      .list {
        width: 100%;
        height: 248px;
        margin-top: 10px;
        overflow-y: scroll;
        .line {
          display: flex;
          width: 100%;
          height: 62px;

          .line-span {
            color: #fff;
            font-size: 17px;
            font-family: 'Microsoft YaHei';
            height: 100%;
            width: 20%;
            line-height: 62px;
            text-align: center;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
      }
      /* 隐藏垂直滚动条 */
      .list::-webkit-scrollbar {
        width: 0;
      }
    }
  }
  .PopUp2 {
    .search-bar {
      padding: 0 20px;
      width: 100%;
      display: flex;
      align-items: center;
      span {
        color: #fff;
        font-family: 'Microsoft YaHei';
        font-size: 24px;
      }
      :deep(.el-input__wrapper) {
        background: #474750;
      }
    }
  }
  .PopUp3 {
    width: 1588px !important;
    .modal-content {
      width: 100%;
      display: flex;
      .video {
        width: 622px;
        height: 422px;
        background: url('@/assets/secialVehicle/videoBg.png') no-repeat center;
      }
      .list {
        width: calc(100% - 622px);
        height: 422px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        padding-left: 58px;
        .line {
          width: 100%;
          height: 40px;
          span {
            display: inline-block;
            width: 50%;
            height: 100%;
            color: #fff;
            font-size: 30px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            font-family: 'Microsoft YaHei';
            i {
              font-style: normal;
              color: #accbff;
            }
          }
        }
      }
    }
  }
  .PopUp4 {
    .modal-content {
      .con-box2 {
        margin-top: 36px;
        .tit {
          span {
            font-size: var(--font-size-module-title); // 使用模块标题变量 30px
            font-family: 'Microsoft YaHei';
            color: #fff;
          }
          span:nth-child(1) {
            display: inline-block;
            width: 43px;
            height: 22px;
            background: url('@/assets/home/<USER>') no-repeat center;
            background-size: 100% 100%;
            margin-right: 12px;
          }
        }
      }
      .con-box2 {
        .name {
          background: #262c3f;
          width: 100%;
          height: 62px;
          display: flex;
          color: #fff;
          margin-top: 20px;
          span {
            font-size: var(--font-size-content); // 使用统一变量 25px
            font-weight: bold;
            font-family: 'Microsoft YaHei';
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            width: 20%;
          }
        }
        .list {
          width: 100%;
          height: 200px;
          margin-top: 10px;
          overflow-y: scroll;
          .line {
            display: flex;
            width: 100%;
            height: 62px;

            .line-span {
              color: #fff;
              font-size: var(--font-size-content); // 使用统一变量 25px
              font-family: 'Microsoft YaHei';
              height: 100%;
              width: 20%;
              line-height: 62px;
              text-align: center;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }
        }
        /* 隐藏垂直滚动条 */
        .list::-webkit-scrollbar {
          width: 0;
        }
      }
    }
  }
}
</style>
